<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IO.Ably</name>
    </assembly>
    <members>
        <member name="M:IO.Ably.AblyAuth.RenewToken">
            <summary>
            Renews the current token and calls OnAuthUpdated without blocking until the connection is reestablished.
            </summary>
            <returns>new token if successful.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the new token is not valid.</exception>
        </member>
        <member name="M:IO.Ably.AblyAuth.RequestTokenAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Makes a token request. This will make a token request now, even if the library already
            has a valid token. It would typically be used to issue tokens for use by other clients.
            </summary>
            <param name="tokenParams">The <see cref="T:IO.Ably.TokenRequest"/> data used for the token.</param>
            <param name="authOptions">Extra <see cref="T:IO.Ably.AuthOptions"/> used for creating a token.</param>
            <returns>A valid ably token.</returns>
            <exception cref="T:IO.Ably.AblyException">something went wrong.</exception>
        </member>
        <member name="M:IO.Ably.AblyAuth.AuthorizeAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Ensure valid auth credentials are present. This may rely in an already-known
            and valid token, and will obtain a new token if necessary or explicitly
            requested.
            Authorisation will use the parameters supplied on construction except
            where overridden with the options supplied in the call.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/> custom parameter. Pass null and default token request options will be generated used the options passed when creating the client.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/> custom options.</param>
            <returns>Returns a valid token.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an ably exception representing the server response.</exception>
        </member>
        <member name="M:IO.Ably.AblyAuth.CreateTokenRequestObjectAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Create a signed token request based on known credentials
            and the given token params. This would typically be used if creating
            signed requests for submission by another client.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/>. If null a token request is generated from options passed when the client was created.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/>. If null the default AuthOptions are used.</param>
            <returns>signed token request.</returns>
        </member>
        <member name="T:IO.Ably.AblyException">
            <summary>
            Ably exception wrapper class. It includes error information <see cref="T:IO.Ably.ErrorInfo"/> used by ably.
            All inner exceptions are wrapped in this class. Always check the inner exception property of the caught exception.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyException"/> class.
            </summary>
            <param name="reason">Reason passed to the error info class.</param>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(System.Exception)">
            <summary>
            Creates AblyException. ErrorInfo is automatically generated based on the inner exception message. StatusCode is set to 'ErrorCodes.InternalError'.
            </summary>
            <param name="ex">Original exception to be wrapped.</param>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(System.String,System.Int32)">
            <summary>
            Creates an AblyException and populates ErrorInfo with the supplied parameters.
            </summary>
            <param name="reason">reason.</param>
            <param name="code">error code.</param>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(System.String,System.Int32,System.Nullable{System.Net.HttpStatusCode})">
            <summary>
            Creates AblyException and populates ErrorInfo with the supplied parameters.
            </summary>
            <param name="reason">error reason.</param>
            <param name="code">error code.</param>
            <param name="statusCode">optional, http status code. <see cref="T:System.Net.HttpStatusCode"/>.</param>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(IO.Ably.ErrorInfo)">
            <summary>
            Creates AblyException with supplied error info.
            </summary>
            <param name="info">Error info.</param>
        </member>
        <member name="M:IO.Ably.AblyException.#ctor(IO.Ably.ErrorInfo,System.Exception)">
            <summary>
            Creates AblyException with ErrorInfo and sets the supplied exception as innerException.
            </summary>
            <param name="info">Error info.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="P:IO.Ably.AblyException.ErrorInfo">
            <summary>
            Gets the current error info for the exception.
            </summary>
        </member>
        <member name="T:IO.Ably.AblyInsecureRequestException">
            <summary>
            Ably exception if an action cannot be performed over http.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyInsecureRequestException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyInsecureRequestException"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyInsecureRequestException.#ctor(System.String)">
            <summary>
            Initializes a new AblyInsecureRequestException using the specified 'message'.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:IO.Ably.AblyInsecureRequestException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new AblyInsecureRequestException using the specified 'message' and 'innerException'.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception to wrap.</param>
        </member>
        <member name="T:IO.Ably.AblyRealtime">
            <summary>
            AblyRealtime
            The top-level class for the Ably Realtime library.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyRealtime.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyRealtime"/> class with an ably key.
            </summary>
            <param name="key">String key (obtained from application dashboard).</param>
        </member>
        <member name="M:IO.Ably.AblyRealtime.#ctor(IO.Ably.ClientOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyRealtime"/> class with the given options.
            </summary>
            <param name="options"><see cref="T:IO.Ably.ClientOptions"/>.</param>
        </member>
        <member name="P:IO.Ably.AblyRealtime.RestClient">
            <summary>
            Gets the initialised RestClient.
            </summary>
        </member>
        <member name="P:IO.Ably.AblyRealtime.Auth">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.AblyRealtime.Push">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.AblyRealtime.ClientId">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.AblyRealtime.Channels">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.AblyRealtime.Connection">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.AblyRealtime.Device">
            <summary>
            The local device instance represents the current state of the device in respect of it being a target for push notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyRealtime.StatsAsync">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.StatsAsync(IO.Ably.StatsRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Stats">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Stats(IO.Ably.StatsRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Connect">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Close">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.TimeAsync">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.AblyRealtime.GetCurrentState">
            <summary>
            Debug method to get the full library state.
            Useful when trying to figure out the full state of the library.
            </summary>
            <returns>json object of the full state of the library.</returns>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Dispose">
            <summary>
            Disposes the current instance.
            Once disposed, it closes the connection and the library can't be used again.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyRealtime.Dispose(System.Boolean)">
            <summary>
            Disposes the current instance.
            Once disposed, it closes the connection and the library can't be used again.
            </summary>
            <param name="disposing">Whether the dispose method triggered it directly.</param>
        </member>
        <member name="T:IO.Ably.AblyRest">
            <summary>Client for the Ably rest API.</summary>
        </member>
        <member name="M:IO.Ably.AblyRest.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyRest"/> class using an api key.</summary>
            <param name="apiKey">Full api key.</param>
        </member>
        <member name="M:IO.Ably.AblyRest.#ctor(System.Action{IO.Ably.ClientOptions})">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.AblyRest"/> class.
            Convenience method for initializing the RestClient by passing a Action{ClientOptions}.
            <example>
            var rest = new AblyRest(opt => {
             opt.Key = "fake.key:value";
             opt.ClientId = "123";
            });
            </example>
            </summary>
            <param name="init">Action delegate which receives a empty options object.</param>
        </member>
        <member name="M:IO.Ably.AblyRest.#ctor(IO.Ably.ClientOptions)">
            <summary>
            Initialize the library with a custom set of options.
            </summary>
            <param name="clientOptions">instance of clientOptions.</param>
        </member>
        <member name="P:IO.Ably.AblyRest.Channels">
            <summary>
            A collection of Channels associated with an Ably instance.
            </summary>
        </member>
        <member name="P:IO.Ably.AblyRest.Auth">
            <summary>
            Authentication methods.
            </summary>
        </member>
        <member name="P:IO.Ably.AblyRest.Push">
            <summary>
            Expose Push Admin Rest APIs.
            Rest API documentation: https://ably.com/docs/rest-api#push.
            </summary>
        </member>
        <member name="P:IO.Ably.AblyRest.Device">
            <summary>
            The local device instance represents the current state of the device in respect of it being a target for push notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.AblyRest.InitializeAbly(IO.Ably.Push.IMobileDevice)">
            <summary>Initializes the rest client and validates the passed in options.</summary>
        </member>
        <member name="M:IO.Ably.AblyRest.Request(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},Newtonsoft.Json.Linq.JToken,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Make a generic HTTP request against an endpoint representing a collection
            of some type; this is to provide a forward compatibility path for new APIs.
            </summary>
            <param name="method">http method.</param>
            <param name="path">the path component of the resource URI.</param>
            <param name="requestParams">(optional; may be null): any parameters to send with the request; see API-specific documentation.</param>
            <param name="body">(optional; may be null): an instance of RequestBody. It will be sent as a json object.</param>
            <param name="headers">(optional; may be null): any additional headers to send; see API-specific documentation.</param>
            <returns>a page of results.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.Request(System.Net.Http.HttpMethod,System.String,System.Collections.Generic.Dictionary{System.String,System.String},Newtonsoft.Json.Linq.JToken,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Make a generic HTTP request against an endpoint representing a collection
            of some type; this is to provide a forward compatibility path for new APIs.
            </summary>
            <param name="method">http method.</param>
            <param name="path">the path component of the resource URI.</param>
            <param name="requestParams">(optional; may be null): any parameters to send with the request; see API-specific documentation.</param>
            <param name="body">(optional; may be null): an instance of RequestBody. It will be sent as a json object.</param>
            <param name="headers">(optional; may be null): any additional headers to send; see API-specific documentation.</param>
            <returns>a page of results.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.TimeAsync">
            <summary>Retrieves the ably service time.</summary>
            <returns>server time as DateTimeOffset.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.StatsAsync">
            <summary>
            Retrieves the stats for the application. Passed default <see cref="T:IO.Ably.StatsRequestParams"/> for the request.
            </summary>
            <returns>returns PaginatedResult of Stats.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.StatsAsync(IO.Ably.StatsRequestParams)">
            <summary>
            Retrieves the stats for the application using a more specific stats query. Check <see cref="T:IO.Ably.StatsRequestParams"/> for more information.
            </summary>
            <param name="query">stats query.</param>
            <returns>returns a PaginatedResult of Stats.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.StatsAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Retrieves the stats for the application based on a custom query. It should be used with <see cref="T:IO.Ably.PaginatedRequestParams"/>.
            It is mainly because of the way a PaginatedResource defines its queries. For retrieving Stats with special parameters use <see cref="M:IO.Ably.AblyRest.StatsAsync(IO.Ably.StatsRequestParams)"/>.
            </summary>
            <example>
            var client = new AblyRest("validkey");
            var stats = client..StatsAsync();
            var nextPage = client..StatsAsync(stats.NextQuery);.
            </example>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> and <see cref="T:IO.Ably.StatsRequestParams"/>.</param>
            <returns>returns a PaginatedResult of Stats.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.CanConnectToAbly">
            <summary>
            Makes an Http request to check whether there is connectivity to ably.
            If Option.SkipInternetCheck is set to true, the method always returns 'true'.
            </summary>
            <returns>returns whether there is internet connectivity.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.Stats">
            <summary>
            Sync version of StatsAsync.
            </summary>
            <returns>returns PaginatedResult of Stats.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.Stats(IO.Ably.StatsRequestParams)">
            <summary>
            Sync version of StatsAsync.
            </summary>
            <param name="query">stats <see cref="T:IO.Ably.StatsRequestParams"/>.</param>
            <returns>returns PaginatedResult of Stats.</returns>
        </member>
        <member name="M:IO.Ably.AblyRest.Time">
            <summary>
            Sync method for getting the current server time.
            </summary>
            <returns>DateTimeOffset of the current server time.</returns>
        </member>
        <member name="M:IO.Ably.Agent.DotnetRuntimeIdentifier">
            <summary>
            This returns dotnet platform as per ably-lib mappings defined in agents.json.
            https://github.com/ably/ably-common/blob/main/protocol/agents.json.
            This is required since we are migrating from 'X-Ably-Lib' header (RSC7b) to agent headers (RSC7d).
            Please note that uwp platform is Deprecated and removed as a part of https://github.com/ably/ably-dotnet/pull/1101.
            </summary>
            <returns> Clean Platform Identifier. </returns>
        </member>
        <member name="T:IO.Ably.ApiKey">
            <summary>
            Internal class used to parse ApiKeys. The api key has the following parts {keyName}:{KeySecret}
            The app and key parts form the KeyId.
            </summary>
        </member>
        <member name="P:IO.Ably.ApiKey.KeyName">
            <summary>
            First part of the key is also called the key name.
            </summary>
        </member>
        <member name="P:IO.Ably.ApiKey.KeySecret">
            <summary>
            The second part of the key is called the key secret.
            </summary>
        </member>
        <member name="M:IO.Ably.ApiKey.ToString">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.ApiKey.Parse(System.String)">
            <summary>
            Parses a string to produce a key object.
            </summary>
            <param name="key">a valid ably key.</param>
            <exception cref="T:IO.Ably.AblyException">throws an exception when the key string is invalid.</exception>
            <returns>ApiKey object representing the parsed key.</returns>
        </member>
        <member name="M:IO.Ably.Shared.CustomSerialisers.MessageDataConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Shared.CustomSerialisers.MessageDataConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Shared.CustomSerialisers.MessageDataConverter.CanConvert(System.Type)">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.DefaultLoggerSink">
            <summary>The default logger implementation, that writes to debug output.</summary>
        </member>
        <member name="T:IO.Ably.ChannelModeExtensions">
            <summary>
            Helper methods when dealing with Channel Models.
            </summary>
        </member>
        <member name="T:IO.Ably.IInternalLogger">
            <summary>
            Extends the public 'ILogger' interface with additional internal capabilities.
            </summary>
        </member>
        <member name="M:IO.Ably.IInternalLogger.CreateDisposableLoggingContext(IO.Ably.ILoggerSink)">
            <summary>
            Allows the caller to temporarily install a 'LogEvent' implementation that will be reverted
            when the returned context is disposed.
            </summary>
            <param name="sink">The 'LogEvent' to use until the context is disposed.</param>
            <returns>The returned context should be disposed to revert to the previous 'LogEvent' implementation.</returns>
        </member>
        <member name="T:IO.Ably.ILoggerSink">
            <summary>An interface that actually logs that messages somewhere.</summary>
        </member>
        <member name="M:IO.Ably.ILoggerSink.LogEvent(IO.Ably.LogLevel,System.String)">
            <summary>
            Implement this method to log messages using your current infrastructure.
            </summary>
            <param name="level">the log level of the message.</param>
            <param name="message">the actual message.</param>
        </member>
        <member name="P:IO.Ably.InternalLogger.LogLevel">
            <summary>Maximum level to log.</summary>
            <remarks>E.g. set to LogLevel.Warning to have only errors and warnings in the log.</remarks>
        </member>
        <member name="M:IO.Ably.InternalLogger.Error(System.String,System.Exception)">
            <summary>Log an error message.</summary>
        </member>
        <member name="M:IO.Ably.InternalLogger.Error(System.String,System.Object[])">
            <summary>Log an error message.</summary>
        </member>
        <member name="M:IO.Ably.InternalLogger.Warning(System.String,System.Object[])">
            <summary>Log a warning message.</summary>
        </member>
        <member name="M:IO.Ably.InternalLogger.Debug(System.String,System.Object[])">
            <summary>Log a debug message.</summary>
        </member>
        <member name="M:IO.Ably.InternalLogger.GetExceptionDetails(System.Exception)">
            <summary>Produce long multiline string with the details about the exception, including inner exceptions, if any.</summary>
        </member>
        <member name="T:IO.Ably.MessageEncoders.DecodingContext">
            <summary>
            Class used to provide context between different encoders.
            </summary>
        </member>
        <member name="P:IO.Ably.MessageEncoders.DecodingContext.ChannelOptions">
            <summary>
            The channel options for the current channel.
            </summary>
        </member>
        <member name="M:IO.Ably.MessageEncoders.DecodingContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.MessageEncoders.DecodingContext"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.MessageEncoders.DecodingContext.#ctor(IO.Ably.ChannelOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.MessageEncoders.DecodingContext"/> class.
            </summary>
            <param name="options">Channel options used for the encode / decode operations.</param>
        </member>
        <member name="T:IO.Ably.MessageEncoders.ChannelOptionsExtensions">
            <summary>
            Helpers methods for <see cref="T:IO.Ably.MessageEncoders.DecodingContext"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.MessageEncoders.ChannelOptionsExtensions.ToDecodingContext(IO.Ably.ChannelOptions)">
            <summary>
            Creates a new <see cref="T:IO.Ably.MessageEncoders.DecodingContext"/> from the provided <see cref="T:IO.Ably.ChannelOptions"/>.
            </summary>
            <param name="options">the <see cref="T:IO.Ably.ChannelOptions"/> used in the new context.</param>
            <returns><see cref="T:IO.Ably.MessageEncoders.DecodingContext"/> created with passed Channel options.</returns>
        </member>
        <member name="T:IO.Ably.MessageEncoders.VcDiffErrorInfo">
            <summary>
            Specific error class that is used to distinguish a critical VcDiff error
            and a normal decoding error which could be caused by a bad encoding string or
            a bad cipher.
            </summary>
        </member>
        <member name="T:IO.Ably.PaginatedRequestParams">
            <summary>
                Data request query used for querying stats and history
                It makes it easier to pass parameters to the ably service by encapsulating the query string parameters passed.
            </summary>
        </member>
        <member name="F:IO.Ably.PaginatedRequestParams.Empty">
            <summary>
            Instance of empty paginated request params.
            </summary>
        </member>
        <member name="M:IO.Ably.PaginatedRequestParams.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.PaginatedRequestParams"/> class.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.HttpMethod">
            <summary>
            HttpMethod that will be used for the query.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Headers">
            <summary>
            Additional headers passed.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Body">
            <summary>
            Body of the query.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Path">
            <summary>
            The path component of the resource URI.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Start">
            <summary>
                Start of the query interval as UTC Date.
                Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.End">
            <summary>
                End of the query interval as UTC Date
                Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Limit">
            <summary>
                The number of the results returned by the server. If there are more result the NextQuery on the PaginatedResource
                will be populated
                Default: 100.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.Direction">
            <summary>
                Query directions. It determines the order in which results are returned. <see cref="T:IO.Ably.QueryDirection" />.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.ExtraParameters">
            <summary>
                Used mainly when parsing query strings to hold extra parameters that need to be passed back to the service.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.QueryString">
            <summary>
                If the datasource was created by parsing a query string it can be accessed from here.
                It is mainly used for debugging purposes of Current and NextQueries of PaginatedResources.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedRequestParams.IsEmpty">
            <summary>
                Gets a value indicating whether <see cref="P:IO.Ably.PaginatedRequestParams.QueryString" /> is empty (or null).
            </summary>
        </member>
        <member name="M:IO.Ably.PaginatedRequestParams.Equals(IO.Ably.PaginatedRequestParams)">
            <summary>
            Implements PaginatedRequestParams specific equals.
            </summary>
            <param name="other">the other object we are comparing to.</param>
            <returns>true or false.</returns>
        </member>
        <member name="M:IO.Ably.PaginatedRequestParams.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.PaginatedRequestParams.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.HistoryRequestParams">
            <inheritdoc />
            <summary>
                Data request query used for querying history.
                Functionally identical to <see cref="T:IO.Ably.PaginatedRequestParams"/> and present for backwards compatibility with 0.8 release.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.DeviceFormFactor">
            <summary>
            Describes possible device form factors.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Phone">
            <summary>
            Phone.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Tablet">
            <summary>
            Tablet.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Desktop">
            <summary>
            Desktop.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Tv">
            <summary>
            Tv.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Watch">
            <summary>
            Watch.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Car">
            <summary>
            Car.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Embedded">
            <summary>
            Embedded.
            </summary>
        </member>
        <member name="F:IO.Ably.Push.DeviceFormFactor.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.PushCallbacks">
            <summary>
            Class used to setup Push state change callbacks.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushCallbacks.DeactivatedCallback">
            <summary>
            Action called when the device has been deactivated for push notifications.
            Error info is either `null` or holds the current error.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushCallbacks.ActivatedCallback">
            <summary>
            Action called when the device has been activated for push notifications.
            Error info is either `null` or holds the current error.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushCallbacks.SyncRegistrationFailedCallback">
            <summary>
            Action called when a registration sync failed. The reason it failed will be in the error info.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.PushChannel">
            <summary>
            PushChannel is a convenience class that facilitates push related actions,
            like subscribing and unsubscribing to push notification,
            narrowed to a specific channel.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.#ctor(System.String,IO.Ably.AblyRest)">
            <summary>
            Create a new instance of PushChannel.
            </summary>
            <param name="channelName">Name of the channel.</param>
            <param name="rest"><see cref="T:IO.Ably.AblyRest"/> client.</param>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.SubscribeDevice">
            <summary>
            Subscribes the current device to receive push notifications from the current channel.
            </summary>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device is not activated. Please make sure Push.Activate() has completed.</exception>
            <returns>Async operation.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.UnsubscribeDevice">
            <summary>
            Unsubscribes the current device from receiving push notification from the current channel.
            </summary>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device is not activated. Please make sure Push.Activate() has completed.</exception>
            <returns>Async operation.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.SubscribeClient">
            <summary>
            Subscribes the current clientId to receive push notifications from the current channel.
            </summary>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device is not activated. Please make sure Push.Activate() has completed.</exception>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device does not have a clientId assigned.</exception>
            <returns>Async operation.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.UnsubscribeClient">
            <summary>
            Unsubscribes the current client from receiving push notification from the current channel.
            </summary>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device is not activated. Please make sure Push.Activate() has completed.</exception>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device does not have a clientId assigned.</exception>
            <returns>Async operation.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannel.ListSubscriptions(IO.Ably.Push.ListSubscriptionsRequest)">
            <summary>
            Returns a list of <see cref="T:IO.Ably.Push.PushChannelSubscription"/> for the current channel, filtered by the currently set clientId and deviceId.
            </summary>
            <param name="listRequest">A custom <see cref="T:IO.Ably.Push.ListSubscriptionsRequest"/> can be passed. However the current clientId and deviceId are always used. It's mainly to provide further parameters.
            If more flexibility is required, please use AblyRest.Push.Admin.ChannelSubscriptions.ListAsync.</param>
            <returns>A PaginatedResult of PushChannelSubscription.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an exception if the local device is not activated. Please make sure Push.Activate() has completed.</exception>
        </member>
        <member name="T:IO.Ably.Push.PushChannelSubscription">
            <summary>
            Represents a push channel subscription.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushChannelSubscription.Channel">
            <summary>
            Name of the channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushChannelSubscription.DeviceId">
            <summary>
            Device id attached to the subscription.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushChannelSubscription.ClientId">
            <summary>
            Client id attached to the channel.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushChannelSubscription.ForDevice(System.String,System.String)">
            <summary>
            Factory method for creating a PushChannelSubscription for a deviceId.
            </summary>
            <param name="channel">Name of the channel.</param>
            <param name="deviceId">Device id.</param>
            <returns>Returns an instance of PushChannelSubscription.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannelSubscription.ForClientId(System.String,System.String)">
            <summary>
            Factory method for creating a PushChannelSubscription for a clientId.
            </summary>
            <param name="channel">Name of the channel.</param>
            <param name="clientId">Client id.</param>
            <returns>Returns an instance of PushChannelSubscription.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushChannelSubscription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Push.PushChannelSubscription"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.DeviceDetails">
            <summary>
            Class representing a Device registered for Ably push notifications.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.Id">
            <summary>
            Device Id.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.Platform">
            <summary>
            Device platform. One of 'android', 'ios' or 'browser').
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.FormFactor">
            <summary>
            Device form factor. One of 'phone', 'tablet', 'desktop', 'tv', 'watch', 'car' or 'embedded'.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.ClientId">
            <summary>
            Device ClientId which is associated with the push registration.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.Metadata">
            <summary>
            Device Metadata. It's a flexible key value pair. Usually used to tag devices.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.Push">
            <summary>
            Push registration data.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.DeviceSecret">
            <summary>
            Random string which is automatically generated when a new LocalDevice is created and can be used to authenticate PushAdmin Rest requests.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.DeviceDetails.PushData">
            <summary>
            Class describing Push data.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.PushData.Recipient">
            <summary>
            Push Recipient. Currently supporter recipients are Apple (apns), Google (fcm) and Browser (web).
            For more information - https://ably.com/docs/rest-api#post-device-registration.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.PushData.State">
            <summary>
            State of the push integration.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.DeviceDetails.PushData.ErrorReason">
            <summary>
            Error registering device as a PushTarget.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.IDeviceRegistrations">
            <summary>
            Device Registrations APIs. For more information visit the Ably Rest Documentation: https://ably.com/docs/rest-api#post-device-registration.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.SaveAsync(IO.Ably.Push.DeviceDetails)">
            <summary>
            Update a device registration.
            </summary>
            <param name="details">Device to be updated. Only `clientId`, `metadata` and `Push.Recipient` can be updates. The rest of the values must match what the server contains.
            RestApi: https://ably.com/docs/rest-api#update-device-registration.</param>
            <returns>Updated Device Registration.</returns>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.GetAsync(System.String)">
            <summary>
            Obtain the details for a device registered for receiving push registrations.
            RestApi: https://ably.com/docs/rest-api#get-device-registration.
            </summary>
            <param name="deviceId">Id of the device.</param>
            <returns>Returns a DeviceDetails class if the device is found or `null` if not.</returns>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.List(IO.Ably.Push.ListDeviceDetailsRequest)">
            <summary>
            Obtain the details for devices registered for receiving push registrations.
            RestApi: https://ably.com/docs/rest-api#list-device-registrations.
            </summary>
            <param name="request">Allows to filter by clientId or deviceId. For further information <see cref="T:IO.Ably.Push.ListDeviceDetailsRequest"/>.</param>
            <returns>A paginated list of DeviceDetails.</returns>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.RemoveAsync(IO.Ably.Push.DeviceDetails)">
            <summary>
            Removes a registered device.
            RestAPI: https://ably.com/docs/rest-api#delete-device-registration.
            </summary>
            <param name="details">DeviceDetails to be removed.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.RemoveAsync(System.String)">
            <summary>
            Removes a registered device.
            RestAPI: https://ably.com/docs/rest-api#delete-device-registration.
            </summary>
            <param name="deviceId">The deviceId of the device to be removed.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Push.IDeviceRegistrations.RemoveWhereAsync(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Removes a registered devices based on a filter of parameters.
            RestAPI: https://ably.com/docs/rest-api#delete-device-registration.
            </summary>
            <param name="deleteFilter">Filter devices by deviceId or clientId.</param>
            <returns>Task.</returns>
        </member>
        <member name="T:IO.Ably.Push.IMobileDevice">
            <summary>
            Interface for communicating with a mobile device supporting pushing notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.IMobileDevice.SetPreference(System.String,System.String,System.String)">
            <summary>
            Persist a preferences on the mobile device. TODO: Add info specific to Android and iOS.
            </summary>
            <param name="key">Key.</param>
            <param name="value">Value.</param>
            <param name="groupName">Groups preferences so they can be removed easier.</param>
        </member>
        <member name="M:IO.Ably.Push.IMobileDevice.GetPreference(System.String,System.String)">
            <summary>
            Retrieves a preference from the mobile device.
            </summary>
            <param name="key">Preference Key.</param>
            <param name="groupName">Group name.</param>
            <returns>The value of the preference or null if it doesn't exist.</returns>
        </member>
        <member name="M:IO.Ably.Push.IMobileDevice.ClearPreferences(System.String)">
            <summary>
            Remove a whole group of preferences.
            </summary>
            <param name="groupName">Group name.</param>
        </member>
        <member name="M:IO.Ably.Push.IMobileDevice.RequestRegistrationToken(System.Action{IO.Ably.Result{IO.Ably.Push.RegistrationToken}})">
            <summary>
            Requests a registration token. So far used only by Android.
            </summary>
            <param name="callback">Action which is executed when the operation completes.</param>
        </member>
        <member name="P:IO.Ably.Push.IMobileDevice.Callbacks">
            <summary>
            Defines callbacks executed at different parts of the push journey.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.IMobileDevice.DevicePlatform">
            <summary>
            Device platform i.e. Android.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.IMobileDevice.FormFactor">
            <summary>
            Device form factor.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.IPushChannelSubscriptions">
            <summary>
            Apis for managing ChannelSubscriptions.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.IPushChannelSubscriptions.SaveAsync(IO.Ably.Push.PushChannelSubscription)">
            <summary>
            Subscribe either a single device or all devices associated with a client ID to receive push notifications from messages sent to a channel.
            RestApi: https://ably.com/docs/rest-api#post-channel-subscription.
            </summary>
            <param name="subscription">Channel subscription object.</param>
            <returns>Return an updated Channel subscription object.</returns>
        </member>
        <member name="M:IO.Ably.Push.IPushChannelSubscriptions.ListAsync(IO.Ably.Push.ListSubscriptionsRequest)">
            <summary>
            Get a list of push notification subscriptions to channels.
            RestApi: https://ably.com/docs/rest-api#list-channel-subscriptions.
            </summary>
            <param name="requestFilter">Provides a way to filter results. <see cref="T:IO.Ably.Push.ListSubscriptionsRequest"/>.</param>
            <returns>Returns a paginated list of ChannelSubscriptions.</returns>
        </member>
        <member name="M:IO.Ably.Push.IPushChannelSubscriptions.ListChannelsAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            List all channels with at least one subscribed device.
            RestApi: https://ably.com/docs/rest-api#list-channels.
            </summary>
            <param name="requestParams">Allows adding a limit to the number of results and supports paginated requests handling.</param>
            <returns>Paginated list of channel names.</returns>
        </member>
        <member name="M:IO.Ably.Push.IPushChannelSubscriptions.RemoveAsync(IO.Ably.Push.PushChannelSubscription)">
            <summary>
            Stop receiving push notifications when push messages are published on the specified channels.
            Please note that this operation is done asynchronously so immediate requests subsequent to this delete request may briefly still return the subscription.
            RestApi: https://ably.com/docs/rest-api#delete-channel-subscription.
            </summary>
            <param name="subscription">Channel Subscription object to unsubscribe.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Push.IPushChannelSubscriptions.RemoveWhereAsync(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Stop receiving push notifications when push messages are published on the specified channels.
            Please note that this operation is done asynchronously so immediate requests subsequent to this delete request may briefly still return the subscription.
            Allows custom parameters to be passed in the filter.
            RestApi: https://ably.com/docs/rest-api#delete-channel-subscription.
            </summary>
            <param name="removeParams">Dictionary with query parameters passed to the server. Possible values are `clientId`, `deviceId` and `channel`.</param>
            <returns>Task.</returns>
        </member>
        <member name="T:IO.Ably.Push.ListDeviceDetailsRequest">
            <summary>
            Encapsulates the List DeviceDetails filter and it prevents invalid states.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.ListDeviceDetailsRequest.ClientId">
            <summary>
            ClientId filter.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.ListDeviceDetailsRequest.DeviceId">
            <summary>
            DeviceId filter.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.ListDeviceDetailsRequest.WithDeviceId(System.String,System.Nullable{System.Int32})">
            <summary>
            Creates a Request to filter devices by deviceId.
            </summary>
            <param name="deviceId">The deviceId used to filter devices.</param>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>returns an instance of <see cref="T:IO.Ably.Push.ListDeviceDetailsRequest"/> with specified values set.</returns>
        </member>
        <member name="M:IO.Ably.Push.ListDeviceDetailsRequest.WithClientId(System.String,System.Nullable{System.Int32})">
            <summary>
            Creates a Request to filter devices by clientId.
            </summary>
            <param name="clientId">The clientId used to filter devices.</param>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>returns an instance of <see cref="T:IO.Ably.Push.ListDeviceDetailsRequest"/> with specified values set.</returns>
        </member>
        <member name="M:IO.Ably.Push.ListDeviceDetailsRequest.Empty(System.Nullable{System.Int32})">
            <summary>
            Empty filter.
            </summary>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>returns an instance of <see cref="T:IO.Ably.Push.ListDeviceDetailsRequest"/> with specified values set.</returns>
        </member>
        <member name="T:IO.Ably.Push.ListSubscriptionsRequest">
            <summary>
            Encapsulates the List DeviceDetails filter and it prevents invalid states.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.ListSubscriptionsRequest.Channel">
            <summary>
            Channel filter.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.ListSubscriptionsRequest.ClientId">
            <summary>
            ClientId filter.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.ListSubscriptionsRequest.DeviceId">
            <summary>
            DeviceId filter.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.ListSubscriptionsRequest.WithDeviceId(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Creates a Request to filter channel subscriptions by deviceId and channel.
            </summary>
            <param name="channel">Optional channel filter.</param>
            <param name="deviceId">DeviceId filter.</param>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>Returns an instance of <see cref="T:IO.Ably.Push.ListSubscriptionsRequest"/> with specified values set.</returns>
        </member>
        <member name="M:IO.Ably.Push.ListSubscriptionsRequest.WithClientId(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Creates a Request to filter channel subscriptions by clientId and channel.
            </summary>
            <param name="channel">Optional channel filter.</param>
            <param name="clientId">The clientId used to filter devices.</param>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>Returns an instance of <see cref="T:IO.Ably.Push.ListSubscriptionsRequest"/> with specified values set.</returns>
        </member>
        <member name="M:IO.Ably.Push.ListSubscriptionsRequest.Empty(System.Nullable{System.Int32})">
            <summary>
            Empty filter.
            </summary>
            <param name="limit">The number of results to return. Default is 100 and Max is 1000.</param>
            <returns>Returns an instance of <see cref="T:IO.Ably.Push.ListSubscriptionsRequest"/> with specified values set.</returns>
        </member>
        <member name="T:IO.Ably.Push.LocalDevice">
            <summary>
            LocalDevice represents the current state of the device in respect of it being a target for push notifications.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.LocalDevice.DeviceIdentityToken">
            <summary>
            Devices that have completed registration have an identity token assigned to them by the push service.
            It can be used to authenticate Push Admin requests.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.LocalDevice.IsRegistered">
            <summary>
            Checks if the device is registered to receive push notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.LocalDevice.Create(System.String,IO.Ably.Push.IMobileDevice)">
            <summary>
            Create a new instance of localDevice with a random Id and secret.
            </summary>
            <param name="clientId">Optional clientId which is set on the device.</param>
            <param name="mobileDevice">If a mobile device is present it we will use the DevicePlatform and FormFactor from there.</param>
            <returns>Instance of LocalDevice.</returns>
        </member>
        <member name="T:IO.Ably.Push.PushAdmin">
            <summary>
            Push Admin APIs.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushAdmin.ChannelSubscriptions">
            <summary>
            Exposes channel subscriptions apis.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushAdmin.DeviceRegistrations">
            <summary>
            Exposes device registrations apis.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.RegisterDevice(IO.Ably.Push.DeviceDetails)">
            <summary>
            Register a new device
            The public api doesn't expose this method but it's much easier to put it here than to manually call it when needed.
            </summary>
            <param name="details">Device details needed for registration.</param>
            <returns>Updated device including a deviceIdentityToken assigned by the Push service.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.PatchDeviceRecipient(IO.Ably.Push.DeviceDetails)">
            <summary>
            Update device recipient information
            The public api doesn't expose this method but it's much easier to put it here than to manually call it when needed.
            </summary>
            <param name="details">Device details which contain the update.</param>
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.PublishAsync(Newtonsoft.Json.Linq.JObject,Newtonsoft.Json.Linq.JObject)">
            <summary>
            Publish a push notification message.
            </summary>
            <param name="recipient">Recipient. Best description of what is allowed can be found in the RestApi documentation: https://ably.com/docs/rest-api#post-device-registration.</param>
            <param name="payload">Message payload.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IPushChannelSubscriptions#SaveAsync(IO.Ably.Push.PushChannelSubscription)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IPushChannelSubscriptions#ListAsync(IO.Ably.Push.ListSubscriptionsRequest)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IPushChannelSubscriptions#RemoveAsync(IO.Ably.Push.PushChannelSubscription)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IPushChannelSubscriptions#RemoveWhereAsync(System.Collections.Generic.IDictionary{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IPushChannelSubscriptions#ListChannelsAsync(IO.Ably.PaginatedRequestParams)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#SaveAsync(IO.Ably.Push.DeviceDetails)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#GetAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#List(IO.Ably.Push.ListDeviceDetailsRequest)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#RemoveAsync(IO.Ably.Push.DeviceDetails)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#RemoveAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Push.PushAdmin.IO#Ably#Push#IDeviceRegistrations#RemoveWhereAsync(System.Collections.Generic.Dictionary{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="T:IO.Ably.Push.PushRealtime">
            <summary>
            Push Apis for Realtime clients.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushRealtime.Activate">
            <summary>
            Start the push notification device registration process.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushRealtime.Deactivate">
            <summary>
            Starts the push notification device de-registration process.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushRealtime.Admin">
            <summary>
            Admin APIs for Push notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.PushRealtime.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Push.PushRest">
            <summary>
            Push Apis for Rest clients.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.PushRest.Admin">
            <summary>
            Admin APIs for Push notifications.
            </summary>
        </member>
        <member name="T:IO.Ably.Push.RegistrationToken">
            <summary>
            Class used to hold registration tokens.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.RegistrationToken.#ctor(System.String,System.String)">
            <summary>
            Constructs a new registration token instance.
            </summary>
            <param name="tokenType">Token type.</param>
            <param name="tokenValue">Token value.</param>
        </member>
        <member name="P:IO.Ably.Push.RegistrationToken.Type">
            <summary>
            FGM or GCM for Google and APNS for Apple.
            </summary>
        </member>
        <member name="P:IO.Ably.Push.RegistrationToken.Token">
            <summary>
            Token value.
            </summary>
        </member>
        <member name="M:IO.Ably.Push.RegistrationToken.ToString">
            <summary>
            Overrides to string to display Type and Token.
            </summary>
            <returns>Returns a string including Type and Token Value.</returns>
        </member>
        <member name="T:IO.Ably.Realtime.ChannelEvent">
            <summary>
            Events defined for a channel. ChannelEvents are equal to <see cref="T:IO.Ably.Realtime.ChannelState"/> with the addition of the Update event.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Initialized">
            <summary>
            Emitted when a channel object having the corresponding state has been initialized but no attach has
            yet been attempted.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Attaching">
            <summary>
            Emitted when an attach has been initiated by sending a request to the service. This indicates a
            transient state; it will be followed either by a transition to Attached or Failed.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Attached">
            <summary>
            Emitted when Attach has succeeded. In the attached state a client may publish, and
            subscribe to messages.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Detaching">
            <summary>
            Emitted when a detach has been initiated by sending a request to the service. This is a
            transient state; it will be followed either by a transition to Detached or Failed.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Detached">
            <summary>
            Emitted when the channel, having previously been attached, has been detached.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Suspended">
            <summary>
            Emitted when the connection state enters the Suspended state and the channel is in the Attaching or Attached state.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Failed">
            <summary>
            An indefinite failure condition. Emitted when a channel error has
            been received from the Ably service (such as an attempt to attach without the
            necessary access rights).
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelEvent.Update">
            <summary>
            Emitted for changes to channel conditions for which the <see cref="T:IO.Ably.Realtime.ChannelState"/> does not change
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.Presence">
            <summary>
            A class that provides access to presence operations and state for the associated Channel.
            </summary>
            <summary>
            A class that provides access to presence operations and state for the associated Channel.
            </summary>
            <summary>
            A class that provides access to presence operations and state for the associated Channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Presence.SyncComplete">
            <summary>
            Has the sync completed.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Presence.IsSyncInProgress">
            <summary>
            Indicates whether there is currently a sync in progress.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.SkipSync">
            <summary>
            Called when a protocol message HasPresenceFlag == false. The presence map should be considered in sync immediately
            with no members present on the channel. See [RTP1] for more detail.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.RemoveAllListeners">
            <summary>
            Disposes the current Presence instance. Removes all listening handlers.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.GetAsync(IO.Ably.Realtime.Presence.GetParams)">
            <summary>
                Get the presence state given a set of options <see cref="T:IO.Ably.Realtime.Presence.GetParams"/>. Implicitly attaches the Channel.
                However, if the channel is in or moves to the FAILED.
                state before the operation succeeds, it will result in an error.
            </summary>
            <param name="options">Options for the GetAsync. For details <see cref="T:IO.Ably.Realtime.Presence.GetParams"/>.</param>
            <returns>a list of PresenceMessages.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.GetAsync(System.Boolean)">
            <summary>
                Get the presence state for the current channel, optionally waiting for Sync to complete.
                Implicitly attaches the Channel. However, if the channel is in or moves to the FAILED.
                state before the operation succeeds, it will result in an error.
            </summary>
            <param name="waitForSync">whether it should wait for a sync to complete.</param>
            <returns>the current present members.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.GetAsync(System.String,System.Boolean)">
            <summary>
                Get the presence state for a given clientId, optionally waiting for Sync to complete.
                Implicitly attaches the Channel. However, if the channel is in or moves to the FAILED.
                state before the operation succeeds, it will result in an error.
            </summary>
            <param name="clientId">requests Presence for the this clientId.</param>
            <param name="waitForSync">whether it should wait for a sync to complete.</param>
            <returns>the current present members.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.GetAsync(System.String,System.String,System.Boolean)">
            <summary>
                Get the presence state for a given clientId and connectionId, optionally waiting for Sync to complete.
                Implicitly attaches the Channel. However, if the channel is in or moves to the FAILED.
                state before the operation succeeds, it will result in an error.
            </summary>
            <param name="clientId">requests Presence for the this clientId.</param>
            <param name="connectionId">requests Presence for the a specific connectionId.</param>
            <param name="waitForSync">whether it should wait for a sync to complete.</param>
            <returns>the current present members.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Subscribe(System.Action{IO.Ably.PresenceMessage})">
            <summary>
            Subscribe to presence events on the associated Channel. This implicitly
            attaches the Channel if it is not already attached.
            </summary>
            <param name="handler">handler to be notified for the arrival of presence messages.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Subscribe(IO.Ably.PresenceAction,System.Action{IO.Ably.PresenceMessage})">
            <summary>
            Subscribe to presence events with a specific action on the associated Channel. This implicitly
            attaches the Channel if it is not already attached.
            </summary>
            <param name="action">action to be observed.</param>
            <param name="handler">handler to be notified for the arrival of presence messages.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Subscribe(IO.Ably.PresenceAction,System.Func{IO.Ably.PresenceMessage,System.Threading.Tasks.Task})">
            <summary>
            Subscribe to presence events with a specific action on the associated Channel. This implicitly
            attaches the Channel if it is not already attached.
            </summary>
            <param name="action">action to be observed.</param>
            <param name="handler">handler to be notified for the arrival of presence messages.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Unsubscribe(System.Action{IO.Ably.PresenceMessage})">
            <summary>
            Unsubscribe a previously subscribed handler.
            </summary>
            <param name="handler">the handler to be unsubscribed.</param>
            <returns>true if unsubscribed, false if the handler doesn't exist.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Unsubscribe">
            <summary>
            Unsubscribes all attached handlers.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Unsubscribe(IO.Ably.PresenceAction,System.Action{IO.Ably.PresenceMessage})">
            <summary>
            Unsubscribe a specific handler for a specific action.
            </summary>
            <param name="presenceAction">the specific action.</param>
            <param name="handler">the handler to be unsubscribed.</param>
            <returns>true if unsubscribed, false if the handler is not found.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Enter(System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Enter this client into this channel. This client will be added to the presence set
            and presence subscribers will see an enter message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.EnterAsync(System.Object)">
            <summary>
            Enter this client into this channel. This client will be added to the presence set
            and presence subscribers will see an enter message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Update(System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Update the presence data for this client. If the client is not already a member of
            the presence set it will be added, and presence subscribers will see an enter or
            update message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.UpdateAsync(System.Object)">
            <summary>
            Update the presence data for this client. If the client is not already a member of
            the presence set it will be added, and presence subscribers will see an enter or
            update message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.UpdateClient(System.String,System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
             Update the presence data for a specified client into this channel.
             If the client is not already a member of the presence set it will be added,
             and presence subscribers will see a corresponding presence message
             with an empty data payload.As for #enterClient above, the connection
             must be authenticated in a way that enables it to represent an arbitrary clientId.
            </summary>
            <param name="clientId">the id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.UpdateClientAsync(System.String,System.Object)">
            <summary>
             Update the presence data for a specified client into this channel.
             If the client is not already a member of the presence set it will be added,
             and presence subscribers will see a corresponding presence message
             with an empty data payload.As for #enterClient above, the connection
             must be authenticated in a way that enables it to represent an arbitrary clientId.
            </summary>
            <param name="clientId">the id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.EnterClient(System.String,System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Enter a specified client into this channel.The given clientId will be added to
            the presence set and presence subscribers will see a corresponding presence message
            with an empty data payload.
            This method is provided to support connections (eg connections from application
            server instances) that act on behalf of multiple clientIds. In order to be able to
            enter the channel with this method, the client library must have been instanced
            either with a key, or with a token bound to the wildcard clientId.
            </summary>
            <param name="clientId">id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.EnterClientAsync(System.String,System.Object)">
            <summary>
            Enter a specified client into this channel.The given clientId will be added to
            the presence set and presence subscribers will see a corresponding presence message
            with an empty data payload.
            This method is provided to support connections (eg connections from application
            server instances) that act on behalf of multiple clientIds. In order to be able to
            enter the channel with this method, the client library must have been instanced
            either with a key, or with a token bound to the wildcard clientId.
            </summary>
            /// <param name="clientId">id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Leave(System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Leave this client from this channel. This client will be removed from the presence
            set and presence subscribers will see a leave message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.LeaveAsync(System.Object)">
            <summary>
            Leave this client from this channel. This client will be removed from the presence
            set and presence subscribers will see a leave message for this client.
            </summary>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.LeaveClient(System.String,System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Leave a given client from this channel. This client will be removed from the
            presence set and presence subscribers will see a corresponding presence message
            with an empty data payload.
            This method is provided to support connections (eg connections from application
            server instances) that act on behalf of multiple clientIds. In order to be able to
            enter the channel with this method, the client library must have been instanced
            either with a key, or with a token bound to the wildcard clientId.
            </summary>
            <param name="clientId">the id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <param name="callback">a listener to be notified on completion of the operation.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.LeaveClientAsync(System.String,System.Object)">
            <summary>
            Leave a given client from this channel. This client will be removed from the
            presence set and presence subscribers will see a corresponding presence message
            with an empty data payload.
            This method is provided to support connections (eg connections from application
            server instances) that act on behalf of multiple clientIds. In order to be able to
            enter the channel with this method, the client library must have been instanced
            either with a key, or with a token bound to the wildcard clientId.
            </summary>
            <param name="clientId">the id of the client.</param>
            <param name="data">optional data (eg a status message) for this member.</param>
            <returns>Result whether the operation was success or error.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.HistoryAsync(System.Boolean)">
            <summary>
            Obtain recent history for this channel using the REST API.
            The history provided relates to all clients of this application,
            not just this instance.
            </summary>
            <param name="untilAttach">optionally can add until attached parameter.</param>
            <exception cref="T:IO.Ably.AblyException">can throw if untilAttached=true and the current channel is not attached.</exception>
            <returns>Paginated list of Presence messages.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.HistoryAsync(IO.Ably.PaginatedRequestParams,System.Boolean)">
            <summary>
            Obtain recent history for this channel using the REST API.
            The history provided relates to all clients of this application,
            not just this instance.
            </summary>
            <param name="query">the request params. See the Ably REST API documentation for more details.</param>
            <param name="untilAttach">add until attached parameter.</param>
            <exception cref="T:IO.Ably.AblyException">can throw if untilAttached=true and the current channel is not attached.</exception>
            <returns>Paginated list of Presence messages.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Dispose(System.Boolean)">
            <summary>
            Dispose(bool disposing) executes in two distinct scenarios. If disposing equals true, the method has
            been called directly or indirectly by a user's code. Managed and unmanaged resources can be disposed.
            If disposing equals false, the method has been called by the runtime from inside the finalizer and
            you should not reference other objects. Only unmanaged resources can be disposed.
            </summary>
            <param name="disposing">Refer to the summary.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Presence.Dispose">
            <summary>
            Implement IDisposable.
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.Presence.GetParams">
            <summary>
            Class used to pass parameters when using Presence.GetAsync method.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Presence.GetParams.WaitForSync">
            <summary>
            Should we wait for sync to complete. If false it will return the current saved state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Presence.GetParams.ClientId">
            <summary>
            Indicates whether to get the Presence for a specific ClientId.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Presence.GetParams.ConnectionId">
            <summary>
            Indicates whether to get the Presence for a specific ConnectionId.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Workflow.RealtimeState.ConnectionData.Id">
            <summary>
                The id of the current connection. This string may be
                used when recovering connection state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Workflow.RealtimeState.ConnectionData.Serial">
            <summary>
                The serial number of the last message received on this connection.
                The serial number may be used when recovering connection state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Workflow.RealtimeState.ConnectionData.Key">
            <summary>
            The current connection key.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Workflow.RealtimeState.ConnectionData.ErrorReason">
            <summary>
                Information relating to the transition to the current state,
                as an Ably ErrorInfo object. This contains an error code and
                message and, in the failed state in particular, provides diagnostic
                error information.
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.Workflow.RealtimeWorkflow">
            <summary>
            Realtime workflow has 2 roles
            1. It serializes requests coming from different threads and guarantees that they are executing one by one and in order
            on a single thread. This makes it very easy to mutate state because we are immune from thread race conditions.
            There requests are encapsulated in Command objects (objects inheriting from RealtimeCommand) which provide
            information about what needs to happen and also hold any parameters necessary for the operation. For example if we take the
            SetClosedStateCommand object. The intention is to change the Connection state to Closed but the Command object also contains the error
            if any associated with this request. This makes logging very easy as we can clearly see the intent of the command and the parameters. Also in the
            future we can parse the logs and easily recreate state in the library.
            2. Centralizes the logic for handling Commands. It is now much easier to find where things are happening. If you exclude
            Channel presence and Channel state management, everything else could be found in this class. It does make it rather long but
            the logic block are rather small and easy to understand.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Workflow.RealtimeWorkflow.ProcessCommandInner(IO.Ably.Realtime.Workflow.RealtimeCommand)">
            <summary>
            Processes a command and return a list of commands that need to be immediately executed.
            </summary>
            <param name="command">The current command that will be executed.</param>
            <returns>returns the next command that needs to be executed.</returns>
            <exception cref="T:IO.Ably.AblyException">will throw an AblyException if anything goes wrong.</exception>
        </member>
        <member name="M:IO.Ably.Realtime.Workflow.RealtimeWorkflow.TryGetCount(System.Int32@)">
            <summary>
            Attempt to query the backlog length of the queue.
            </summary>
            <param name="count">The (approximate) count of items in the Channel.</param>
        </member>
        <member name="T:IO.Ably.Realtime.ConnectionEvent">
            <summary>A series of connection states.</summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Initialized">
            <summary>
            Connection is initialised. This is initial state when the library is initialised.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Connecting">
            <summary>
            Trying to connect to the server.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Connected">
            <summary>
            Connected to the server.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Disconnected">
            <summary>
            Disconnected from the server. Usually followed by Connecting or Suspended.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Suspended">
            <summary>
            The connection was suspended. Usually followed by retry.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Closing">
            <summary>
            The connection is closing. Usually followed by Closed or Failed.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Closed">
            <summary>
            The connection is Closed. This is an end state and won't change until Connect() is called.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Failed">
            <summary>
            Connection is in a failed state. This is a terminal state.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionEvent.Update">
            <summary>
            The current state was updated. Usually happens if the Auth is updated.
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.RealtimeChannels">
            <summary>
            Manages Realtime channels.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.Get(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.Get(System.String,IO.Ably.ChannelOptions)">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeChannels.Item(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.Release(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.ReleaseAll">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.Exists(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.System#Collections#Generic#IEnumerable{IO#Ably#Realtime#IRealtimeChannel}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannels.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the channels collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the channels collection.</returns>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeChannel.AttachResume">
            <summary>
            True when the channel moves to the @ATTACHED@ state, and False
            when the channel moves to the @DETACHING@ or @FAILED@ states.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeChannel.Push">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannel.PublishAsync(System.String,System.Object,System.String)">
            <summary>Publish a single message on this channel based on a given event name and payload.</summary>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannel.Publish(System.Collections.Generic.IEnumerable{IO.Ably.Message},System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>Publish several messages on this channel.</summary>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannel.PublishAsync(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <summary>Publish several messages on this channel.</summary>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeChannel.ReattachAfterTimeout(IO.Ably.ErrorInfo,IO.Ably.Types.ProtocolMessage)">
            <summary>
            should only be called when the channel is SUSPENDED.
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.ChannelErrorEventArgs">
            <summary>
            EventArgs class used when a Channel error gets raised.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelErrorEventArgs.Reason">
            <summary>
            Error reason.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.ChannelErrorEventArgs.#ctor(IO.Ably.ErrorInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.ChannelErrorEventArgs"/> class.
            </summary>
            <param name="reason">Error reason.</param>
        </member>
        <member name="T:IO.Ably.Realtime.ChannelState">
            <summary>
            States defined for a channel. ChannelEvents are a logical subset of <see cref="T:IO.Ably.Realtime.ChannelEvent"/> (which has the additional Update event).
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Initialized">
            <summary>
            A channel object having this state has been initialized but no attach has
            yet been attempted.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Attaching">
            <summary>
            An attach has been initiated by sending a request to the service. This is a
            transient state; it will be followed either by a transition to Attached or Failed.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Attached">
            <summary>
            Attach has succeeded. In the attached state a client may publish, and
            subscribe to messages.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Detaching">
            <summary>
            An detach has been initiated by sending a request to the service. This is a
            transient state; it will be followed either by a transition to Detached or Failed.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Detached">
            <summary>
            The channel, having previously been attached, has been detached.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Suspended">
            <summary>
            If the connection state enters the SUSPENDED state, then an ATTACHING or ATTACHED channel state will transition to SUSPENDED (RTL3c)
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ChannelState.Failed">
            <summary>
            An indefinite failure condition. This state is entered if a channel error has
            been received from the Ably service (such as an attempt to attach without the
            necessary access rights).
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.ChannelStateChange">
            <summary>
            Class representing a channel state change.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.ChannelStateChange.#ctor(IO.Ably.Realtime.ChannelEvent,IO.Ably.Realtime.ChannelState,IO.Ably.Realtime.ChannelState,IO.Ably.ErrorInfo,IO.Ably.Types.ProtocolMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.ChannelStateChange"/> class.
            </summary>
            <param name="e">channel event.</param>
            <param name="state">Current state of the channel.</param>
            <param name="previous">Previous state of the channel.</param>
            <param name="error">Error if any.</param>
            <param name="protocolMessage">Protocol message.</param>
        </member>
        <member name="M:IO.Ably.Realtime.ChannelStateChange.#ctor(IO.Ably.Realtime.ChannelEvent,IO.Ably.Realtime.ChannelState,IO.Ably.Realtime.ChannelState,IO.Ably.ErrorInfo,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.ChannelStateChange"/> class.
            </summary>
            <param name="e">channel event.</param>
            <param name="state">Current state of the channel.</param>
            <param name="previous">Previous state of the channel.</param>
            <param name="error">Error if any.</param>
            <param name="resumed">whether the connection was resumed.</param>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelStateChange.Previous">
            <summary>
            Previous channel state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelStateChange.Current">
            <summary>
            Current channel state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelStateChange.Error">
            <summary>
            Error if any that caused the state change.
            TODO: Update to Reason to be inline with the other libraries.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelStateChange.Resumed">
            <summary>
            Whether the connection was resumed.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelStateChange.Event">
            <summary>
            Channel event.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.ChannelStateChange.ToString">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Realtime.NetworkState">
            <summary>
            Represents the OS network state.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.NetworkState.Online">
            <summary>
            Online network state
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.NetworkState.Offline">
            <summary>
            Offline network state
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.Connection">
            <summary>
            A class representing the connection associated with an AblyRealtime instance.
            The Connection object exposes the lifecycle and parameters of the realtime connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.NotifyClient">
            <summary>
            Gets Action used to notify external clients. The logic is centralised in the RealtimeClient.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Connection.NotifyOperatingSystemNetworkState(IO.Ably.Realtime.NetworkState)">
            <summary>
            This method is used when the Operating system notifies the library about changes in
            the network state.
            </summary>
            <param name="state">The current state of the OS network connection.</param>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.State">
            <summary>
                Indicates the current state of this connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.Id">
            <summary>
                The id of the current connection. This string may be
                used when recovering connection state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.Serial">
            <summary>
                The serial number of the last message received on this connection.
                The serial number may be used when recovering connection state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.Key">
            <summary>
            Gets the current connection Key.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.ConnectionResumable">
            <summary>
            Indicates whether the current connection can be resumed.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.RecoveryKey">
            <summary>
            - (RTN16b) Connection#recoveryKey is an attribute composed of the connectionKey, and the latest connectionSerial received on the connection, and the current msgSerial.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.ConnectionStateTtl">
            <summary>
            Gets the current connections time to live.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.ErrorReason">
            <summary>
                Information relating to the transition to the current state,
                as an Ably ErrorInfo object. This contains an error code and
                message and, in the failed state in particular, provides diagnostic
                error information.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.Host">
            <summary>
            Gets the currently used Host.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.Connection.ConfirmedAliveAt">
            <summary>
            Gets the last point in time when the connection was confirmed alive.
            </summary>
        </member>
        <member name="E:IO.Ably.Realtime.Connection.ConnectionStateChanged">
            <summary>
            EventHandler which exposes Connection state changes in an idiomatic .net way.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Connection.Connect">
            <summary>
            Instructs the library to start a connection to the server.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Connection.PingAsync">
            <summary>
            Sends a ping request to the server and waits for the requests to be confirmed.
            </summary>
            <returns>returns a result which is either successful and has the time it took for the request to complete or
            it contains an error indicating the reason of the failure.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.Connection.Ping(System.Action{System.Nullable{System.TimeSpan},IO.Ably.ErrorInfo})">
            <summary>
            Sends a ping request to the server. It supports an optional <paramref name="callback"/> parameter if the
            client wants to be notified of the result. Use <methodref name="PingAsync"/> inside async methods.
            </summary>
            <param name="callback">an action which will be called when the Ping completes. It either indicates success or contains an error.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Connection.Close">
            <summary>
                Causes the connection to close, entering the <see cref="F:IO.Ably.Realtime.ConnectionState.Closed" /> state. Once closed,
                the library will not attempt to re-establish the connection without a call
                to <see cref="M:IO.Ably.Realtime.Connection.Connect" />.
            </summary>
        </member>
        <member name="T:IO.Ably.Realtime.ConnectionState">
            <summary>
            A series of connection states.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Initialized">
            <summary>
            A connection object having this state has been initialized but no connection has yet been
            attempted.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Connecting">
            <summary>
            A connection attempt has been initiated. The connecting state is entered as soon as the library
            has completed initialization, and is reentered each time connection is re-attempted following
            disconnection.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Connected">
            <summary>A connection exists and is active.</summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Disconnected">
            <summary>A temporary failure condition.
            No current connection exists because there is no network connectivity or no host is unavailable.
            </summary>
            <remarks>The disconnected state is entered if an established connection is dropped, or if a connection
            attempt was unsuccessful. In the disconnected state the library will trigger a new connection
            attempt after a short period, anticipating that the connection will be re-established soon, and
            connection continuity will be possible. Clients can continue to publish messages, and these will
            be queued, to be sent as soon as a connection is established.</remarks>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Suspended">
            <summary>A long term failure condition.
            No current connection exists because there is no network
            connectivity or no host is unavailable.
            </summary>
            <remarks>The suspended state is entered after a failed connection attempt if there has then been no
            connection for a period of two minutes. A new connection attempt will be triggered after a
            further two minutes. In the suspended state clients are unable to publish messages. A new
            connection attempt can also be triggered by an explicit call to connect() on the connection
            object.</remarks>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Closing">
            <summary>
            A call to Close has been made. The connection will temporarily move to Closing until a CLOSED message
            is received from the server or a timeout has been reached.
            </summary>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Closed">
            <summary>The connection has been explicitly closed by the client.</summary>
            <remarks>In the closed state, no reconnection attempts are made automatically by the library, and clients
            may not publish messages. No connection state is preserved by the service or by the library. A
            new connection attempt can be triggered by an explicit call to connect() on the connection
            object, which will result in a new connection.
            </remarks>
        </member>
        <member name="F:IO.Ably.Realtime.ConnectionState.Failed">
            <summary>
            An indefinite failure condition. This state is entered if a connection error has been received from
            the Ably service (such as an attempt to connect with a non-existent appId or with invalid
            credentials). A failed state may also be triggered by the client library directly as a result of some
            local permanent error.</summary>
            <remarks>
            In the failed state, no reconnection attempts are made automatically by the library, and clients
            may not publish messages. A new connection attempt can be triggered by an explicit call to
            Connect() on the connection object.
            </remarks>
        </member>
        <member name="T:IO.Ably.Realtime.ConnectionStateChange">
            <summary>
            Whenever the connection state changes, a ConnectionStateChange Event is emitted on the Connection object
            The ConnectionStateChange object contains the current state in attribute current, the previous state in attribute previous, and when the client is not connected and a connection attempt will be made automatically by the library, the amount of time in milliseconds until the next retry in the attribute retryIn
            If the connection state change includes error information, then the reason attribute will contain an ErrorInfo object describing the reason for the error
            <see href="Http://docs.ably.io/client-lib-development-guide/features/#TA1"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.ConnectionStateChange.#ctor(IO.Ably.Realtime.ConnectionEvent,IO.Ably.Realtime.ConnectionState,IO.Ably.Realtime.ConnectionState,System.Nullable{System.TimeSpan},IO.Ably.ErrorInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.ConnectionStateChange"/> class.
            </summary>
            <param name="connectionEvent">Connection event.</param>
            <param name="previous">previous connection state.</param>
            <param name="current">current connection state.</param>
            <param name="retryIn">if necessary, how long ably will wait until retry.</param>
            <param name="reason">if present, the error reason for this state change.</param>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.Previous">
            <summary>
            Previous connection state. <see cref="T:IO.Ably.Realtime.ConnectionState"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.Event">
            <summary>
            Current connection Event. <see cref="T:IO.Ably.Realtime.ConnectionEvent"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.Current">
            <summary>
            Current Connection State <see cref="T:IO.Ably.Realtime.ConnectionState"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.RetryIn">
            <summary>
            Optional RetryIn.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.Reason">
            <summary>
            The <see cref="T:IO.Ably.ErrorInfo">error reason</see> why the connection transitioned in the current state.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ConnectionStateChange.HasError">
            <summary>
            Has error.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.Handlers`1.Add(IO.Ably.Realtime.MessageHandlerAction{`0})">
            <summary>Add handler to the collection.</summary>
            <param name="handler">MessageHandler action to be added.</param>
        </member>
        <member name="M:IO.Ably.Realtime.Handlers`1.Remove(IO.Ably.Realtime.MessageHandlerAction{`0})">
            <summary>Remove handler from the collection.</summary>
            <param name="handler">MessageHandler action to be removed.</param>
            <returns>True if removed, false if not found.</returns>
        </member>
        <member name="T:IO.Ably.Realtime.MessageHandlerAction`1">
            <summary>Adapter to pass a delegate as IMessageHandler.</summary>
        </member>
        <member name="T:IO.Ably.Realtime.IRealtimeChannel">
            <summary>
            Interface representing a Realtime channel.
            Implement <see cref="T:IO.Ably.IEventEmitter`2"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.State">
            <summary>
                Indicates the current state of this channel.
                <see cref="T:IO.Ably.Realtime.ChannelState"/> for more details.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Params">
            <summary>
            Channel params that the server has recognised and validated.
            It cannot be used to set ChannelParams on this channel. See <see cref="M:IO.Ably.Realtime.IRealtimeChannel.SetOptions(IO.Ably.ChannelOptions,System.Action{System.Boolean,IO.Ably.ErrorInfo})"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Modes">
            <summary>
            Channel modes received from the server during Attach of this channel.
            It cannot be used to set ChannelModes on this channel. See <see cref="M:IO.Ably.Realtime.IRealtimeChannel.SetOptions(IO.Ably.ChannelOptions,System.Action{System.Boolean,IO.Ably.ErrorInfo})"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Name">
            <summary>
            Channel name.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Presence">
            <summary>
            Presence object for the current channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Options">
            <summary>
            Channel options.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.ErrorReason">
            <summary>
            Current error emitted on this channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Properties">
            <summary>
            <see cref="T:IO.Ably.Realtime.ChannelProperties"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.IRealtimeChannel.Push">
            <summary>
            A convenient set of methods that help with managing subscriptions to a push channel.
            <see cref="T:IO.Ably.Push.PushChannel"/>.
            </summary>
        </member>
        <member name="E:IO.Ably.Realtime.IRealtimeChannel.StateChanged">
            <summary>
            EventHandler for notifying Clients with channel state changes.
            </summary>
        </member>
        <member name="E:IO.Ably.Realtime.IRealtimeChannel.Error">
            <summary>
            EventHandler for notifying Client with Errors emitted on the channel.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Attach(System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Attach to this channel, and execute callback if provided.
            </summary>
            <param name="callback">optional callback.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.AttachAsync">
            <summary>
            Attach to this channel and return a Task that can be awaited.
            The task completes when Attach has completed.
            </summary>
            <returns>Task of Result.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Detach(System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Detach from this channel and execute callback if provided.
            </summary>
            <param name="callback">optional callback.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.DetachAsync">
            <summary>
            Detach from this channel and return a Task that can be awaited.
            The task complete when the Detach has completed.
            </summary>
            <returns>Task of Result.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Subscribe(System.Action{IO.Ably.Message})">
            <summary>Subscribe a handler to all messages.</summary>
            <param name="handler">the provided handler will be called when messages are received.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Subscribe(System.String,System.Action{IO.Ably.Message})">
            <summary>Subscribe a handler to only messages whose name member matches the string name.</summary>
            <param name="eventName">name of the event (usually name of the message).</param>
            <param name="handler">the provided handler will be called every time a message with <paramref name="eventName"/> is received.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Unsubscribe(System.Action{IO.Ably.Message})">
            <summary>
            Unsubscribe a handler so it's no longer called.
            </summary>
            <param name="handler">handler to be unsubscribed.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Unsubscribe(System.String,System.Action{IO.Ably.Message})">
            <summary>
            Unsubscribe a handler for a specific eventName.
            </summary>
            <param name="eventName">event name (usually name of the message).</param>
            <param name="handler">handler to be unsubscribed.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Unsubscribe">
            <summary>
            Unsubscribe all handlers.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Publish(System.String,System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo},System.String)">
            <summary>Publish a single message on this channel based on a given event name and payload.</summary>
            <param name="name">The event name.</param>
            <param name="data">The payload of the message.</param>
            <param name="callback">handler to be notified if the operation succeeded.</param>
            <param name="clientId">optional, id of the client.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.PublishAsync(System.String,System.Object,System.String)">
            <summary>
            Async implementation of publish. Use this method if you want to
            ensure the message was received by ably. If you don't want to wait for the Ack message
            then use <see cref="M:IO.Ably.Realtime.IRealtimeChannel.Publish(System.String,System.Object,System.Action{System.Boolean,IO.Ably.ErrorInfo},System.String)"/>.
            </summary>
            <param name="eventName">The event name.</param>
            <param name="data">The payload of the message.</param>
            <param name="clientId">optional, id of the client.</param>
            <returns>Task of Result.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Publish(IO.Ably.Message,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Publish a single message and execute an optional callback when completed.
            </summary>
            <param name="message">Message to be published.</param>
            <param name="callback">optional callback that is executed when the message is confirmed by the server.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.PublishAsync(IO.Ably.Message)">
            <summary>
            Publish a single message.
            The resulted task completes when a response from the server with Ack or Nack.
            Use this if you care whether the message has been received.
            </summary>
            <param name="message">Message to be published.</param>
            <returns>Task of Result.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.Publish(System.Collections.Generic.IEnumerable{IO.Ably.Message},System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Publish a number of messages and execute an optional callback when completed.
            </summary>
            <param name="messages">list of messages to be published.</param>
            <param name="callback">optional, callback to be executed on Ack on Nack received from the server.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.PublishAsync(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <summary>
            Publish a list of messages.
            The resulted task completes when a response from the server with Ack or Nack.
            </summary>
            <param name="messages">list of messages.</param>
            <returns>Task of Result.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.HistoryAsync">
            <summary>
            Returns past message of this channel.
            </summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.HistoryAsync(System.Boolean)">
            <summary>
            Returns past message of this channel.
            </summary>
            <param name="untilAttach">indicates whether it should pass the latest attach serial to 'fromSerial' parameter.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an error if untilAttach is true and the channel is not currently attached.</exception>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.HistoryAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Returns past message of this channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.HistoryAsync(IO.Ably.PaginatedRequestParams,System.Boolean)">
            <summary>
            Returns past message of this channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <param name="untilAttach">indicates whether it should pass the latest attach serial to 'fromSerial' parameter.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an error if untilAttach is true and the channel is not currently attached.</exception>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.SetOptions(IO.Ably.ChannelOptions,System.Action{System.Boolean,IO.Ably.ErrorInfo})">
            <summary>
            Updates the options for a channel. If the ChannelModes or ChannelParams differ and the channel is Attaching or Attached
            then the channel will be reattached.
            </summary>
            <param name="options">the new <see cref="T:IO.Ably.ChannelOptions"/> for the channel.</param>
            <param name="callback">optional callback that will indicate whether the method succeeded.</param>
        </member>
        <member name="M:IO.Ably.Realtime.IRealtimeChannel.SetOptionsAsync(IO.Ably.ChannelOptions)">
            <summary>
            Updates the options for a channel. If the ChannelModes or ChannelParams differ and the channel is Attaching or Attached
            then the channel will be reattached.
            </summary>
            <param name="options">the new <see cref="T:IO.Ably.ChannelOptions"/> for the channel.</param>
            <returns>returns Result to indicate whether the operation completed successfully or failed.</returns>
        </member>
        <member name="T:IO.Ably.Realtime.RealtimeTransportData">
            <summary>
            Class that encapsulates data sent over Ably Websocket Transport.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeTransportData.Original">
            <summary>
            Original Protocol message. It's handy for logging and debugging.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeTransportData.IsBinary">
            <summary>
            Whether it's a binary message.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeTransportData.Data">
            <summary>
            Binary data.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeTransportData.Text">
            <summary>
            Text data.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.RealtimeTransportData.Length">
            <summary>
            Length of binary data.
            </summary>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeTransportData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.RealtimeTransportData"/> class with text data.
            </summary>
            <param name="text">Text data.</param>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeTransportData.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Realtime.RealtimeTransportData"/> class with binary data.
            </summary>
            <param name="data">Binary data.</param>
        </member>
        <member name="M:IO.Ably.Realtime.RealtimeTransportData.Explain">
            <summary>
            Either returns the text message or the length of the binary data.
            </summary>
            <returns>text description.</returns>
        </member>
        <member name="T:IO.Ably.Realtime.ChannelProperties">
            <summary>
            Channel properties.
            </summary>
        </member>
        <member name="P:IO.Ably.Realtime.ChannelProperties.AttachSerial">
            <summary>
            contains the last channelSerial received in an ATTACHED ProtocolMessage for the channel, see RTL15a.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelDetails.ChannelId">
            <summary>
            The required name of the channel including any qualifier, if any.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelDetails.Status">
            <summary>
            The status and occupancy stats for the channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelStatus.IsActive">
            <summary>
            Indicates whether the channel that is the subject of the event is active.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelStatus.Occupancy">
            <summary>
            Metadata relating to the occupants of the channel.
            </summary>
        </member>
        <member name="T:IO.Ably.Rest.ChannelOccupancy">
            <summary>
            Metadata relating to the occupants of the channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.Connections">
            <summary>
            The number of connections.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.Publishers">
            <summary>
            The number of connections attached to the channel that are authorised to publish.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.Subscribers">
            <summary>
            The number of connections attached that are authorised to subscribe to messages.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.PresenceConnections">
            <summary>
            The number of connections that are authorised to enter members into the presence channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.PresenceMembers">
            <summary>
            The number of members currently entered into the presence channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.ChannelMetrics.PresenceSubscribers">
            <summary>
            The number of connections that are authorised to subscribe to presence messages.
            </summary>
        </member>
        <member name="T:IO.Ably.Rest.RestChannels">
            <summary>
            Class that manages RestChannels.
            </summary>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.Get(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.Get(System.String,IO.Ably.ChannelOptions)">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.Rest.RestChannels.Item(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.Release(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.ReleaseAll">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.Exists(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.System#Collections#Generic#IEnumerable{IO#Ably#Rest#IRestChannel}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannels.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the channels collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the channels collection.</returns>
        </member>
        <member name="T:IO.Ably.Rest.RestChannel">
            <summary>
            The Ably Realtime service organises the traffic within any application into named channels.
            Channels are the "unit" of message distribution; clients attach to channels to subscribe to messages,
            and every message broadcast by the service is associated with a unique channel.
            A channel cannot be instantiated but needs to be created using the AblyRest.Channels.Get("channelname").
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.RestChannel.Name">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.Rest.RestChannel.Options">
            <summary>
            Channel options of this channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.RestChannel.Push">
            <inheritdoc />
        </member>
        <member name="P:IO.Ably.Rest.RestChannel.Presence">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.PublishAsync(System.String,System.Object,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.PublishAsync(IO.Ably.Message)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.PublishAsync(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.IO#Ably#Rest#IPresence#GetAsync(System.Nullable{System.Int32},System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.IO#Ably#Rest#IPresence#GetAsync(IO.Ably.PaginatedRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.GetAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Obtain the set of members currently present for a channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Presence messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.IO#Ably#Rest#IPresence#HistoryAsync">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.IO#Ably#Rest#IPresence#HistoryAsync(IO.Ably.PaginatedRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.HistoryAsync">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.HistoryAsync(IO.Ably.PaginatedRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.Publish(System.String,System.Object,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.Publish(IO.Ably.Message)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.Publish(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.History">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.History(IO.Ably.PaginatedRequestParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Rest.RestChannel.Status">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Rest.IRestChannel">
            <summary>
            The Ably Realtime service organises the traffic within any application into named channels.
            Channels are the "unit" of message distribution; clients attach to channels to subscribe to messages,
            and every message broadcast by the service is associated with a unique channel.
            A channel cannot be instantiated but needs to be created using the AblyRest.Channels.Get("channelname").
            </summary>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.PublishAsync(System.String,System.Object,System.String)">
            <summary>
            Publish a message to the channel.
            </summary>
            <param name="name">The event name of the message to publish.</param>
            <param name="data">The message payload. Allowed payloads are string, objects and byte[].</param>
            <param name="clientId">Explicit message clientId.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.PublishAsync(IO.Ably.Message)">
            <summary>
            Publish a single message object to the channel.
            </summary>
            <param name="message"><see cref="T:IO.Ably.Message"/>.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.PublishAsync(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <summary>
            Publish a list of messages to the channel.
            </summary>
            <param name="messages">a list of messages.</param>
            <returns>Task.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.HistoryAsync">
            <summary>
            Returns past message of this channel.
            </summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.HistoryAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Returns past message of this channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of past Messages.</returns>
        </member>
        <member name="P:IO.Ably.Rest.IRestChannel.Name">
            <summary>
            Name of the channel.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.IRestChannel.Presence">
            <summary>
            Returns the Presence object.
            </summary>
        </member>
        <member name="P:IO.Ably.Rest.IRestChannel.Push">
            <summary>
            A convenient set of methods that help with managing subscriptions to a push channel.
            <see cref="T:IO.Ably.Push.PushChannel"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.Publish(System.String,System.Object,System.String)">
            <summary>
            Sync version of <see cref="M:IO.Ably.Rest.IRestChannel.PublishAsync(System.String,System.Object,System.String)"/>.
            Prefer async method where possible.
            </summary>
            <param name="name">message name.</param>
            <param name="data">optional message data object.</param>
            <param name="clientId">optional client id.</param>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.Publish(IO.Ably.Message)">
            <summary>
            Sync version of <see cref="M:IO.Ably.Rest.IRestChannel.PublishAsync(IO.Ably.Message)"/>.
            Prefer async method where possible.
            </summary>
            <param name="message">message to publish.</param>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.Publish(System.Collections.Generic.IEnumerable{IO.Ably.Message})">
            <summary>
            Sync version of <see cref="M:IO.Ably.Rest.IRestChannel.PublishAsync(System.Collections.Generic.IEnumerable{IO.Ably.Message})"/>.
            Prefer sync version where possible.
            </summary>
            <param name="messages">array of messages to publish.</param>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.History">
            <summary>
            Sync version of <see cref="M:IO.Ably.Rest.IRestChannel.HistoryAsync"/>.
            Prefer async version where possible.
            </summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.History(IO.Ably.PaginatedRequestParams)">
            <summary>
            Sync version of <see cref="M:IO.Ably.Rest.IRestChannel.HistoryAsync(IO.Ably.PaginatedRequestParams)"/>.
            Prefer async version where possible.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IRestChannel.Status">
            <summary>
            Returns the active status for the channel including the number of publishers, subscribers and presenceMembers etc.
            </summary>
            <returns><see cref="T:IO.Ably.Rest.ChannelDetails"/>Channel Details.</returns>
        </member>
        <member name="T:IO.Ably.Rest.IPresence">
            <summary>
            Interface representing Rest Presence operations.
            </summary>
        </member>
        <member name="M:IO.Ably.Rest.IPresence.GetAsync(System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            Obtain the set of members currently present for a channel.
            </summary>
            <param name="limit">Maximum number of members to retrieve up to 1,000, defaults to 100.</param>
            <param name="clientId">optional clientId filter for the member.</param>
            <param name="connectionId">optional connectionId filter for the member.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of the PresenceMessages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IPresence.HistoryAsync">
            <summary>
            Return the presence messages history for the channel.
            </summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Presence messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IPresence.HistoryAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Return the presence messages history for the channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Presence messages.</returns>
        </member>
        <member name="M:IO.Ably.Rest.IPresence.GetAsync(IO.Ably.PaginatedRequestParams)">
            <summary>
            Obtain the set of members currently present for a channel.
            </summary>
            <param name="query"><see cref="T:IO.Ably.PaginatedRequestParams"/> query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of Presence messages.</returns>
        </member>
        <member name="T:IO.Ably.ChannelOptions">
            <summary>
            Channel options used for initialising channels.
            </summary>
        </member>
        <member name="P:IO.Ably.ChannelOptions.Encrypted">
            <summary>
            Indicates whether the channel is encrypted.
            </summary>
        </member>
        <member name="P:IO.Ably.ChannelOptions.CipherParams">
            <summary>
            If Encrypted it provides the <see cref="T:IO.Ably.CipherParams"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.ChannelOptions.Params">
            <summary>
            Params allows custom parameters to be passed to the server when attaching the channel.
            In that list are 'delta' and 'rewind'. For more information about channel params visit
            https://ably.com/docs/realtime/channels/channel-parameters/overview.
            </summary>
        </member>
        <member name="P:IO.Ably.ChannelOptions.Modes">
            <summary>
            Channel Modes like Params are passed to the server when attaching the channel.
            They let specify how the channel will behave and what is allowed. <see cref="T:IO.Ably.ChannelMode"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.ChannelOptions.#ctor(IO.Ably.CipherParams)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelOptions"/> class.
            </summary>
            <param name="params"><see cref="T:IO.Ably.CipherParams"/>.</param>
        </member>
        <member name="M:IO.Ably.ChannelOptions.#ctor(System.Boolean,IO.Ably.CipherParams,IO.Ably.ChannelModes,IO.Ably.ChannelParams)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelOptions"/> class.
            </summary>
            <param name="encrypted">whether it is encrypted.</param>
            <param name="params">optional, <see cref="P:IO.Ably.ChannelOptions.CipherParams"/>.</param>
            <param name="modes">optional, <see cref="T:IO.Ably.ChannelModes"/>.</param>
            <param name="channelParams">optional, <see cref="T:IO.Ably.ChannelParams"/>.</param>
        </member>
        <member name="M:IO.Ably.ChannelOptions.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelOptions"/> class.
            </summary>
            <param name="key">cipher key.</param>
        </member>
        <member name="M:IO.Ably.ChannelOptions.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.ChannelOptions.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Transport.ConnectionInfo">
            <summary>
            Contains current connection details.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ConnectionInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.ConnectionInfo"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ConnectionInfo.#ctor(IO.Ably.Types.ProtocolMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.ConnectionInfo"/> class.
            </summary>
            <param name="message">initialises it from a message.</param>
        </member>
        <member name="P:IO.Ably.Transport.ConnectionInfo.ConnectionStateTtl">
            <summary>
            current connection time to live.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ConnectionInfo.ClientId">
            <summary>
            contains the client ID assigned to the connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ConnectionInfo.ConnectionId">
            <summary>
            Unique id of the current connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ConnectionInfo.ConnectionSerial">
            <summary>
            the connection serial.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ConnectionInfo.ConnectionKey">
            <summary>
            the connection secret key string that is used to resume a connection and its state.
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.TransportState">
            <summary>
            Current state of the websocket transport.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.TransportState.Initialized">
            <summary>
            Never connected.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.TransportState.Connecting">
            <summary>
            In the process of connecting.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.TransportState.Connected">
            <summary>
            Connection has been successfully established.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.TransportState.Closing">
            <summary>
            In the process of closing the connection.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.TransportState.Closed">
            <summary>
            Connection has been closed.
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.ITransport">
            <summary>
            Represents a websocket transport.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ITransport.Id">
            <summary>
            Unique id to represent each transport instance.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ITransport.State">
            <summary>
            Current state of the connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.ITransport.Listener">
            <summary>
            Current listener class. <see cref="T:IO.Ably.Transport.ITransportListener"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ITransport.Connect">
            <summary>
            Tries to establish a connection.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ITransport.Close(System.Boolean)">
            <summary>
            Closes the current connection.
            Optionally can suppress the closed event.
            </summary>
            <param name="suppressClosedEvent">If true, it doesn't emmit the Closed Event.
            Default: true.
            </param>
        </member>
        <member name="M:IO.Ably.Transport.ITransport.Send(IO.Ably.Realtime.RealtimeTransportData)">
            <summary>
            Sends a message.
            </summary>
            <param name="data">Transport message. <see cref="T:IO.Ably.Realtime.RealtimeTransportData"/>.</param>
            <returns>Result whether the operation succeeded or failed.</returns>
        </member>
        <member name="T:IO.Ably.Transport.ITransportFactory">
            <summary>
            Interface representing a transport factory.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ITransportFactory.CreateTransport(IO.Ably.Transport.TransportParams)">
            <summary>
            Creates a websocket transport.
            </summary>
            <param name="parameters">transport parameters.</param>
            <returns>instance of a transport object.</returns>
        </member>
        <member name="T:IO.Ably.Transport.ITransportListener">
            <summary>
            Interface defining the methods required for a transport listener.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.ITransportListener.OnTransportDataReceived(IO.Ably.Realtime.RealtimeTransportData)">
            <summary>
            Called when data has been received on the transport websocket.
            </summary>
            <param name="data"><see cref="T:IO.Ably.Realtime.RealtimeTransportData"/>.</param>
        </member>
        <member name="M:IO.Ably.Transport.ITransportListener.OnTransportEvent(System.Guid,IO.Ably.Transport.TransportState,System.Exception)">
            <summary>
            Called when the TransportState changes.
            </summary>
            <param name="transportId">Unique identifier for the current transport instance.</param>
            <param name="state">the new state.</param>
            <param name="exception">optional, exception.</param>
        </member>
        <member name="T:IO.Ably.Transport.States.Connection.ICountdownTimer">
            <summary>
            Internal interface used for countdown timer.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.States.Connection.ICountdownTimer.Start(System.TimeSpan,System.Action)">
            <summary>
            Starts a timer.
            </summary>
            <param name="delay">when to fire.</param>
            <param name="onTimeOut">action to execute.</param>
        </member>
        <member name="M:IO.Ably.Transport.States.Connection.ICountdownTimer.Abort(System.Boolean)">
            <summary>
            Aborts the timer.
            </summary>
            <param name="trigger">whether to trigger the action. Default: false.</param>
        </member>
        <member name="T:IO.Ably.Transport.TaskWrapper">
            <summary>This trivial class wraps legacy callback-style API into a Task API.</summary>
        </member>
        <member name="T:IO.Ably.Transport.TransportParams">
            <summary>
            Parameters passed when creating a new Websocket transport.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.Host">
            <summary>
            Host used to establish the connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.Tls">
            <summary>
            Use a secure connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.FallbackHosts">
            <summary>
            A list of fallback hosts.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.Port">
            <summary>
            Connection port.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.ConnectionKey">
            <summary>
            Connection key.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.ConnectionSerial">
            <summary>
            Connection serial.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.UseBinaryProtocol">
            <summary>
            Whether to use the binary protocol.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.AuthValue">
            <summary>
            Either ably key or token value.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.RecoverValue">
            <summary>
            Recover value used to recover a connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.ClientId">
            <summary>
            Id of the client establishing the connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.EchoMessages">
            <summary>
            Whether to echo message.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.AdditionalParameters">
            <summary>
            Additional parameters coming from ClientOptions.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.TransportParams.Agents">
            <summary>
            Additional agents coming from ClientOptions.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.TransportParams.GetUri">
            <summary>
            Helper method used to construct the server uri.
            </summary>
            <returns>Server uri.</returns>
        </member>
        <member name="M:IO.Ably.Transport.TransportParams.GetParams">
            <summary>
            Gets the current query parameters a dictionary.
            </summary>
            <returns>dictionary of query parameters.</returns>
        </member>
        <member name="T:IO.Ably.Transport.MsWebSocketOptions">
            <summary>
            Class encapsulating additional parameters for the websocket connection.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketOptions.SendBufferInBytes">
            <summary>
            If populated it will specify a new value for the Send buffer used in the websocket
            Default: 8192.
            </summary>
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketOptions.ReceiveBufferInBytes">
            <summary>
            If populated it will specify a new value for the Receive buffer used in the websocket
            Default: 8192.
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.MsWebSocketTransport">
            <summary>
            Websocket Transport implementation based on System.Net.Websocket.
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.MsWebSocketTransport.TransportFactory">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.TransportFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.MsWebSocketTransport.TransportFactory"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.TransportFactory.#ctor(IO.Ably.Transport.MsWebSocketOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.MsWebSocketTransport.TransportFactory"/> class.
            </summary>
            <param name="socketOptions">Additional Websocket options. <see cref="T:IO.Ably.Transport.MsWebSocketOptions"/>.</param>
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.TransportFactory.SocketOptions">
            <summary>
            Custom <see cref="T:IO.Ably.Transport.MsWebSocketOptions"/> passed to the MsWebsocket transport and then
            <see cref="T:IO.Ably.Transport.MsWebSocketConnection"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.TransportFactory.CreateTransport(IO.Ably.Transport.TransportParams)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.#ctor(IO.Ably.Transport.TransportParams,IO.Ably.Transport.MsWebSocketOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.MsWebSocketTransport"/> class.
            </summary>
            <param name="parameters">Transport parameters.</param>
            <param name="socketOptions">Additional websocket options. See <see cref="T:IO.Ably.Transport.MsWebSocketOptions"/>.</param>
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.BinaryProtocol">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.WebSocketUri">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.Id">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.State">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="P:IO.Ably.Transport.MsWebSocketTransport.Listener">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Connect">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Close(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Send(IO.Ably.Realtime.RealtimeTransportData)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Dispose(System.Boolean)">
            <inheritdoc cref="T:IO.Ably.Transport.ITransport" />
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketTransport.Finalize">
            <summary>
            Finalizes an instance of the <see cref="T:IO.Ably.Transport.MsWebSocketTransport"/> class.
            Calls Dispose(false)
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.MsWebSocketConnection">
            <summary>
            Wrapper around Websocket which handles state changes.
            </summary>
        </member>
        <member name="T:IO.Ably.Transport.MsWebSocketConnection.ConnectionState">
            <summary>
            Websocket connection possible states.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.MsWebSocketConnection.ConnectionState.Connecting">
            <summary>
            Connecting.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.MsWebSocketConnection.ConnectionState.Connected">
            <summary>
            Connected.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.MsWebSocketConnection.ConnectionState.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.MsWebSocketConnection.ConnectionState.Closing">
            <summary>
            Closing.
            </summary>
        </member>
        <member name="F:IO.Ably.Transport.MsWebSocketConnection.ConnectionState.Closed">
            <summary>
            Closed.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.#ctor(System.Uri,IO.Ably.Transport.MsWebSocketOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Transport.MsWebSocketConnection"/> class.
            </summary>
            <param name="uri">Uri used for the websocket connection.</param>
            <param name="socketOptions">additional socket options.</param>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.SubscribeToEvents(System.Action{IO.Ably.Transport.MsWebSocketConnection.ConnectionState,System.Exception})">
            <summary>
            Uses the passed handler to notify about events.
            </summary>
            <param name="handler">Handler used for the notifications.</param>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.ClearStateHandler">
            <summary>
            Clears the currently saved notification handler.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.StartConnectionAsync">
            <summary>
            Start the websocket connection and the background thread used for sending messages.
            </summary>
            <returns>return a Task.</returns>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.StopConnectionAsync">
            <summary>
            Closes the websocket connection.
            </summary>
            <returns>returns a Task.</returns>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.SendText(System.String)">
            <summary>
            Sends a text message over the websocket connection.
            </summary>
            <param name="message">The text message sent over the websocket.</param>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.SendData(System.Byte[])">
            <summary>
            Sends a binary message over the websocket connection.
            </summary>
            <param name="data">The data to be sent over the websocket.</param>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.Receive(System.Action{IO.Ably.Realtime.RealtimeTransportData})">
            <summary>
            The Receive methods starts the receiving loop. It's run on a separate thread and it waits for data to become
            available on the websocket.
            </summary>
            <param name="handleMessage">The handle which is notified when a message is received.</param>
            <returns>return a Task.</returns>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.Dispose(System.Boolean)">
            <summary>
            Dispose method. Stops the send thread and disposes the websocket.
            </summary>
            <param name="disposing">Whether it should do some disposing.</param>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.Dispose">
            <summary>
            Cleans up resources and disconnects the websocket.
            </summary>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.TryGetCount(System.Int32@)">
            <summary>
            Attempt to query the backlog length of the queue.
            </summary>
            <param name="count">The (approximate) count of items in the Channel.</param>
            <returns>true if it managed to get count.</returns>
        </member>
        <member name="M:IO.Ably.Transport.MsWebSocketConnection.Finalize">
            <summary>
            Finalizes an instance of the <see cref="T:IO.Ably.Transport.MsWebSocketConnection"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.Types.AuthDetails">
            <summary>
            AuthDetails is a type used with an AUTH protocol messages to send authentication details.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.AuthDetails.AccessToken">
            <summary>
            Gets or sets the accessToken.
            </summary>
        </member>
        <member name="T:IO.Ably.Types.MessageExtras">
            <summary>
            Extra properties on the Message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.MessageExtras.Data">
            <summary>
            Data holds actual extra information associated with message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.MessageExtras.Delta">
            <summary>
            Delta extras is part of the Ably delta's functionality.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.MessageExtras.#ctor(Newtonsoft.Json.Linq.JToken)">
            <summary>
            Messages extras is a flexible object that may other properties that are not exposed by the strongly typed implementation.
            </summary>
            <param name="data">the json object passed to Message extras.</param>
        </member>
        <member name="M:IO.Ably.Types.MessageExtras.ToJson">
            <summary>
            Returns the inner message extras json object including any changes made to DeltaExtras.
            </summary>
            <returns>returns the inner json.</returns>
        </member>
        <member name="M:IO.Ably.Types.MessageExtras.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Types.MessageExtras.Equals(IO.Ably.Types.MessageExtras)">
            <summary>
            Compares two MessageExtras objects by comparing the underlying json data.
            </summary>
            <param name="other">other Message extras object.</param>
            <returns>true or false.</returns>
        </member>
        <member name="M:IO.Ably.Types.MessageExtras.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Types.DeltaExtras">
            <summary>
            Extra message properties relating to the delta's functionality.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.DeltaExtras.Format">
            <summary>
            Format.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.DeltaExtras.From">
            <summary>
            From.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.DeltaExtras.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Types.DeltaExtras"/> class.
            </summary>
            <param name="from">from parameter.</param>
            <param name="format">format parameter.</param>
        </member>
        <member name="T:IO.Ably.Types.ProtocolMessage">
            <summary>
             A message sent and received over the Realtime protocol.
             A ProtocolMessage always relates to a single channel only, but
             can contain multiple individual Messages or PresenceMessages.
             ProtocolMessages are serially numbered on a connection.
             See the Ably client library developer documentation for further
             details on the members of a ProtocolMessage.
            </summary>
        </member>
        <member name="T:IO.Ably.Types.ProtocolMessage.MessageAction">
            <summary>
            Action associated with the message.
            </summary>
        </member>
        <member name="T:IO.Ably.Types.ProtocolMessage.Flag">
            <summary>
            Message Flag sent by the server.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.ProtocolMessage.HasFlag(System.Nullable{System.Int32},IO.Ably.Types.ProtocolMessage.Flag)">
            <summary>
            Helper method to check for the existence of a flag in an integer.
            </summary>
            <param name="value">int value storing the flag.</param>
            <param name="flag">flag we check for.</param>
            <returns>true or false.</returns>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Params">
            <summary>
            Channel params is a Dictionary&lt;string, string&gt; which is used to pass parameters to the server when
            attaching a channel. Some params include `delta` and `rewind`. The server will also echo the params in the
            ATTACHED message.
            For more information https://ably.com/docs/realtime/channels/channel-parameters/overview.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.ProtocolMessage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Types.ProtocolMessage"/> class.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Action">
            <summary>
            Current message action.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Auth">
            <summary>
            <see cref="T:IO.Ably.Types.AuthDetails"/> for the current message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Flags">
            <summary>
            Current message flags.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Count">
            <summary>
            Count.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Error">
            <summary>
            Error associated with the message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Id">
            <summary>
            Ably generated message id.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Channel">
            <summary>
            Optional channel for which the message belongs to.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.ChannelSerial">
            <summary>
            Current channel serial.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.ConnectionId">
            <summary>
            Current connectionId.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.ConnectionSerial">
            <summary>
            Current connection serial.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.MsgSerial">
            <summary>
            Current message serial.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Timestamp">
            <summary>
            Timestamp of the message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Messages">
            <summary>
            List of messages contained in this protocol message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.Presence">
            <summary>
            List of presence messages contained in this protocol message.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.ProtocolMessage.ConnectionDetails">
            <summary>
            Connection details received. <see cref="T:IO.Ably.ConnectionDetails"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.ProtocolMessage.HasFlag(IO.Ably.Types.ProtocolMessage.Flag)">
            <summary>
            Convenience method to check if the current message contains a flag.
            </summary>
            <param name="flag">Flag we are looking for.</param>
            <returns>true / false.</returns>
        </member>
        <member name="M:IO.Ably.Types.ProtocolMessage.SetFlag(IO.Ably.Types.ProtocolMessage.Flag)">
            <summary>
            Convenience method to add <see cref="T:IO.Ably.Types.ProtocolMessage.Flag"/> to the Flags property.
            </summary>
            <param name="flag"><see cref="T:IO.Ably.Types.ProtocolMessage.Flag"/> to be added.</param>
        </member>
        <member name="M:IO.Ably.Types.ProtocolMessage.ToString">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.Types.SemanticVersion">
            <summary>
            SemanticVersion is a simple representation of a semantic version as defined by https://semver.org/ .
            </summary>
        </member>
        <member name="M:IO.Ably.Types.SemanticVersion.#ctor">
            <summary>
            Default construct a SemanticVersion instance setting 'Major', 'Minor' and 'Patch' to 0.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.SemanticVersion.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Construct a SemanticVersion instance using the specified 'major', 'minor' and 'patch'.
            </summary>
            <param name="major">The Major component of the semantic version.</param>
            <param name="minor">The Minor component of the semantic version.</param>
            <param name="patch">The Patch component of the semantic version.</param>
        </member>
        <member name="M:IO.Ably.Types.SemanticVersion.#ctor(System.Version)">
            <summary>
            Construct a SemanticVersion instance from a standard .NET 'Version'.  In doing this
            'Major' and 'Minor' map directly, 'SemanticVersion.Patch' maps to 'Version.Build'
            and 'Version.Revision' is discarded.
            </summary>
            <param name="version">The 'Version' to translate.</param>
        </member>
        <member name="P:IO.Ably.Types.SemanticVersion.Major">
            <summary>
            The 'Major' value set at construction.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.SemanticVersion.Minor">
            <summary>
            The 'Minor' value set at construction.
            </summary>
        </member>
        <member name="P:IO.Ably.Types.SemanticVersion.Patch">
            <summary>
            The 'Patch' value set at construction.
            </summary>
        </member>
        <member name="M:IO.Ably.Types.SemanticVersion.ToString">
            <summary>
            Return the string representation of this type conforming to that defined by https://semver.org/ .
            </summary>
            <returns>The string representation.</returns>
        </member>
        <member name="T:IO.Ably.AuthMethod">
            <summary>
            Authentication method.
            Can be Basic or Token.
            </summary>
        </member>
        <member name="F:IO.Ably.AuthMethod.Basic">
            <summary>
            The Basic auth to authenticate
            </summary>
        </member>
        <member name="F:IO.Ably.AuthMethod.Token">
            <summary>
            Uses a token to authenticate
            </summary>
        </member>
        <member name="T:IO.Ably.TokenAuthMethod">
            <summary>The library supports several token authentication methods, this enum lists those methods + descriptions.</summary>
        </member>
        <member name="T:IO.Ably.AuthOptions">
            <summary>
            Authentication options.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.AuthCallback">
            <summary>
            The callback used to get a new <see cref="T:IO.Ably.TokenDetails"/> or <see cref="T:IO.Ably.TokenRequest"/>.
            AuthCallback is used by internally by <see cref="T:IO.Ably.AblyAuth"/>.RequestTokenAsync.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.AuthUrl">
            <summary>
            A URL to query to obtain either a signed token request (<see cref="T:IO.Ably.TokenRequest"/>) or a valid <see cref="P:IO.Ably.AuthOptions.TokenDetails"/>
            This enables a client to obtain token requests from
            another entity, so tokens can be renewed without the
            client requiring access to keys.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.AuthMethod">
            <summary>
            Used in conjunction with AuthUrl. Default is GET.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.Key">
            <summary>
            Used to authenticate the client using an Ably Key.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.Token">
            <summary>
            Used to authenticate the client using a provided token.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.TokenDetails">
            <summary>
            Used to authenticate the client using a provider Token Details instance.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.AuthHeaders">
            <summary>
            Extra Auth headers.
            A set of key value pair headers to be added to any request made to the.
            Useful when an application requires these to be added to validate the request or implement the response.
            If the authHeaders object contains an authorization key,
            then withCredentials will be set on the xhr request.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.AuthParams">
            <summary>
            Extra Auth params.
            A set of key value pair params to be added to any request made to the authUrl.
            When the authMethod is GET, query params are added to the URL,
            whereas when authMethod is POST, the params are sent as URL encoded form data.
            Useful when an application require these to be added to validate the request or implement the response.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.QueryTime">
            <summary>
            Specify whether to query the server time when requesting a new token.
            </summary>
        </member>
        <member name="P:IO.Ably.AuthOptions.UseTokenAuth">
            <summary>
            Specify whether to use Token authentication.
            </summary>
        </member>
        <member name="M:IO.Ably.AuthOptions.#ctor">
            <summary>
            Initializes a new instance of the AuthOptions class.
            </summary>
        </member>
        <member name="M:IO.Ably.AuthOptions.#ctor(System.String)">
            <summary>
            Initialized a new instance of AuthOptions by populating the KeyId and KeyValue properties from the full Key.
            </summary>
            <param name="key">Full ably key string.</param>
        </member>
        <member name="M:IO.Ably.AuthOptions.Merge(IO.Ably.AuthOptions)">
            <summary>
            Merges two AuthOptions objects.
            </summary>
            <param name="defaults">second AuthOptions object.</param>
            <returns>merged AuthOptions object.</returns>
        </member>
        <member name="M:IO.Ably.AuthOptions.FromExisting(IO.Ably.AuthOptions)">
            <summary>
            Creates a new AuthOptions instance using values from an existing instance.
            </summary>
            <param name="existing">initial AuthOptions object.</param>
            <returns>copied AuthOptions object.</returns>
        </member>
        <member name="T:IO.Ably.Capability">
             <summary>
             Capability class that wraps the Ably capability string and provides a fluent interface in defining
             capability objects.
             <code>
             var capability = new Capability();
            
             capability.AddResource("second").AllowPublish();
             capability.AddResource("first").AllowAll();
            
             Assert.Equal("{ \"first\": [ \"*\" ], \"second\": [ \"publish\" ] }", capability.ToJson());
             </code>
             </summary>
        </member>
        <member name="F:IO.Ably.Capability.AllowAll">
            <summary>
            Capability to allow all actions. This is the default passed to requests where the capability is not explicitly set.
            </summary>
        </member>
        <member name="F:IO.Ably.Capability.Empty">
            <summary>
            Empty Capability object.
            </summary>
        </member>
        <member name="P:IO.Ably.Capability.Resources">
            <summary>
            A list of <see cref="T:IO.Ably.CapabilityResource"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.Capability.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Capability"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.Capability.#ctor(System.String)">
            <summary>
            Creates a capability object by parsing an ably capability string.
            </summary>
            <param name="capabilityString">Valid json capability string.</param>
        </member>
        <member name="M:IO.Ably.Capability.AddResource(System.String)">
             <summary>
             Adds a capability resource. The resource returned can be used to define the actions allowed for it by chaining the Allow methods
             Possible options are: AllowAll, AllowPublish, AllowPresence and AllowSubscribe
             A Resource can be a channel "channel" or a namespace "namespace:*". Please consult the rest documentation.
             </summary>
             <code>
             var capability = new Capability();
            
             capability.AddResource("name").AllowPublish();.
             </code>
             <param name="name">name of the resource.</param>
             <returns>CapabilityResource.</returns>
        </member>
        <member name="M:IO.Ably.Capability.ToJson">
            <summary>
            Returns the Ably capability json based on the current object.
            </summary>
            <returns>json representation of a capability object.</returns>
        </member>
        <member name="M:IO.Ably.Capability.ToString">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Capability.Equals(IO.Ably.Capability)">
            <summary>
            Equals for comparing 2 different Capability objects.
            </summary>
            <param name="other">other Capability object.</param>
            <returns>true / false.</returns>
        </member>
        <member name="M:IO.Ably.Capability.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Capability.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.CapabilityResource">
            <summary>
            Describes a CapabilityResource.
            </summary>
        </member>
        <member name="M:IO.Ably.CapabilityResource.#ctor(System.String)">
            <summary>
            Initializes a new instance and assigns a name.
            </summary>
            <param name="name">name of the resource.</param>
        </member>
        <member name="M:IO.Ably.CapabilityResource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.CapabilityResource"/> class.
            </summary>
        </member>
        <member name="P:IO.Ably.CapabilityResource.Name">
            <summary>
            Name of the resource.
            </summary>
        </member>
        <member name="P:IO.Ably.CapabilityResource.AllowedOperations">
            <summary>
            List of allowed operations.
            </summary>
        </member>
        <member name="P:IO.Ably.CapabilityResource.AllowsAll">
            <summary>
            Does the current resource allow all operations.
            </summary>
        </member>
        <member name="M:IO.Ably.CapabilityResource.AllowAll">
            <summary>
            Adds the All(*) operation to the list of allowed operations.
            Unlike the other Capability helpers it doesn't return this
            because there is no need to add any more operations once everything is allowed.
            </summary>
        </member>
        <member name="M:IO.Ably.CapabilityResource.AllowPresence">
            <summary>
            Allows the Presence operation.
            </summary>
            <returns>the current instance so more Allow methods can be chained.</returns>
        </member>
        <member name="M:IO.Ably.CapabilityResource.AllowSubscribe">
            <summary>
            Allows the Subscribe operation.
            </summary>
            <returns>the current instance so more Allow methods can be chained.</returns>
        </member>
        <member name="M:IO.Ably.CapabilityResource.AllowPublish">
            <summary>
            Allows the Publish operation.
            </summary>
            <returns>the current instance so more Allow methods can be chained.</returns>
        </member>
        <member name="T:IO.Ably.CapabilityResource.AllowedOps">
            <summary>
            Describes all the allowed operations.
            </summary>
        </member>
        <member name="F:IO.Ably.CapabilityResource.AllowedOps.Publish">
            <summary>
            Publish operation.
            </summary>
        </member>
        <member name="F:IO.Ably.CapabilityResource.AllowedOps.Subscribe">
            <summary>
            Subscribe operation.
            </summary>
        </member>
        <member name="F:IO.Ably.CapabilityResource.AllowedOps.Presence">
            <summary>
            Presence operation.
            </summary>
        </member>
        <member name="F:IO.Ably.CapabilityResource.AllowedOps.All">
            <summary>
            All operations.
            </summary>
        </member>
        <member name="T:IO.Ably.CipherParams">
             <summary>
             A class encapsulating the client-specifiable parameters for
             the cipher.
            
             algorithm is the name of the algorithm in the default system provider,
             or the lower-cased version of it; eg "aes" or "AES".
            
             Clients may instance a CipherParams directly and populate it, or may
             query the implementation to obtain a default system CipherParams.
            
             </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.Algorithm">
            <summary>
            Algorithm.
            </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.Key">
            <summary>
            Encryption key.
            </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.Iv">
            <summary>
            Encryption Iv.
            </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.Mode">
            <summary>
            Cipher mode.
            </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.KeyLength">
            <summary>
            Length of the specified key.
            Returns: 0 if there is no key.
            </summary>
        </member>
        <member name="P:IO.Ably.CipherParams.CipherType">
            <summary>
            Concatenated string of Algorithm-KeyLength-Mode.
            </summary>
        </member>
        <member name="M:IO.Ably.CipherParams.#ctor(System.Byte[])">
            <summary>
            Obtain a default CipherParams. This uses default algorithm (AES), mode and
            padding and initialises a key based on the given key data.
            </summary>
            <param name="key">encryption key.</param>
        </member>
        <member name="M:IO.Ably.CipherParams.#ctor(System.String,System.Byte[],System.Nullable{IO.Ably.Encryption.CipherMode},System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.CipherParams"/> class by passing all
            parameters, algorithm, key, mode and iv.
            </summary>
            <param name="algorithm">encryption algorithm.</param>
            <param name="key">key.</param>
            <param name="mode">mode.</param>
            <param name="iv">iv.</param>
        </member>
        <member name="M:IO.Ably.CipherParams.#ctor(System.String,System.String,IO.Ably.Encryption.CipherMode,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.CipherParams"/> class by passing all
            parameters, algorithm, base64 encoded key, mode and base64 encoded iv.
            </summary>
            <param name="algorithm">encryption algorithm.</param>
            <param name="base64Key">base64 encoded key.</param>
            <param name="mode">mode.</param>
            <param name="base64Iv">base64 encoded iv.</param>
        </member>
        <member name="M:IO.Ably.CipherParams.Equals(IO.Ably.CipherParams)">
            <summary>
            Equals method comparing two CipherParams objects.
            </summary>
            <param name="other">second CipherParams object.</param>
            <returns>true or false.</returns>
        </member>
        <member name="M:IO.Ably.CipherParams.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.CipherParams.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.IEventEmitter`2">
            <summary>
            An interface exposing the ability to register listeners for a class of events.
            </summary>
            <typeparam name="TEvent">Type of event.</typeparam>
            <typeparam name="TArgs">Type of arguments passed.</typeparam>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Off">
            <summary>
            Unregisters all event listeners.
            </summary>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.On(System.Action{`1})">
            <summary>
            Register a given listener for all events.
            </summary>
            <param name="listener">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Once(System.Action{`1})">
            <summary>
            Register a given listener for a single occurrence of a single event.
            </summary>
            <param name="listener">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Off(System.Action{`1})">
            <summary>
            Unregister the passed listener.
            </summary>
            <param name="listener">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.On(`0,System.Action{`1})">
            <summary>
            Register the given listener for a given event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Once(`0,System.Action{`1})">
            <summary>
            Register the given listener for a single occurence of a single event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Off(`0,System.Action{`1})">
            <summary>
            Unregister a given listener for a single event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.On(System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Register a given listener  for all events.
            </summary>
            <param name="listener">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Once(System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Register a given listener for a single occurrence of a single event.
            </summary>
            <param name="listener">listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Off(System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Unregister the passed listener.
            </summary>
            <param name="listener">async listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.On(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Register the given listener for a given event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">async listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Once(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Register the given listener for a single occurence of a single event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">async listener function.</param>
        </member>
        <member name="M:IO.Ably.IEventEmitter`2.Off(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <summary>
            Unregister a given listener for a single event.
            </summary>
            <param name="state">the event to listen for.</param>
            <param name="action">async listener function.</param>
        </member>
        <member name="T:IO.Ably.EventEmitter`2">
            <summary>
            Abstract class that allows other classes to implement the IEventEmitter interface.
            </summary>
            <typeparam name="TState">Type of Event.</typeparam>
            <typeparam name="TArgs">Type of args passed to the listeners.</typeparam>
        </member>
        <member name="P:IO.Ably.EventEmitter`2.NotifyClient">
            <summary>
            Used to delegate notifying the clients.
            </summary>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Off">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.On(System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.On(System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Once(System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Once(System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Off(System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Off(System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.On(`0,System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.On(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Once(`0,System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Once(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Off(`0,System.Action{`1})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Off(`0,System.Func{`1,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Emit(`0,`1)">
            <summary>
            Protected method that Emits an event and calls all registered listeners that match.
            </summary>
            <param name="state">the event emitted.</param>
            <param name="data">the data arguments passed to the listeners.</param>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Dispose(System.Boolean)">
            <summary>
            Dispose(bool disposing) executes in two distinct scenarios. If disposing equals true, the method has
            been called directly or indirectly by a user's code. Managed and unmanaged resources can be disposed.
            If disposing equals false, the method has been called by the runtime from inside the finalizer and
            you should not referenceother objects. Only unmanaged resources can be disposed.
            </summary>
            <param name="disposing">Refer to the summary.</param>
        </member>
        <member name="M:IO.Ably.EventEmitter`2.Dispose">
            <summary>
            Implement IDisposable.
            </summary>
        </member>
        <member name="T:IO.Ably.TaskExtensions">
            <summary>
            Contains Extension methods working with Task objects.
            </summary>
        </member>
        <member name="M:IO.Ably.TaskExtensions.WaitAndUnwrapException(System.Threading.Tasks.Task)">
            <summary>
            Waits for the task to complete, unwrapping any exceptions.
            </summary>
            <param name="task">The task. May not be <c>null</c>.</param>
        </member>
        <member name="M:IO.Ably.TaskExtensions.MapAsync``2(System.Threading.Tasks.Task{``0},System.Func{``0,``1})">
            <summary>
            Helps chain results that return a Task without having to await the first one.
            </summary>
            <typeparam name="T">Type of the input Task.</typeparam>
            <typeparam name="TResult">Type of the returned result.</typeparam>
            <param name="start">The first task.</param>
            <param name="mapFunction">The function used to map to the resulting type.</param>
            <returns>return Task of TResult.</returns>
        </member>
        <member name="M:IO.Ably.TaskExtensions.TimeoutAfter``1(System.Threading.Tasks.Task{``0},System.TimeSpan,``0)">
            <summary>
            Helps timeout an async execution after a specified period.
            </summary>
            <typeparam name="T">Type of Task.</typeparam>
            <param name="task">The task we want to timeout.</param>
            <param name="timeout">The period after which the timeout will occur.</param>
            <param name="timeoutResult">The value returned if the timeout occurs.</param>
            <returns>will either return the actual result if the tasks executes in time or the value provided by <paramref name="timeoutResult"/>.</returns>
        </member>
        <member name="T:IO.Ably.HttpUtility">
            <summary>
            Provides Http helper methods.
            </summary>
        </member>
        <member name="T:IO.Ably.HttpValueCollection">
            <summary>
            Class to a querystring as a list of name / value pairs.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpValueCollection.AllKeys">
            <summary>
            All the keys.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpValueCollection.Item(System.String)">
            <summary>
            Returns the value for a specific name.
            </summary>
            <param name="name">name of the requested value.</param>
            <returns>the value for the requested name or null if it doesn't exist.</returns>
        </member>
        <member name="M:IO.Ably.HttpValueCollection.GetValues(System.String)">
            <summary>
            Returns a list of all the values for a specific name.
            </summary>
            <param name="name">name of the requested value(s).</param>
            <returns>the value(s) as an array.</returns>
        </member>
        <member name="M:IO.Ably.HttpValueCollection.Add(System.String,System.String)">
            <summary>
            Adds a name / value pair.
            </summary>
            <param name="name">name of value.</param>
            <param name="value">value.</param>
        </member>
        <member name="M:IO.Ably.HttpValueCollection.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.HttpValueCollection"/> class.
            </summary>
            <param name="query">initialise with a query string.</param>
            <param name="urlencoded">is the query url encoded.</param>
        </member>
        <member name="M:IO.Ably.HttpValueCollection.ToQueryString">
            <summary>
            For internal testing only.
            This method does not URL encode and should be considered unsafe for general use.
            </summary>
            <returns>returns query string value based on the contents of the object.</returns>
        </member>
        <member name="T:IO.Ably.IChannels`1">
            <summary>
            Interface for managing channel objects.
            </summary>
            <typeparam name="T">type of channel (RealtimeChannel or RestChannel).</typeparam>
        </member>
        <member name="M:IO.Ably.IChannels`1.Get(System.String)">
            <summary>
            Create or retrieve a channel with the specified name.
            </summary>
            <param name="name">name of the channel.</param>
            <returns>an instance of <see cref="T:IO.Ably.Rest.RestChannel"/>.</returns>
        </member>
        <member name="M:IO.Ably.IChannels`1.Get(System.String,IO.Ably.ChannelOptions)">
            <summary>
            Create or retrieve a channel with the specified name and options
            If the channel already exists the channel's options are updated and
            the channel is reattached if the new options contain Modes or Params.
            </summary>
            <param name="name">name of the channel.</param>
            <param name="options"><see cref="T:IO.Ably.ChannelOptions"/>.</param>
            <returns>an instance of <see cref="T:IO.Ably.Rest.RestChannel"/>.</returns>
        </member>
        <member name="P:IO.Ably.IChannels`1.Item(System.String)">
            <summary>
            Same as the Get(string name)/>.
            </summary>
            <param name="name">name of the channel.</param>
            <returns>an instance of <see cref="T:IO.Ably.Rest.RestChannel"/>.</returns>
        </member>
        <member name="M:IO.Ably.IChannels`1.Release(System.String)">
            <summary>
            Removes a specified channel from the Channels collection.
            </summary>
            <param name="name">name of the channel.</param>
            <returns>true if success and false if Channel was not found.</returns>
        </member>
        <member name="M:IO.Ably.IChannels`1.ReleaseAll">
            <summary>
            Removes and disposes all channels.
            </summary>
        </member>
        <member name="M:IO.Ably.IChannels`1.Exists(System.String)">
            <summary>
            Checks if a channel exist.
            </summary>
            <param name="name">name of channel.</param>
            <returns>true / false.</returns>
        </member>
        <member name="T:IO.Ably.IoC">
            <summary>This class initializes dynamically-injected platform dependencies.</summary>
        </member>
        <member name="M:IO.Ably.IoC.#cctor">
            <summary>Load AblyPlatform.dll, instantiate AblyPlatform.PlatformImpl type.</summary>
        </member>
        <member name="T:IO.Ably.IPlatform">
            <summary>
            This interface is implemented for each platform .NETFramework, NetStandard,
            iOS and Android. The library dynamically creates an instance of Platform in
            IoC.cs. It lets us deal with the differences in the various platforms.
            </summary>
        </member>
        <member name="M:IO.Ably.IPlatform.RegisterOsNetworkStateChanged">
            <summary>
            This method when implemented in each Platform class includes logic to subscribe to
            NetworkStatus changes from the operating system. It is then exposed through
            IoC.RegisterOsNetworkStateChanged to the rest of the library and should be called
            when the Realtime library is initialized only if the ClientOption `AutomaticNetworkStateMonitoring`
            is set to true.
            The implementation will only allow one registration to operating system network state events even
            thought this method can be called multiple times.
            </summary>
        </member>
        <member name="T:IO.Ably.IRealtimeClient">
            <summary>
            The top-level interface for the Ably Realtime library.
            </summary>
        </member>
        <member name="M:IO.Ably.IRealtimeClient.Connect">
            <summary>
            Initiate a connection.
            </summary>
        </member>
        <member name="M:IO.Ably.IRealtimeClient.Close">
            <summary>
                This simply calls connection.close. Causes the connection to close, entering the closed state. Once
                closed, the library will not attempt to re-establish the connection without a call to connect().
            </summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.Auth">
            <summary>
            Gets the initialised Auth.
            </summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.Connection">
            <summary>A reference to the connection object for this library instance.</summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.Push">
            <summary>
            Methods for managing Push notifications subscriptions and device registrations.
            </summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.ClientId">
            <summary>
            Current client id.
            </summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.Channels">
            <summary>The collection of channels instanced, indexed by channel name.</summary>
        </member>
        <member name="P:IO.Ably.IRealtimeClient.Device">
            <summary>
            The local device instance represents the current state of the device in respect of it being a target for push notifications.
            </summary>
        </member>
        <member name="M:IO.Ably.IRealtimeClient.TimeAsync">
            <summary>
            Retrieves the ably service time.
            </summary>
            <returns>returns current server time as DateTimeOffset.</returns>
        </member>
        <member name="T:IO.Ably.JsonHelper">
            <summary>
            Public helper class for serialising and deserialising
            json using Ably's specific converters for DateTimeOffset, TimeSpan and Capability.
            </summary>
        </member>
        <member name="M:IO.Ably.JsonHelper.Serialize(System.Object)">
            <summary>
            Serialise an object to json.
            </summary>
            <param name="obj">Object to be serialised.</param>
            <returns>json string.</returns>
        </member>
        <member name="M:IO.Ably.JsonHelper.Deserialize``1(System.String)">
            <summary>
            Deserialise a json string to an object of type T.
            </summary>
            <typeparam name="T">type of object.</typeparam>
            <param name="json">input json string.</param>
            <returns>deserialised object of type T.</returns>
        </member>
        <member name="M:IO.Ably.JsonHelper.Deserialize(System.String)">
            <summary>
            Deserialise a json string to an object.
            </summary>
            <param name="json">input json string.</param>
            <returns>deserialised object.</returns>
        </member>
        <member name="M:IO.Ably.JsonHelper.DeserializeObject``1(Newtonsoft.Json.Linq.JObject)">
            <summary>
            Convert a JObject to an object of type T.
            </summary>
            <typeparam name="T">Type of object to deserialise to.</typeparam>
            <param name="obj">input JObject.</param>
            <returns>object of type T.</returns>
        </member>
        <member name="T:IO.Ably.TaskConstants">
            <summary>
            Provides completed Task constants.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.BooleanTrue">
            <summary>
            Gets a task that has been completed with the value <c>true</c>.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.BooleanFalse">
            <summary>
            Gets a task that has been completed with the value <c>false</c>.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.Int32Zero">
            <summary>
            Gets a task that has been completed with the value <c>0</c>.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.Int32NegativeOne">
            <summary>
            Gets a task that has been completed with the value <c>-1</c>.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.Completed">
            <summary>
            Gets a <see cref="T:System.Threading.Tasks.Task"/> that has been completed.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.Never">
            <summary>
            Gets a <see cref="T:System.Threading.Tasks.Task"/> that will never complete.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants.Canceled">
            <summary>
            Gets a task that has been canceled.
            </summary>
        </member>
        <member name="T:IO.Ably.TaskConstants`1">
            <summary>
            Provides completed task constants.
            </summary>
            <typeparam name="T">The type of the task result.</typeparam>
        </member>
        <member name="P:IO.Ably.TaskConstants`1.Default">
            <summary>
            Gets a task that has been completed with the default value of <typeparamref name="T"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants`1.Never">
            <summary>
            Gets a <see cref="T:System.Threading.Tasks.Task"/> that will never complete.
            </summary>
        </member>
        <member name="P:IO.Ably.TaskConstants`1.Canceled">
            <summary>
            Gets a task that has been canceled.
            </summary>
        </member>
        <member name="T:IO.Ably.ClientOptions">
            <summary>
            Options: Ably library options for REST and Realtime APIs.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.AutoConnect">
            <summary>
            If false, suppresses the automatic initiation of a connection when the library is instanced.
            Default: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.ClientId">
            <summary>
            The id of the client represented by this instance. The clientId is relevant
            to presence operations, where the clientId is the principal identifier of the
            client in presence update messages. The clientId is also relevant to
            authentication; a token issued for a specific client may be used to authenticate
            the bearer of that token to the service.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.DefaultTokenParams">
            <summary>
            When a TokenParams object is provided, it will override
            the client library defaults described in TokenParams
            Spec: TO3j11.
            Default: null.
            </summary>
        </member>
        <member name="M:IO.Ably.ClientOptions.GetClientId">
            <summary>
            Used internally for getting clientId from options or DefaultTokenParams.
            </summary>
            <returns>clientId.</returns>
        </member>
        <member name="P:IO.Ably.ClientOptions.QueueMessages">
            <summary>
            If <c>false</c>, this disables the default behaviour whereby the library queues messages on a
            connection in the disconnected or connecting states. The default behaviour allows applications
            to submit messages immediately upon instancing the library, without having to wait for the
            connection to be established. Applications may use this option to disable that behaviour if they
            wish to have application-level control over the queueing under those conditions.
            Default: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.EchoMessages">
            <summary>
            If <c>false</c>, prevents messages originating from this connection being echoed back on the same
            connection.
            Default: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.Recover">
            <summary>
            A connection recovery string, specified by a client when initializing the library
            with the intention of inheriting the state of an earlier connection. See the Ably
            Realtime API documentation for further information on connection state recovery.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.LogLevel">
            <summary>
            Log level; controls the level of verbosity of log messages from the library.
            Default: Defaults.DefaultLogLevel. Which means the log level is set to Warning.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.LogHandler">
            <summary>
            Log handler: allows the client to intercept log messages and handle them in a client-specific way.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.RestHost">
            <summary>
            For development environments only. Allows a non default host for the rest service.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.RealtimeHost">
            <summary>
            For development environments only. Allows a non default host for the realtime service.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.FallbackHosts">
            <summary>
            Gets or sets an array of custom Fallback hosts to be (optionally) used in place of the defaults.
            If an empty array is specified, then fallback host functionality is disabled.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.FallbackHostsUseDefault">
            <summary>
            Gets or sets a value indicating whether to use default FallbackHosts even when overriding
            environment or restHost/realtimeHost.
            It will be removed in the next version of the library.
            Default: false.
            </summary>
        </member>
        <member name="M:IO.Ably.ClientOptions.FullRestHost">
            <summary>
            Used for getting default/provided RestHost.
            </summary>
            <returns>RestHost.</returns>
        </member>
        <member name="M:IO.Ably.ClientOptions.FullRealtimeHost">
            <summary>
            Used for getting default/provided RealtimeHost.
            </summary>
            <returns>RealtimeHost.</returns>
        </member>
        <member name="M:IO.Ably.ClientOptions.GetFallbackHosts">
            <summary>
            Used for getting default/provided FallbackHosts.
            </summary>
            <returns>FallbackHosts.</returns>
        </member>
        <member name="P:IO.Ably.ClientOptions.Port">
            <summary>
            For development environments only; allows a non-default Ably port to be specified.
            Default: 80.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.Tls">
            <summary>
            Encrypted transport: if true, TLS will be used for all connections (whether REST/HTTP
            or Realtime WebSocket or Comet connections).
            Default: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.TlsPort">
            <summary>
            Allows non-default Tls port to be specified.
            Default: 443.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.UseBinaryProtocol">
            <summary>
            If false, forces the library to use the JSON encoding for REST and Realtime operations,
            If true, the MsgPack binary format is used (if available in the current build
            Default: false.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.DisconnectedRetryTimeout">
            <summary>
            Number of seconds before the library will retry after a disconnect.
            Default: 15s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.SuspendedRetryTimeout">
            <summary>
            Number of seconds before the library will retry after reaching Suspended state.
            Default: 30s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.ChannelRetryTimeout">
            <summary>
            Number of seconds before a channel will try to reattach itself after becoming suspended.
            Default: 15s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.HttpOpenTimeout">
            <summary>
            Timeout for opening an http request.
            Default: 4s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.HttpRequestTimeout">
            <summary>
            Timeout for completing an http request.
            Default: 10s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.FallbackRetryTimeout">
            <summary>
            After a failed request to the default endpoint, followed by a successful request to a fallback endpoint),
            the period in milliseconds before HTTP requests are retried against the default endpoint.
            Default: 10 minutes.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.HttpMaxRetryCount">
            <summary>
            Maximum number of Http retries the library will attempt.
            Default: 3.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.HttpMaxRetryDuration">
            <summary>
            Duration for which the library will retry an http request.
            Default: 15s.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.ChannelDefaults">
            <summary>
            Provides Channels Setting for all Channels created. For more information see <see cref="T:IO.Ably.ChannelOptions"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.Environment">
            <summary>
            For development; allows to specify a non production environment.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.TransportFactory">
            <summary>
            Allows the user to override the Websocket Transport Factory and provide their own implementation.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.IdempotentRestPublishing">
            <summary>
            Switched on IdempotentRest publishing. Currently switched off by default in libraries with version less than 1.2.
            Default before 1.2: false.
            Default from 1.2: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.TransportParams">
            <summary>
            Additional parameters to be sent in the querystring when initiating a realtime connection.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.CaptureCurrentSynchronizationContext">
            <summary>
            Useful where you want to execute callbacks on the main/UI thread instead of background thread in UI based apps.
            Allows developers to capture their Current SynchronizationContext and trigger handlers and emitters on the same.
            Default: false.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.CustomContext">
            <summary>
            Useful where you want to execute callbacks on the main/UI thread instead of background thread in UI based apps.
            Allows developers to provide their Thread Synchronization Context to be used when triggering handlers and emitters.
            This is majorly useful in unity, xamarin and MAUI apps.
            Default: null.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.AutomaticNetworkStateMonitoring">
            <summary>
            Allows developers to control Automatic network state monitoring. When set to `false` the library will not subscribe to
            `NetworkChange.NetworkAvailabilityChanged` events. Developers can manually notify the Connection for changes by calling
            `Connection.NotifyOperatingSystemNetworkState(NetworkState.Online or NetworkState.Offline)`.
            Unity and some Mono environments don't have `NetworkChange.NetworkAvailabilityChanged` event implemented and
            which used to prevent the library from initialising.
            Default: true.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.HeartbeatMonitorDelay">
            <summary>
            Allows developers to control how often (in milliseconds) the heartbeat is checked to determine if the server
            connection has been lost.
            Defaults: 1000.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.AddRequestIds">
            <summary>
            If enabled, every REST request to Ably includes a `request_id` query string parameter.
            This request ID remains the same if a request is retried to a fallback host.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.PushAdminFullWait">
            <summary>
            It tells Ably push REST requests to fully wait for all their effects before responding.
            Default: false.
            </summary>
        </member>
        <member name="P:IO.Ably.ClientOptions.Agents">
            <summary>
            Map of agents that will be appended to the internal ably agent headers.
            This should only be used by Ably-authored SDKs.
            If you need to use this then you have to add the agent to the agents.json file:
            https://github.com/ably/ably-common/blob/main/protocol/agents.json
            The keys represent agent names and its corresponding values represent agent versions.
            RSC7d6.
            </summary>
        </member>
        <member name="M:IO.Ably.ClientOptions.#ctor">
            <summary>
            Default constructor for ClientOptions.
            </summary>
        </member>
        <member name="M:IO.Ably.ClientOptions.#ctor(System.String)">
            <summary>
            Construct ClientOptions class and set the Key
            It automatically parses the key to ensure the correct format is used and sets the KeyId and KeyValue properties.
            </summary>
            <param name="key">Ably authentication key.</param>
        </member>
        <member name="T:IO.Ably.StatsIntervalGranularity">
            <summary>
            Stats Granularity enum.
            </summary>
        </member>
        <member name="F:IO.Ably.StatsIntervalGranularity.Minute">
            <summary>
            Minute
            </summary>
        </member>
        <member name="F:IO.Ably.StatsIntervalGranularity.Hour">
            <summary>
            Hour.
            </summary>
        </member>
        <member name="F:IO.Ably.StatsIntervalGranularity.Day">
            <summary>
            Day.
            </summary>
        </member>
        <member name="F:IO.Ably.StatsIntervalGranularity.Month">
            <summary>
            Month
            </summary>
        </member>
        <member name="T:IO.Ably.StatsRequestParams">
            <summary>
            Stats query. Allows you query for application statistics.
            </summary>
        </member>
        <member name="P:IO.Ably.StatsRequestParams.Unit">
            <summary>
            Define how the stats will be aggregated and presented.
            </summary>
        </member>
        <member name="M:IO.Ably.StatsRequestParams.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.StatsRequestParams"/> class.
            </summary>
        </member>
        <member name="F:IO.Ably.Defaults.DefaultLogLevel">
            <summary>The default log level you'll see in the debug output.</summary>
        </member>
        <member name="T:IO.Ably.Encryption.CipherMode">
            <summary>Specifies the block cipher mode to use for encryption.</summary>
        </member>
        <member name="F:IO.Ably.Encryption.CipherMode.CBC">
            <summary>
            The Cipher Block Chaining (CBC) mode introduces feedback. Before each plain text
            block is encrypted, it is combined with the cipher text of the previous block
            by a bitwise exclusive OR operation. This ensures that even if the plain text
            contains many identical blocks, they will each encrypt to a different cipher
            text block. The initialization vector is combined with the first plain text block
            by a bitwise exclusive OR operation before the block is encrypted. If a single
            bit of the cipher text block is mangled, the corresponding plain text block will
            also be mangled. In addition, a bit in the subsequent block, in the same position
            as the original mangled bit, will be mangled.
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.CipherMode.ECB">
            <summary>
            The Electronic Codebook (ECB) mode encrypts each block individually. Any blocks
            of plain text that are identical and in the same message, or that are in a different
            message encrypted with the same key, will be transformed into identical cipher
            text blocks. Important: This mode is not recommended because it opens the door
            for multiple security exploits. If the plain text to be encrypted contains substantial
            repetition, it is feasible for the cipher text to be broken one block at a time.
            It is also possible to use block analysis to determine the encryption key. Also,
            an active adversary can substitute and exchange individual blocks without detection,
            which allows blocks to be saved and inserted into the stream at other points
            without detection.
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.CipherMode.OFB">
            <summary>
            The Output Feedback (OFB) mode processes small increments of plain text into
            cipher text instead of processing an entire block at a time. This mode is similar
            to CFB; the only difference between the two modes is the way that the shift register
            is filled. If a bit in the cipher text is mangled, the corresponding bit of plain
            text will be mangled. However, if there are extra or missing bits from the cipher
            text, the plain text will be mangled from that point on.
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.CipherMode.CFB">
            <summary>
            The Cipher Feedback (CFB) mode processes small increments of plain text into
            cipher text, instead of processing an entire block at a time. This mode uses
            a shift register that is one block in length and is divided into sections. For
            example, if the block size is 8 bytes, with one byte processed at a time, the
            shift register is divided into eight sections. If a bit in the cipher text is
            mangled, one plain text bit is mangled and the shift register is corrupted. This
            results in the next several plain text increments being mangled until the bad
            bit is shifted out of the shift register. The default feedback size can vary
            by algorithm, but is typically either 8 bits or the number of bits of the block
            size. You can alter the number of feedback bits by using the System.Security.Cryptography.SymmetricAlgorithm.FeedbackSize
            property. Algorithms that support CFB use this property to set the feedback.
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.CipherMode.CTS">
            <summary>
            The Cipher Text Stealing (CTS) mode handles any length of plain text and produces
            cipher text whose length matches the plain text length. This mode behaves like
            the CBC mode for all but the last two blocks of the plain text.
            </summary>
        </member>
        <member name="T:IO.Ably.Encryption.Crypto">
             <summary>
                 Utility classes for message payload encryption.
            
             This class supports AES/CBC with a default key length of 256 bits
             but supporting other key lengths.Other algorithms and chaining modes are
             not supported directly, but supportable by extending/implementing the base
             classes and interfaces here.
            
             Secure random data for creation of Initialisation Vectors (IVs) and keys
             is obtained from the default system.
            
             Each message payload is encrypted with an IV in CBC mode, and the IV is
             concatenated with the resulting raw ciphertext to construct the "ciphertext"
             data passed to the recipient.
             </summary>
        </member>
        <member name="F:IO.Ably.Encryption.Crypto.DefaultAlgorithm">
            <summary>
            Default Encryption algorithm. (AES).
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.Crypto.DefaultKeylength">
            <summary>
            Default key length (256).
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.Crypto.DefaultBlocklength">
            <summary>
            Default block length (16).
            </summary>
        </member>
        <member name="F:IO.Ably.Encryption.Crypto.DefaultMode">
            <summary>
            Default CipherMode (CBC).
            </summary>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GetDefaultParams(System.String,System.String,System.Nullable{IO.Ably.Encryption.CipherMode})">
            <summary>
            Gets default CipherParams based on a base64EncodedKey.
            </summary>
            <param name="base64EncodedKey">required at least base64 encoded key.</param>
            <param name="base64Iv">optional base64Encoded Iv.</param>
            <param name="mode">optional Cipher mode.</param>
            <returns>CipherParams.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GetDefaultParams(System.Byte[],System.Byte[],System.Nullable{IO.Ably.Encryption.CipherMode})">
            <summary>
            Gets default params and generates a random key if not supplied.
            </summary>
            <param name="key">optional; key.</param>
            <param name="iv">optional; iv.</param>
            <param name="mode">optional: CipherMode.</param>
            <returns>CipherParams.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GetCipher(IO.Ably.CipherParams)">
            <summary>
            Gets a ChannelCipher for given cipher params. Currently supports only AES.
            </summary>
            <param name="cipherParams">cipher params.</param>
            <exception cref="T:IO.Ably.AblyException">throws if the encryption algorithm is different from AES.</exception>
            <returns>a computed channel cipher.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.ComputeHMacSha256(System.String,System.String)">
            <summary>
            Computes HMacSha256 hash.
            </summary>
            <param name="text">Text to be hashed.</param>
            <param name="key">Key used for hashing.</param>
            <returns>HMacSha256 hash.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GenerateRandomKey(System.Nullable{System.Int32},System.Nullable{IO.Ably.Encryption.CipherMode})">
            <summary>
            Given a keyLength and Cipher mode, generates a random key.
            </summary>
            <param name="keyLength">128 or 256 bit keys are supporter.</param>
            <param name="mode">optional Cipher mode.</param>
            <returns>returns generated encryption key.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GetRandomMessageId">
            <summary>
            Generates a cryptographically random message id.
            </summary>
            <returns>base64 encoded random array of 9 bytes.</returns>
        </member>
        <member name="M:IO.Ably.Encryption.Crypto.GenerateSecret">
            <summary>
            Generates a random SHA256 hash and returns a Base64 encoded string.
            </summary>
            <returns>Returns a random string.</returns>
        </member>
        <member name="T:IO.Ably.DateExtensions">
            <summary>
            Extension methods working with dates.
            </summary>
        </member>
        <member name="F:IO.Ably.DateExtensions.Epoch">
            <summary>
            Unix Epoch.
            </summary>
        </member>
        <member name="M:IO.Ably.DateExtensions.FromUnixTimeInMilliseconds(System.Int64)">
            <summary>
            Converts Unix time in milliseconds to a DateTimeOffset object.
            </summary>
            <param name="unixTime">The unix time.</param>
            <returns>returns corresponding DateTimeOffset object.</returns>
        </member>
        <member name="M:IO.Ably.DateExtensions.ToUnixTimeInMilliseconds(System.DateTimeOffset)">
            <summary>
            Converts a datetime offsite to a unix time in milliseconds.
            </summary>
            <param name="date">the date offset to be converted.</param>
            <returns>unix time in milliseconds.</returns>
        </member>
        <member name="M:IO.Ably.DateExtensions.TimeToString(System.TimeSpan)">
            <summary>
            Converts a Timespan to a string in the following format {0}h:{1}m:{2}s.{3}ms
            e.g. 00:03:32.8289777.
            </summary>
            <param name="timeSpan">The timespan.</param>
            <returns>the timespan as a string.</returns>
        </member>
        <member name="T:IO.Ably.DictionaryExtensions">
            <summary>
            Extension methods for Dictionary objects.
            </summary>
        </member>
        <member name="M:IO.Ably.DictionaryExtensions.ToQueryString(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Converts a dictionary of strings to a querystring.
            </summary>
            <param name="params">dictionary to convert.</param>
            <returns>returns a url encoded query string.</returns>
        </member>
        <member name="M:IO.Ably.DictionaryExtensions.Merge(System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Merges two dictionaries of strings without overwriting keys if they exist.
            </summary>
            <param name="first">first dictionary.</param>
            <param name="second">second dictionary.</param>
            <returns>returns merged dictionary.</returns>
        </member>
        <member name="T:IO.Ably.EnumerableExtensions">
            <summary>
            Enumerable extension methods.
            </summary>
        </member>
        <member name="M:IO.Ably.EnumerableExtensions.Shuffle``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Shuffles the values of a collection.
            </summary>
            <typeparam name="T">type.</typeparam>
            <param name="source">source collection.</param>
            <returns>returns a shuffled collection.</returns>
        </member>
        <member name="M:IO.Ably.EnumerableExtensions.Shuffle``1(System.Collections.Generic.IEnumerable{``0},System.Random)">
            <summary>
            Shuffles the values of a collection.
            </summary>
            <typeparam name="T">type.</typeparam>
            <param name="source">source collection.</param>
            <param name="rng">random seed.</param>
            <returns>returns a shuffled collection.</returns>
        </member>
        <member name="P:IO.Ably.AblyRequest.NoExceptionOnHttpError">
            <summary>
            Tell the HTTP client to not raise an exception when a non 2XX status is returned.
            Set to 'true' when 4XX or 5XX HTTP status codes should not cause an exception.
            Add to support AblyRest.Request(...).
            </summary>
        </member>
        <member name="T:IO.Ably.IAblyAuth">
            <summary>
            Token-generation and authentication operations for the Ably API.
            See the Ably Authentication documentation for details of the
            authentication methods available.
            </summary>
        </member>
        <member name="P:IO.Ably.IAblyAuth.ClientId">
            <summary>
            Client id for this library instance.
            Spec: RSA7b.
            </summary>
        </member>
        <member name="M:IO.Ably.IAblyAuth.RequestTokenAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Makes a token request. This will make a token request now, even if the library already
            has a valid token. It would typically be used to issue tokens for use by other clients.
            </summary>
            <param name="tokenParams">The <see cref="T:IO.Ably.TokenRequest"/> data used for the token.</param>
            <param name="authOptions">Extra <see cref="T:IO.Ably.AuthOptions"/> used for creating a token.</param>
            <returns>A valid ably token.</returns>
            <exception cref="T:IO.Ably.AblyException">something went wrong.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.AuthorizeAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Ensure valid auth credentials are present. This may rely in an already-known
            and valid token, and will obtain a new token if necessary or explicitly
            requested.
            Authorisation will use the parameters supplied on construction except
            where overridden with the options supplied in the call.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/> custom parameter. Pass null and default token request options will be generated used the options passed when creating the client.</param>
            <param name="options"><see cref="T:IO.Ably.AuthOptions"/> custom options.</param>
            <returns>Returns a valid token.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an ably exception representing the server response.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.AuthoriseAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            See <see cref="M:IO.Ably.IAblyAuth.AuthorizeAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/> custom parameter. Pass null and default token request options will be generated used the options passed when creating the client.</param>
            <param name="options"><see cref="T:IO.Ably.AuthOptions"/> custom options.</param>
            <returns>Returns a valid token.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an ably exception representing the server response.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.CreateTokenRequestAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Create a signed token request based on known credentials.
            and the given token params. This would typically be used if creating
            signed requests for submission by another client.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/>. If null a token request is generated from options passed when the client was created.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/>. If null the default AuthOptions are used.</param>
            <returns>serialized signed token request.</returns>
        </member>
        <member name="M:IO.Ably.IAblyAuth.CreateTokenRequestObjectAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Create a signed token request based on known credentials
            and the given token params. This would typically be used if creating
            signed requests for submission by another client.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/>. If null a token request is generated from options passed when the client was created.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/>. If null the default AuthOptions are used.</param>
            <returns>signed token request.</returns>
        </member>
        <member name="M:IO.Ably.IAblyAuth.RequestToken(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Sync version for <see cref="M:IO.Ably.IAblyAuth.RequestTokenAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>.
            Prefer the Async version where possible.
            </summary>
            <param name="tokenParams">The <see cref="T:IO.Ably.TokenRequest"/> data used for the token.</param>
            <param name="options">Extra <see cref="T:IO.Ably.AuthOptions"/> used for creating a token.</param>
            <returns>A valid ably token.</returns>
            <exception cref="T:IO.Ably.AblyException">something went wrong.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.Authorize(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Sync version for <see cref="M:IO.Ably.IAblyAuth.AuthorizeAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>.
            Prefer the Async version where possible.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/> custom parameter. Pass null and default token request options will be generated used the options passed when creating the client.</param>
            <param name="options"><see cref="T:IO.Ably.AuthOptions"/> custom options.</param>
            <returns>Returns a valid token.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an ably exception representing the server response.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.Authorise(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            See <see cref="M:IO.Ably.IAblyAuth.Authorize(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/> custom parameter. Pass null and default token request options will be generated used the options passed when creating the client.</param>
            <param name="options"><see cref="T:IO.Ably.AuthOptions"/> custom options.</param>
            <returns>Returns a valid token.</returns>
            <exception cref="T:IO.Ably.AblyException">Throws an ably exception representing the server response.</exception>
        </member>
        <member name="M:IO.Ably.IAblyAuth.CreateTokenRequest(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Sync version for <see cref="M:IO.Ably.IAblyAuth.CreateTokenRequestAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>
            Prefer the async version where possible.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/>. If null a token request is generated from options passed when the client was created.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/>. If null the default AuthOptions are used.</param>
            <returns>serialized signed token request.</returns>
        </member>
        <member name="M:IO.Ably.IAblyAuth.CreateTokenRequestObject(IO.Ably.TokenParams,IO.Ably.AuthOptions)">
            <summary>
            Sync version for <see cref="M:IO.Ably.IAblyAuth.CreateTokenRequestObjectAsync(IO.Ably.TokenParams,IO.Ably.AuthOptions)"/>
            Prefer the async version where possible.
            </summary>
            <param name="tokenParams"><see cref="T:IO.Ably.TokenParams"/>. If null a token request is generated from options passed when the client was created.</param>
            <param name="authOptions"><see cref="T:IO.Ably.AuthOptions"/>. If null the default AuthOptions are used.</param>
            <returns>signed token request.</returns>
        </member>
        <member name="T:IO.Ably.IChannelCipher">
            <summary>
            Extension point for implementing custom Channel Ciphers.
            Currently only AesCipher channel cipher exists.
            </summary>
        </member>
        <member name="P:IO.Ably.IChannelCipher.Algorithm">
            <summary>
            Encryption algorithm.
            </summary>
        </member>
        <member name="M:IO.Ably.IChannelCipher.Encrypt(System.Byte[])">
            <summary>
            Method to encrypt some input.
            </summary>
            <param name="input">byte array.</param>
            <returns>encrypted byte array.</returns>
        </member>
        <member name="M:IO.Ably.IChannelCipher.Decrypt(System.Byte[])">
            <summary>
            Method to decrypt some input.
            </summary>
            <param name="input">encrypted byte array.</param>
            <returns>decrypted byte array.</returns>
        </member>
        <member name="T:IO.Ably.IRestClient">
            <summary>
            Interface for a rest client.
            </summary>
        </member>
        <member name="P:IO.Ably.IRestClient.Auth">
            <summary>Authentication methods.</summary>
        </member>
        <member name="P:IO.Ably.IRestClient.Channels">
            <summary>Channel methods.</summary>
        </member>
        <member name="M:IO.Ably.IRestClient.TimeAsync">
            <summary>Retrieves the ably service time.</summary>
            <returns>DateTimeOffset of the server time.</returns>
        </member>
        <member name="M:IO.Ably.IRestClient.Time">
            <summary>
            Sync method for getting server time.
            </summary>
            <returns>DateTimeOffset of the server time.</returns>
        </member>
        <member name="P:IO.Ably.IRestClient.Device">
            <summary>
            The local device instance represents the current state of the device in respect of it being a target for push notifications.
            </summary>
        </member>
        <member name="T:IO.Ably.IStatsCommands">
            <summary>
            Defines an interface for Stats commands.
            </summary>
        </member>
        <member name="M:IO.Ably.IStatsCommands.StatsAsync">
            <summary>Retrieves the stats for the application. Passed default <see cref="T:IO.Ably.StatsRequestParams"/> for the request.</summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of <see cref="T:IO.Ably.Stats"/>.</returns>
        </member>
        <member name="M:IO.Ably.IStatsCommands.StatsAsync(IO.Ably.StatsRequestParams)">
            <summary>Retrieves the stats for the application using a more specific stats query. Check <see cref="T:IO.Ably.StatsRequestParams"/> for more information.</summary>
            <param name="query">stats query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of <see cref="T:IO.Ably.Stats"/>.</returns>
        </member>
        <member name="M:IO.Ably.IStatsCommands.Stats">
            <summary>Retrieves the stats for the application. Passed default <see cref="T:IO.Ably.StatsRequestParams"/> for the request.</summary>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of <see cref="T:IO.Ably.Stats"/>.</returns>
        </member>
        <member name="M:IO.Ably.IStatsCommands.Stats(IO.Ably.StatsRequestParams)">
            <summary>Retrieves the stats for the application. Passed default <see cref="T:IO.Ably.StatsRequestParams"/> for the request.</summary>
            <param name="query">stats query.</param>
            <returns><see cref="T:IO.Ably.PaginatedResult`1"/> of <see cref="T:IO.Ably.Stats"/>.</returns>
        </member>
        <member name="T:IO.Ably.LogLevel">
            <summary>Level of a log message.</summary>
        </member>
        <member name="F:IO.Ably.LogLevel.Debug">
            <summary>
            Verbose setting. Logs everything.
            </summary>
        </member>
        <member name="F:IO.Ably.LogLevel.Warning">
            <summary>
            Warning setting. Logs clues that something is not 100% right.
            </summary>
        </member>
        <member name="F:IO.Ably.LogLevel.Error">
            <summary>
            Error setting. Logs errors
            </summary>
        </member>
        <member name="F:IO.Ably.LogLevel.None">
            <summary>
            None setting. No logs produced
            </summary>
        </member>
        <member name="T:IO.Ably.DefaultLogger">
            <summary>An utility class for logging various messages.</summary>
        </member>
        <member name="P:IO.Ably.DefaultLogger.LogLevel">
            <summary>Maximum level to log.</summary>
            <remarks>E.g. set to LogLevel.Warning to have only errors and warnings in the log.</remarks>
        </member>
        <member name="P:IO.Ably.DefaultLogger.LoggerSink">
            <summary>
            The current LoggerSink. When the library is initialised with a LoggerSink
            this property gets set.
            </summary>
        </member>
        <member name="P:IO.Ably.DefaultLogger.IsDebug">
            <summary>
            IsDebug.
            </summary>
        </member>
        <member name="M:IO.Ably.DefaultLogger.Error(System.String,System.Exception)">
            <summary>Log an error message.</summary>
        </member>
        <member name="M:IO.Ably.DefaultLogger.Error(System.String,System.Object[])">
            <summary>Log an error message.</summary>
        </member>
        <member name="M:IO.Ably.DefaultLogger.Warning(System.String,System.Object[])">
            <summary>Log a warning message.</summary>
        </member>
        <member name="M:IO.Ably.DefaultLogger.Debug(System.String,System.Object[])">
            <summary>Log a debug message.</summary>
        </member>
        <member name="T:IO.Ably.PaginatedResult`1">
            <summary>
            Wraps any Ably HTTP response that supports paging and provides methods to iterate through the pages
            using <see cref="!:FirstAsync()"/>, <see cref="M:IO.Ably.PaginatedResult`1.NextAsync"/>, <see cref="P:IO.Ably.PaginatedResult`1.HasNext"/> and <see cref="P:IO.Ably.PaginatedResult`1.IsLast"/>.
            All items in the HTTP response are available in the IEnumerable <see cref="P:IO.Ably.PaginatedResult`1.Items"/>
            Paging information is provided by Ably in the LINK HTTP headers.
            </summary>
            <typeparam name="T">Type of items contained in the result.</typeparam>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.Limit">
            <summary>
            Limit of how many items should be returned.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.ExecuteDataQueryFunc">
            <summary>
            Executes the next request.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.Items">
            <summary>
            List that holds the actual items returned from the Ably Api.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.NextQueryParams">
            <summary>
            The <see cref="T:IO.Ably.PaginatedRequestParams"/> for the Next query.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.FirstQueryParams">
            <summary>
            The <see cref="T:IO.Ably.PaginatedRequestParams"/> for the First query.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.CurrentQueryParams">
            <summary>
            The <see cref="T:IO.Ably.PaginatedRequestParams"/> for the Current page.
            </summary>
        </member>
        <member name="M:IO.Ably.PaginatedResult`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.PaginatedResult`1"/> class.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.HasNext">
            <summary>
            Gets a value indicating whether there are further pages.
            </summary>
        </member>
        <member name="P:IO.Ably.PaginatedResult`1.IsLast">
            <summary>
            Gets a value indicating whether the current page is the last one available.
            </summary>
        </member>
        <member name="M:IO.Ably.PaginatedResult`1.NextAsync">
            <summary>
            Calls the api with the <see cref="P:IO.Ably.PaginatedResult`1.NextQueryParams"/> and returns the next page of result.
            </summary>
            <returns>returns the next page of results.</returns>
        </member>
        <member name="M:IO.Ably.PaginatedResult`1.Next">
            <summary>
            Sync version of <see cref="M:IO.Ably.PaginatedResult`1.NextAsync"/>.
            Prefer the async version of the method where possible.
            </summary>
            <returns>returns the next page of results.</returns>
        </member>
        <member name="T:IO.Ably.Protocol">
            <summary>
            Protocol used.
            </summary>
        </member>
        <member name="F:IO.Ably.Protocol.Json">
            <summary>
            Json text protocol.
            </summary>
        </member>
        <member name="T:IO.Ably.QueryDirection">
            <summary>
            Direction of the query.
            Supports Forwards | Backwards.
            </summary>
        </member>
        <member name="F:IO.Ably.QueryDirection.Forwards">
            <summary>
            Forwards.
            </summary>
        </member>
        <member name="F:IO.Ably.QueryDirection.Backwards">
            <summary>
            Backwards.
            </summary>
        </member>
        <member name="T:IO.Ably.Unit">
            <summary>
            A unit type is a type that allows only one value (and thus can hold no information)
            Original source
            https://github.com/louthy/language-ext/blob/0eb922bf9ca33944a5aa0745d791255321a4b351/LanguageExt.Core/DataTypes/Unit/Unit.cs.
            </summary>
        </member>
        <member name="F:IO.Ably.Unit.Default">
            <summary>
            Default value.
            </summary>
        </member>
        <member name="M:IO.Ably.Unit.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Unit.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:IO.Ably.Unit.ToString">
            <summary>
            Returns ().
            </summary>
            <returns>().</returns>
        </member>
        <member name="T:IO.Ably.Result">
            <summary>
            Result class representing the result of an operation.
            </summary>
        </member>
        <member name="P:IO.Ably.Result.IsSuccess">
            <summary>
            Is the operation successful.
            </summary>
        </member>
        <member name="P:IO.Ably.Result.Error">
            <summary>
            Error if the operation failed.
            </summary>
        </member>
        <member name="P:IO.Ably.Result.IsFailure">
            <summary>
            Has the operation failed.
            </summary>
        </member>
        <member name="M:IO.Ably.Result.#ctor(System.Boolean,IO.Ably.ErrorInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Result"/> class.
            </summary>
            <param name="isSuccess">true if success.</param>
            <param name="error">error if failure.</param>
            <exception cref="T:System.InvalidOperationException">for invalid parameters.</exception>
        </member>
        <member name="M:IO.Ably.Result.Fail(System.String)">
            <summary>
            Factory method to create a failed Result with message.
            </summary>
            <param name="message">error message.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:IO.Ably.Result.Fail(IO.Ably.ErrorInfo)">
            <summary>
            Factory method to create a failed Result with Error.
            </summary>
            <param name="error">Error.</param>
            <returns>true / false.</returns>
        </member>
        <member name="M:IO.Ably.Result.Fail``1(IO.Ably.ErrorInfo)">
            <summary>
            Factory method to create a failed Result of T.
            </summary>
            <typeparam name="T">Type of value.</typeparam>
            <param name="error">Error of the failure.</param>
            <returns>failed Result of T.</returns>
        </member>
        <member name="M:IO.Ably.Result.Fail``1(IO.Ably.Result)">
            <summary>
            Factory method to create a failed Result of T.
            </summary>
            <typeparam name="T">Type of value.</typeparam>
            <param name="other">Creates a failed result from another.</param>
            <returns>failed Result of T.</returns>
        </member>
        <member name="M:IO.Ably.Result.Ok">
            <summary>
            Factory method to create a successful Result.
            </summary>
            <returns>Result.</returns>
        </member>
        <member name="M:IO.Ably.Result.Ok``1(``0)">
            <summary>
            Factory method to create a successful Result of T with value.
            </summary>
            <typeparam name="T">Type of value.</typeparam>
            <param name="value">successful value held in the result.</param>
            <returns>Result.</returns>
        </member>
        <member name="M:IO.Ably.Result.Combine(IO.Ably.Result[])">
            <summary>
            Combines a number of results. If any of them has failed it returns failure otherwise
            returns a successful result.
            </summary>
            <param name="results">Results to check.</param>
            <returns>Result.</returns>
        </member>
        <member name="T:IO.Ably.Result`1">
            <summary>
            Class representing a <see cref="T:IO.Ably.Result"/> containing a value.
            </summary>
            <typeparam name="T">Type of value.</typeparam>
        </member>
        <member name="P:IO.Ably.Result`1.Value">
            <summary>
            When successful exposes the value
            held in the result object.
            </summary>
        </member>
        <member name="M:IO.Ably.Result`1.#ctor(`0,System.Boolean,IO.Ably.ErrorInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Result`1"/> class.
            </summary>
            <param name="value">value.</param>
            <param name="isSuccess">if successful.</param>
            <param name="error">Error object.</param>
        </member>
        <member name="T:IO.Ably.ResultExtension">
            <summary>
            Extension methods to help with chaining result calls.
            </summary>
        </member>
        <member name="M:IO.Ably.ResultExtension.Map``2(IO.Ably.Result{``0},System.Func{``0,``1})">
            <summary>
            Map takes a result and if successful executes the mapper function on the value.
            </summary>
            <param name="initial">Initial Result of T.</param>
            <param name="mapper">Mapper function that transforms the value.</param>
            <typeparam name="T">Initial type of the Value wrapped in the Result.</typeparam>
            <typeparam name="TResult">The new type returned from the mapper function.</typeparam>
            <returns>a new result.</returns>
        </member>
        <member name="M:IO.Ably.ResultExtension.IfSuccess``1(IO.Ably.Result{``0},System.Action{``0})">
            <summary>
            Helper method for Result of T. It will execute the provided action if
            the result is successful.
            </summary>
            <param name="result">The Result we check.</param>
            <param name="successFunc">The action that is executed when the result is successful.</param>
            <typeparam name="T">Type of Value wrapped by the Result.</typeparam>
            <returns>The original passed object to facilitate chaining.</returns>
        </member>
        <member name="M:IO.Ably.ResultExtension.IfFailure(IO.Ably.Result,System.Action{IO.Ably.ErrorInfo})">
            <summary>
            Helper method for Result. It is only executed if the result is failed and passed the ErrorInfo
            to the Action.
            </summary>
            <param name="result">the Result we check.</param>
            <param name="failAction">the action that is executed in case of a failure.</param>
            <returns>the same Result object to facilitate chaining.</returns>
        </member>
        <member name="M:IO.Ably.ResultExtension.Bind``2(IO.Ably.Result{``0},System.Func{``0,IO.Ably.Result{``1}})">
            <summary>
            Map takes a result and if successful executes the mapper function on the value.
            </summary>
            <param name="initial">Initial Result of T.</param>
            <param name="bindFunc">Bind function that takes the value and returns a new Result.</param>
            <typeparam name="T">Initial type of the Value wrapped in the Result.</typeparam>
            <typeparam name="TResult">The new type returned from the bind function.</typeparam>
            <returns>a new result.</returns>
        </member>
        <member name="T:IO.Ably.Stats">
            <summary>
            A class encapsulating a Stats datapoint.
            Ably usage information, across an account or an individual app,
            is available as Stats records on a timeline with different granularities.
            This class defines the Stats type and its subtypes, giving a structured
            representation of service usage for a specific scope and time interval.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.All">
            <summary>
            Aggregates inbound and outbound messages.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Inbound">
            <summary>
            All inbound messages.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Outbound">
            <summary>
            All outbound messages.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Persisted">
            <summary>
            Messages persisted for later retrieval via the history API.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Connections">
            <summary>
            Breakdown of connection stats data for different (TLS vs non-TLS) connection types.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Channels">
            <summary>
            Breakdown of channels stats.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.ApiRequests">
            <summary>
            Breakdown of API requests received via the REST API.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.TokenRequests">
            <summary>
            Breakdown of Token requests received via the REST API.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.IntervalId">
            <summary>
            The interval that this statistic applies to.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.IntervalGranularity">
            <summary>
            The granularity of the interval for the stat such as :day, :hour, :minute, see <see cref="T:IO.Ably.StatsIntervalGranularity"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.IntervalTime">
            <summary>
            A DateTimeOffset representing the start of the interval.
            </summary>
        </member>
        <member name="P:IO.Ably.Stats.Interval">
            <summary>
            IntervalId converted to a DateTimeOffset.
            </summary>
        </member>
        <member name="M:IO.Ably.Stats.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Stats"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.ConnectionTypes">
            <summary>
            A breakdown of summary stats data for different (tls vs non-tls)
            connection types.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionTypes.All">
            <summary>
            All connection count (includes both TLS &amp; non-TLS connections).
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionTypes.Plain">
            <summary>
            Non-TLS connection count (unencrypted).
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionTypes.Tls">
            <summary>
            TLS connection count.
            </summary>
        </member>
        <member name="M:IO.Ably.ConnectionTypes.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ConnectionTypes"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.MessageCount">
            <summary>
            MessageCount contains aggregate counts for messages and data transferred.
            </summary>
        </member>
        <member name="P:IO.Ably.MessageCount.Count">
            <summary>
            Count of all message.
            </summary>
        </member>
        <member name="P:IO.Ably.MessageCount.Data">
            <summary>
            Total data transferred for all messages in bytes.
            </summary>
        </member>
        <member name="T:IO.Ably.MessageTypes">
            <summary>
            A breakdown of summary stats data for different (message vs presence)
            message types.
            </summary>
        </member>
        <member name="P:IO.Ably.MessageTypes.All">
            <summary>
            All messages count (includes both presence &amp; messages).
            </summary>
        </member>
        <member name="P:IO.Ably.MessageTypes.Messages">
            <summary>
            Count of channel messages.
            </summary>
        </member>
        <member name="P:IO.Ably.MessageTypes.Presence">
            <summary>
            Count of presence messages.
            </summary>
        </member>
        <member name="M:IO.Ably.MessageTypes.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.MessageTypes"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.InboundMessageTraffic">
            <summary>
            A breakdown of summary stats data for traffic over various transport types.
            </summary>
        </member>
        <member name="P:IO.Ably.InboundMessageTraffic.All">
            <summary>
            All messages count (includes realtime, rest and webhook messages).
            </summary>
        </member>
        <member name="P:IO.Ably.InboundMessageTraffic.Realtime">
            <summary>
            Count of messages transferred over a realtime transport such as WebSockets.
            </summary>
        </member>
        <member name="P:IO.Ably.InboundMessageTraffic.Rest">
            <summary>
            Count of messages transferred using REST.
            </summary>
        </member>
        <member name="M:IO.Ably.InboundMessageTraffic.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.InboundMessageTraffic"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.OutboundMessageTraffic">
            <summary>
            A breakdown of summary stats data for traffic over various transport types.
            </summary>
        </member>
        <member name="P:IO.Ably.OutboundMessageTraffic.All">
            <summary>
            All messages count (includes realtime, rest and webhook messages).
            </summary>
        </member>
        <member name="P:IO.Ably.OutboundMessageTraffic.Realtime">
            <summary>
            Count of messages transferred over a realtime transport such as WebSockets.
            </summary>
        </member>
        <member name="P:IO.Ably.OutboundMessageTraffic.Rest">
            <summary>
            Count of messages transferred using REST.
            </summary>
        </member>
        <member name="P:IO.Ably.OutboundMessageTraffic.Webhook">
            <summary>
            Count of messages delivered using WebHooks.
            </summary>
        </member>
        <member name="M:IO.Ably.OutboundMessageTraffic.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.OutboundMessageTraffic"/> class.
            </summary>
        </member>
        <member name="T:IO.Ably.RequestCount">
            <summary>
            RequestCount contains aggregate counts for requests made.
            </summary>
        </member>
        <member name="P:IO.Ably.RequestCount.Succeeded">
            <summary>
            Requests succeeded.
            </summary>
        </member>
        <member name="P:IO.Ably.RequestCount.Failed">
            <summary>
            Requests failed.
            </summary>
        </member>
        <member name="P:IO.Ably.RequestCount.Refused">
            <summary>
            Requests refused typically as a result of permissions or a limit being exceeded.
            </summary>
        </member>
        <member name="T:IO.Ably.ResourceCount">
            <summary>
            Aggregate data for usage of a resource in a specific scope.
            </summary>
        </member>
        <member name="P:IO.Ably.ResourceCount.Opened">
            <summary>
            Total resources of this type opened.
            </summary>
        </member>
        <member name="P:IO.Ably.ResourceCount.Peak">
            <summary>
            Peak resources of this type used for this period.
            </summary>
        </member>
        <member name="P:IO.Ably.ResourceCount.Mean">
            <summary>
            Average resources of this type used for this period.
            </summary>
        </member>
        <member name="P:IO.Ably.ResourceCount.Min">
            <summary>
            Minimum total resources of this type used for this period.
            </summary>
        </member>
        <member name="P:IO.Ably.ResourceCount.Refused">
            <summary>
            Resource requests refused within this period.
            </summary>
        </member>
        <member name="T:IO.Ably.TokenDetails">
            <summary>
            A class providing details of a token and its associated metadata.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.Capability">
            <summary>
            The allowed capabilities for this token. <see cref="P:IO.Ably.TokenDetails.Capability"/>.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.ClientId">
            <summary>
            The clientId associated with the token.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.Expires">
            <summary>
            Absolute token expiry date in UTC.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.Issued">
            <summary>
            Date and time when the token was issued in UTC.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.Token">
            <summary>
            The token itself.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenDetails.KeyName">
            <summary>
            API key name used to create this token.
            </summary>
        </member>
        <member name="M:IO.Ably.TokenDetails.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.TokenDetails"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.TokenDetails.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.TokenDetails"/> class.
            </summary>
            <param name="token">initialise TokenDetails from a token string.</param>
        </member>
        <member name="M:IO.Ably.TokenDetails.IsToken(Newtonsoft.Json.Linq.JObject)">
            <summary>
            Checks if a json object is a token. It does it by ensuring the existence of "issued" property.
            </summary>
            <param name="json">Json object to check.</param>
            <returns>true if json object contains "issued".</returns>
        </member>
        <member name="M:IO.Ably.TokenDetails.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.TokenDetails.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.TokenDetails.ToString">
            <inheritdoc/>
        </member>
        <member name="T:IO.Ably.TokenDetailsExtensions">
            <summary>
            <see cref="T:IO.Ably.TokenDetails"/> extensions.
            </summary>
        </member>
        <member name="M:IO.Ably.TokenDetailsExtensions.IsValidToken(IO.Ably.TokenDetails,System.Nullable{System.DateTimeOffset})">
            <summary>
            Checks whether the token is valid.
            </summary>
            <param name="token"><see cref="T:IO.Ably.TokenDetails"/>.</param>
            <param name="serverTime">the server time instance of now to compare with the token.</param>
            <returns>true / false.</returns>
        </member>
        <member name="M:IO.Ably.TokenDetailsExtensions.IsExpired(IO.Ably.TokenDetails,System.DateTimeOffset)">
            <summary>
            Checks whether the <see cref="T:IO.Ably.TokenDetails"/> are valid.
            </summary>
            <param name="token"><see cref="T:IO.Ably.TokenDetails"/>.</param>
            <param name="now">the correct instance of now to compare with the token.</param>
            <returns>true / false.</returns>
        </member>
        <member name="T:IO.Ably.TokenParams">
            <summary>
            A class providing parameters of a token request.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenParams.Ttl">
            <summary>
            Requested time to live for the token. If the token request
            is successful, the TTL of the returned token will be less
            than or equal to this value depending on application settings
            and the attributes of the issuing key.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenParams.Capability">
            <summary>
            <see cref="P:IO.Ably.TokenParams.Capability"/> of the token. If the token request is successful,
            the capability of the returned token will be the intersection of
            this capability with the capability of the issuing key.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenParams.ClientId">
            <summary>
            ClientId to associate with the current token. The generated token may be to authenticate as this tokenId.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenParams.Timestamp">
            <summary>
            The timestamp  of this request. If not supplied the timestamp is automatically set to the current UTC time
            Timestamps, in conjunction with the nonce, are used to prevent
            token requests from being replayed.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenParams.Nonce">
            <summary>
            An opaque nonce string of at least 16 characters to ensure
            uniqueness of this request. Any subsequent request using the
            same nonce will be rejected.
            </summary>
        </member>
        <member name="M:IO.Ably.TokenParams.Merge(IO.Ably.TokenParams)">
            <summary>
            Merges two another instance of TokenParams with the current instance.
            </summary>
            <param name="otherParams">other instance.</param>
            <returns>a new instance of merged token params.</returns>
        </member>
        <member name="M:IO.Ably.TokenParams.Clone">
            <summary>
            Creates a new instance of token params and populates all the current values.
            </summary>
            <returns>a new instance of token params.</returns>
        </member>
        <member name="M:IO.Ably.TokenParams.WithDefaultsApplied">
            <summary>
            Get a new instance of TokenParams and applies the
            default Capability and Ttl.
            </summary>
            <returns>instance of TokenParams.</returns>
        </member>
        <member name="M:IO.Ably.TokenParams.ToRequestParams(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Populates a dictionary of strings and optionally merges with
            an existing one. Internal method.
            </summary>
            <param name="mergeWith">optional, dictionary of strings to merge with.</param>
            <returns>returns a merge.</returns>
        </member>
        <member name="T:IO.Ably.TokenRequest">
            <summary>
            A class providing parameters of a token request.
            </summary>
        </member>
        <member name="M:IO.Ably.TokenRequest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.TokenRequest"/> class.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.KeyName">
            <summary>
            The Id against which the request is made.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.Ttl">
            <summary>
            Requested time to live for the token. If the token request
            is successful, the TTL of the returned token will be less
            than or equal to this value depending on application settings
            and the attributes of the issuing key.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.Capability">
            <summary>
            <see cref="P:IO.Ably.TokenRequest.Capability"/> of the token. If the token request is successful,
            the capability of the returned token will be the intersection of
            this capability with the capability of the issuing key.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.ClientId">
            <summary>
            ClientId to associate with the current token. The generated token may be to authenticate as this tokenId.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.Timestamp">
            <summary>
            The timestamp  of this request. If not supplied the timestamp is automatically set to the current UTC time
            Timestamps, in conjunction with the nonce, are used to prevent
            token requests from being replayed.
            </summary>
        </member>
        <member name="P:IO.Ably.TokenRequest.Nonce">
             <summary>
             An opaque nonce string of at least 16 characters to ensure
             uniqueness of this request. Any subsequent request using the
             same nonce will be rejected.
             </summary>
            
        </member>
        <member name="P:IO.Ably.TokenRequest.Mac">
            <summary>
            The Message Authentication Code for this request. See the Ably
            Authentication documentation for more details.
            </summary>
        </member>
        <member name="T:IO.Ably.ChannelMode">
            <summary>
            Realtime channel modes.
            </summary>
        </member>
        <member name="F:IO.Ably.ChannelMode.Presence">
            <summary>
            Presence mode. Allows the attached channel to enter Presence.
            </summary>
        </member>
        <member name="F:IO.Ably.ChannelMode.Publish">
            <summary>
            Publish mode. Allows the messages to be published to the attached channel.
            </summary>
        </member>
        <member name="F:IO.Ably.ChannelMode.Subscribe">
            <summary>
            Subscribe mode. Allows the attached channel to subscribe to messages.
            </summary>
        </member>
        <member name="F:IO.Ably.ChannelMode.PresenceSubscribe">
            <summary>
            PresenceSubscribe. Allows the attached channel to subscribe to Presence updates.
            </summary>
        </member>
        <member name="T:IO.Ably.ChannelModes">
            <summary>
            Set of Channel modes. It's used inside ChannelOptions.
            </summary>
        </member>
        <member name="M:IO.Ably.ChannelModes.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelModes"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.ChannelModes.#ctor(System.Collections.Generic.IEnumerable{IO.Ably.ChannelMode})">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelModes"/> class.
            </summary>
            <param name="modes">A list of modes to be populated.</param>
        </member>
        <member name="M:IO.Ably.ChannelModes.#ctor(IO.Ably.ChannelMode[])">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ChannelModes"/> class.
            </summary>
            <param name="modes">A list of modes to be populated.</param>
        </member>
        <member name="T:IO.Ably.ReadOnlyChannelModes">
            <summary>
            Read only version of <see cref="T:IO.Ably.ChannelModes"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.ReadOnlyChannelModes.#ctor(System.Collections.Generic.IList{IO.Ably.ChannelMode})">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ReadOnlyChannelModes"/> class.
            </summary>
            <param name="list">list of channelModes.</param>
        </member>
        <member name="T:IO.Ably.ChannelParams">
            <summary>
            Channel params is a "Dictionary&lt;string, string&gt;" used for passing extra parameters when
            attaching to an Ably Realtime channel.
            </summary>
        </member>
        <member name="T:IO.Ably.ReadOnlyChannelParams">
            <summary>
            Read only version of <see cref="T:IO.Ably.ChannelParams"/>.
            </summary>
        </member>
        <member name="M:IO.Ably.ReadOnlyChannelParams.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ReadOnlyChannelParams"/> class.
            </summary>
            <param name="dictionary">original dictionary used to initialize ReadOnlyChannelParams.</param>
        </member>
        <member name="T:IO.Ably.ConnectionDetails">
            <summary>
            provides details on the constraints or defaults for the connection such as max message size, client ID or connection state TTL.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.ClientId">
            <summary>
            Client id associated with the current connection.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.ConnectionKey">
            <summary>
            Connection key.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.ConnectionStateTtl">
            <summary>
            Optional Connection state time to live.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.MaxFrameSize">
            <summary>
            Max frame size.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.MaxInboundRate">
            <summary>
            Max inbound rate.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.MaxMessageSize">
            <summary>
            Max message size.
            </summary>
        </member>
        <member name="P:IO.Ably.ConnectionDetails.ServerId">
            <summary>
            Server id associated with the current connection.
            </summary>
        </member>
        <member name="T:IO.Ably.ErrorInfo">
            <summary>
            An exception type encapsulating error information containing an Ably specific error code and generic status code.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.Code">
            <summary>
            Ably error code (see https://github.com/ably/ably-common/blob/main/protocol/errors.json).
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.StatusCode">
            <summary>
            The http status code corresponding to this error.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.Message">
            <summary>
            Additional reason information, where available.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.Href">
            <summary>
            Link to specification detail for this error code, where available. Spec TI4.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.Cause">
            <summary>
            Additional cause information, where available.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.IsUnAuthorizedError">
            <summary>
            Is this Error as result of a 401 Unauthorized HTTP response.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.IsForbiddenError">
            <summary>
            Is this Error as result of a 403 Forbidden HTTP response.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.IsTokenError">
            <summary>
            Is the error Code a token error code.
            </summary>
        </member>
        <member name="P:IO.Ably.ErrorInfo.InnerException">
            <summary>
            Get or Sets the InnerException.
            </summary>
        </member>
        <member name="M:IO.Ably.ErrorInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ErrorInfo"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.ErrorInfo.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ErrorInfo"/> class.
            </summary>
            <param name="reason">error reason.</param>
        </member>
        <member name="M:IO.Ably.ErrorInfo.#ctor(System.String,System.Int32,System.Nullable{System.Net.HttpStatusCode},System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ErrorInfo"/> class.
            </summary>
            <param name="reason">error reason.</param>
            <param name="code">error code.</param>
            <param name="statusCode">optional, http status code.</param>
            <param name="innerException">optional, InnerException.</param>
        </member>
        <member name="M:IO.Ably.ErrorInfo.#ctor(System.String,System.Int32,System.Nullable{System.Net.HttpStatusCode},System.String,IO.Ably.ErrorInfo,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.ErrorInfo"/> class.
            </summary>
            <param name="reason">error reason.</param>
            <param name="code">error code.</param>
            <param name="statusCode">optional, http status code.</param>
            <param name="href">optional, documentation url.</param>
            <param name="cause">optional, another ErrorInfo that caused this one.</param>
            <param name="innerException">optional, InnerException.</param>
        </member>
        <member name="M:IO.Ably.ErrorInfo.ToString">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.ErrorInfo.IsRetryableStatusCode">
            <summary>
            Checks if the current error's status code is retryable.
            </summary>
            <returns>true / false.</returns>
        </member>
        <member name="M:IO.Ably.ErrorInfo.LogMessage">
            <summary>
            Spec: TI5.
            </summary>
        </member>
        <member name="M:IO.Ably.ErrorInfo.IsRetryableStatusCode(System.Net.HttpStatusCode)">
            <summary>
            Is statusCode considered retryable.
            </summary>
            <param name="statusCode">status code to check.</param>
            <returns>true / false.</returns>
        </member>
        <member name="T:IO.Ably.HttpPaginatedResponse">
            <summary>
            A type that represents a page of results from a paginated http query.
            The response is accompanied by response details and metadata that
            indicates the relative queries available.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.Headers">
            <summary>
            Response headers.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.StatusCode">
            <summary>
            Response Status code.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.Success">
            <summary>
            Is the response successful.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.ErrorCode">
            <summary>
            Error code if any.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.ErrorMessage">
            <summary>
            Error message if any.
            </summary>
        </member>
        <member name="P:IO.Ably.HttpPaginatedResponse.ExecuteDataQueryFunc">
            <summary>
            Method attached to the Response so we can provide Next and Prev convenience methods.
            </summary>
        </member>
        <member name="M:IO.Ably.HttpPaginatedResponse.#ctor">
            <summary>
            Initialised a new <see cref="T:IO.Ably.HttpPaginatedResponse"/> instance.
            </summary>
        </member>
        <member name="M:IO.Ably.HttpPaginatedResponse.NextAsync">
            <summary>
            If there is a next result it will make a call to retrieve it. Otherwise it will return an empty response.
            </summary>
            <returns>returns the next response.</returns>
        </member>
        <member name="M:IO.Ably.HttpPaginatedResponse.FirstAsync">
            <summary>
            If there is a first result it will make a call to retrieve it. Otherwise it will return an empty response.
            </summary>
            <returns>returns the first response in the sequence.</returns>
        </member>
        <member name="M:IO.Ably.HttpPaginatedResponse.Next">
            <summary>
            Sync version of NextAsync().
            </summary>
            <returns>returns the next response.</returns>
        </member>
        <member name="M:IO.Ably.HttpPaginatedResponse.First">
            <summary>
            Sync version of FirstAsync().
            </summary>
            <returns>returns the next response.</returns>
        </member>
        <member name="T:IO.Ably.Message">
            <summary>A class representing an individual message to be sent or received via the Ably realtime service.</summary>
        </member>
        <member name="M:IO.Ably.Message.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Message"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.Message.#ctor(System.String,System.Object,System.String,IO.Ably.Types.MessageExtras)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.Message"/> class.
            </summary>
            <param name="name">message name.</param>
            <param name="data">message data.</param>
            <param name="extras">extra message parameters.</param>
            <param name="clientId">id of the publisher of this message.</param>
        </member>
        <member name="P:IO.Ably.Message.Id">
            <summary>A globally unique message id.</summary>
        </member>
        <member name="P:IO.Ably.Message.ClientId">
            <summary>The id of the publisher of this message.</summary>
        </member>
        <member name="P:IO.Ably.Message.ConnectionId">
            <summary>The connection id of the publisher of the message.</summary>
        </member>
        <member name="P:IO.Ably.Message.ConnectionKey">
            <summary>The connection key of the publisher of the message. Used for impersonation.</summary>
        </member>
        <member name="P:IO.Ably.Message.Name">
            <summary>The event name, if available.</summary>
        </member>
        <member name="P:IO.Ably.Message.Timestamp">
            <summary>Timestamp when the message was received by the Ably real-time service.</summary>
        </member>
        <member name="P:IO.Ably.Message.Data">
            <summary>The message payload. Supported data types are objects, byte[] and strings.</summary>
        </member>
        <member name="P:IO.Ably.Message.Extras">
            <summary>
            Extra properties associated with the message.
            </summary>
        </member>
        <member name="P:IO.Ably.Message.Encoding">
            <summary>
                The encoding for the message data. Encoding and decoding of messages is handled automatically by the client
                library.
                Therefore, the `encoding` attribute should always be nil unless an Ably library decoding error has occurred.
            </summary>
        </member>
        <member name="M:IO.Ably.Message.ToString">
            <inheritdoc/>
        </member>
        <member name="P:IO.Ably.Message.IsEmpty">
            <summary>
            Checks if this is an empty message.
            </summary>
        </member>
        <member name="M:IO.Ably.Message.Equals(IO.Ably.Message)">
            <summary>
            Checks equality with another message.
            </summary>
            <param name="other">other Message object.</param>
            <returns>true / false..</returns>
        </member>
        <member name="M:IO.Ably.Message.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Message.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:IO.Ably.Message.FromEncoded(IO.Ably.Message,IO.Ably.ChannelOptions)">
            <summary>
            Decodes the current message data using the default list of encoders.
            </summary>
            <param name="encoded">encoded message object.</param>
            <param name="options">optional channel options. <see cref="T:IO.Ably.ChannelOptions"/>.</param>
            <returns>message with decoded payload.</returns>
        </member>
        <member name="M:IO.Ably.Message.FromEncodedArray(IO.Ably.Message[],IO.Ably.ChannelOptions)">
            <summary>
            Decodes an array of messages. <see cref="M:IO.Ably.Message.FromEncoded(IO.Ably.Message,IO.Ably.ChannelOptions)"/>.
            </summary>
            <param name="encoded">array of encoded Messages.</param>
            <param name="options">optional channel options. <see cref="T:IO.Ably.ChannelOptions"/>.</param>
            <returns>array of decoded messages.</returns>
        </member>
        <member name="M:IO.Ably.Message.FromEncoded(System.String,IO.Ably.ChannelOptions)">
            <summary>
            Decodes the json representation of a Message using the default list of encoders.
            </summary>
            <param name="messageJson">json representation of a Message.</param>
            <param name="options">optional channel options. <see cref="T:IO.Ably.ChannelOptions"/>.</param>
            <returns>message with decoded payload.</returns>
            <exception cref="T:IO.Ably.AblyException">AblyException if there is an issue decoding the message. The most likely error is invalid json string.</exception>
        </member>
        <member name="M:IO.Ably.Message.FromEncodedArray(System.String,IO.Ably.ChannelOptions)">
            <summary>
            Decodes a json representation of an array of messages using the default list of encoders.
            </summary>
            <param name="messagesJson">json representation of an array of messages.</param>
            <param name="options">optional channel options. <see cref="T:IO.Ably.ChannelOptions"/>.</param>
            <returns>array of decoded messages.</returns>
            <exception cref="T:IO.Ably.AblyException">AblyException if there is an issue decoding the message. The most likely error is invalid json string.</exception>
        </member>
        <member name="T:IO.Ably.PresenceAction">
            <summary>
            Presence Action: the event signified by a PresenceMessage.
            </summary>
        </member>
        <member name="F:IO.Ably.PresenceAction.Absent">
            <summary>
            Absent.
            </summary>
        </member>
        <member name="F:IO.Ably.PresenceAction.Present">
            <summary>
            Present.
            </summary>
        </member>
        <member name="F:IO.Ably.PresenceAction.Enter">
            <summary>
            Enter.
            </summary>
        </member>
        <member name="F:IO.Ably.PresenceAction.Leave">
            <summary>
            Leave.
            </summary>
        </member>
        <member name="F:IO.Ably.PresenceAction.Update">
            <summary>
            Update.
            </summary>
        </member>
        <member name="T:IO.Ably.PresenceMessage">
            <summary>
            A class representing an individual presence update to be sent or received
            via the Ably Realtime service.
            </summary>
        </member>
        <member name="M:IO.Ably.PresenceMessage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.PresenceMessage"/> class.
            </summary>
        </member>
        <member name="M:IO.Ably.PresenceMessage.#ctor(IO.Ably.PresenceAction,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.PresenceMessage"/> class.
            </summary>
            <param name="action">presence action.</param>
            <param name="clientId">id of client.</param>
        </member>
        <member name="M:IO.Ably.PresenceMessage.#ctor(IO.Ably.PresenceAction,System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:IO.Ably.PresenceMessage"/> class.
            </summary>
            <param name="action">presence action.</param>
            <param name="clientId">id of client.</param>
            <param name="data">custom data object passed with the presence message.</param>
        </member>
        <member name="P:IO.Ably.PresenceMessage.Id">
            <summary>
            Ably message id.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.Action">
            <summary>
            Presence action associated with the presence message.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.ClientId">
            <summary>
            Id of the client associate.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.ConnectionId">
            <summary>
            Id of the current connection.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.ConnectionKey">
            <summary>The connection key of the publisher of the message. Used for impersonation.</summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.Data">
            <summary>
            Custom data object associated with the message.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.Encoding">
            <summary>
            Encoding for the message.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.Timestamp">
            <summary>
            Server timestamp for the message.
            </summary>
        </member>
        <member name="P:IO.Ably.PresenceMessage.MemberKey">
            <summary>
            Member key which is a combination of ClientId:ConnectionId.
            </summary>
        </member>
        <member name="M:IO.Ably.PresenceMessage.ShallowClone">
            <summary>
            Clones the current object.
            </summary>
            <returns>a new Presence message.</returns>
        </member>
        <member name="T:IO.Ably.ActionOnDispose">
            <summary>Utility class that implements IDisposable bu calling the provided action.</summary>
        </member>
        <member name="T:IO.Ably.Utils.ErrorPolicy">
            <summary>
            Utility type to defining various error policies.
            </summary>
        </member>
        <member name="M:IO.Ably.Utils.ErrorPolicy.HandleUnexpected(System.Exception,IO.Ably.ILogger)">
            <summary>
            Upon receiving an *unexpected* exception and local policy is unclear, defer to this method.
            </summary>
            <param name="e">The unexpected exception.</param>
            <param name="logger">Logger to report to.</param>
        </member>
        <member name="T:IO.Ably.StringUtils">
            <summary>
            String utility functions.
            </summary>
        </member>
        <member name="M:IO.Ably.StringUtils.GetBytes(System.String)">
            <summary>
            Returns UTF8 bytes from a given string.
            </summary>
            <param name="text">input string.</param>
            <returns>UTF8 byte[].</returns>
        </member>
        <member name="T:AblyPlatform.Cryptography.AesCipher">
            <summary>Cipher implementation using RinjaelManaged class under the hood.
            The Cipher params decide the Cipher mode and key
            The Iv vector is generated on each encryption request and added to the encrypted data stream.</summary>
        </member>
        <member name="M:AblyPlatform.Cryptography.AesCipher.#ctor(IO.Ably.CipherParams)">
            <summary>Create a new instance of AesCipher.</summary>
            <param name="params">Cipher params used to configure the RinjaelManaged algorithm.</param>
        </member>
        <member name="M:AblyPlatform.Cryptography.AesCipher.Encrypt(System.Byte[])">
            <summary>Encrypt a byte[] using the CipherParams provided in the constructor.</summary>
            <param name="input">byte[] to be encrypted.</param>
            <returns>Encrypted result.</returns>
        </member>
        <member name="M:AblyPlatform.Cryptography.AesCipher.Decrypt(System.Byte[])">
            <summary>Decrypt an encrypted byte[] using the CipherParams provided in the constructor.</summary>
            <param name="input">encrypted byte[].</param>
            <returns>decrypted byte[].</returns>
        </member>
    </members>
</doc>
