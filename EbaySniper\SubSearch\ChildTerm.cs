﻿using System;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Serialization;
using DevExpress.Data.Filtering;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList;
using Lucene.Net.Search;
using uBuyFirst.Tools;

namespace uBuyFirst.SubSearch;

[Serializable]
[Obfuscation(Exclude = true)]
public class ChildTerm : TreeList.IVirtualTreeListData, IDisposable
{
    private Keyword2Find? _parentCore;

    public string Alias;

    public bool Enabled;

    [XmlElement("XFilter")]
    public XFilterClassChild SubSearch;

    public ChildTerm()
    {
    }

    public ChildTerm(Keyword2Find? parent, string alias)
    {
        if (parent != null)
        {
            var uniqueAliasesDict = Helpers.CountStrings(parent.ChildrenCore.Select(subSearch => subSearch.Alias).ToList());
            Alias = Helpers.MakeUniqAliasOnAdd(alias, uniqueAliasesDict);
        }

        _parentCore = parent;
        _parentCore?.ChildrenCore.Add(this);
    }

    public string[] Condition
    {
        get
        {
            if (_condition == null)
            {
                _condition = new[] { "" };
            }

            return _condition;
        }
        set
        {
            if (value != null)
            {
                _condition = value;
            }
        }
    }

    private string[] _categoryIDs;

    public string[] CategoryIDs
    {
        get
        {
            if (_categoryIDs == null)
                _categoryIDs = new string[] { };
            return _categoryIDs;
        }
        set
        {
            if (value != null)
            {
                _categoryIDs = value;
            }
        }
    }

    public override string ToString()
    {
        if (Alias != null)
            return Alias;

        return "";
    }

    private string _keywords;

    public double PriceMax { get; set; }

    public double PriceMin { get; set; }

    public string Keywords
    {
        get => _keywords;
        set
        {
            _keywords = "";
            if (value == null)
                return;

            value = value.Trim();
            if (value == "")
                return;

            try
            {
                LuQuery = KeywordHelpers.ParseKeyword2LuceneQuery(value, true);
                _keywords = value;
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Parsing sub query: ", ex);
                MessageBox.Show($"{value}\n{ex.Message}");
            }
        }
    }

    [XmlIgnore]
    private Query LuQuery
    {
        get => _luQuery;
        set => _luQuery = value;
    }

    public string Id { get; set; } = Guid.NewGuid().ToString();

    private Query _luQuery;
    public bool SearchInDescription;
    private string[] _condition;

    public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
    {
    }

    public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
    {
        var term = (ChildTerm)info.Node;

        switch (info.Column.Caption)
        {
            case "Enabled":
                info.CellData = term.Enabled;

                break;

            case "Alias":
                info.CellData = term.Alias;
                break;

            case "Keywords":
                info.CellData = term.Keywords;
                break;

            case "Price Min":
                info.CellData = term.PriceMin;
                break;

            case "Price Max":
                info.CellData = term.PriceMax;
                break;

            case "Search in description":
                info.CellData = term.SearchInDescription;
                break;

            case "Condition":
                if (term.Condition == null || term.Condition.Length == 0 || term.Condition.Length == 1 && string.IsNullOrEmpty(term.Condition[0]))
                {
                    //    info.CellData = "Any condition";
                }
                else
                    info.CellData = string.Join(",", term.Condition);

                break;

            case "Category ID":
                if (term.CategoryIDs != null)
                    info.CellData = string.Join(",", term.CategoryIDs);
                break;

            case "Filter":
                info.CellData = term.SubSearch?.FilterCriteria;
                break;
        }
    }

    public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
    {
        if (info.Node is ChildTerm term)
        {
            switch (info.Column.Caption)
            {
                case "Enabled":
                    term.Enabled = (bool)info.NewCellData;
                    if (info.NewCellData is bool dataIsBool)
                    {
                        term.Enabled = dataIsBool;
                    }

                    if (info.NewCellData is string dataIsString)
                    {
                        term.Enabled = dataIsString == "Checked";
                    }

                    info.Column.TreeList.PostEditor();
                    break;

                case "Alias":
                    term.Alias = info.NewCellData as string;
                    var uniqueAliasesDict = Helpers.CountStrings(term._parentCore.ChildrenCore.Select(subSearch => subSearch.Alias).ToList());
                    Helpers.MakeUniqAliasOnEdit(term.Alias, uniqueAliasesDict);
                    if (term.SubSearch != null)
                    {
                        term.SubSearch.Alias = term.Alias;
                    }

                    break;

                case "Keywords":
                    term.Keywords = info.NewCellData as string;
                    break;

                case "Price Min":
                    var newCellData = info.NewCellData.ToString();
                    if (double.TryParse(newCellData, out var priceMin))
                        PriceMin = priceMin;
                    break;

                case "Price Max":
                    if (double.TryParse(info.NewCellData.ToString(), out var priceMax))
                        PriceMax = priceMax;
                    break;

                case "Search in description":
                    term.SearchInDescription = (bool)info.NewCellData;
                    break;

                case "Condition":
                    var s = info.NewCellData as string;
                    if (string.IsNullOrEmpty(s) || s == "Any condition" || s == "1000, 1500, 1750, 2000, 2010, 2020, 2030, 2500, 2750, 3000, 4000, 5000, 6000, 7000, Unspecified")
                    {
                        Condition = new[] { "" };
                    }
                    else
                    {
                        var v = s.Split(',');
                        for (var index = 0; index < v.Length; index++)
                        {
                            v[index] = v[index].Trim();
                        }

                        Condition = v;
                    }

                    break;

                case "Category ID":
                    if (info.NewCellData is string c)
                    {
                        var v = c.Split(',');
                        for (var index = 0; index < v.Length; index++)
                        {
                            v[index] = v[index].Trim();
                        }

                        CategoryIDs = v;
                    }
                    else
                    {
                        CategoryIDs = new[] { "" };
                    }

                    break;

                case "Filter":
                    term.SubSearch.FilterCriteria = info.NewCellData as CriteriaOperator;
                    break;
            }
        }

        //CellsCore[info.Column.AbsoluteIndex] = info.NewCellData;
    }

    public void SetParent(Keyword2Find? item)
    {
        if (_parentCore != null)
        {
        }
        else
        {
            _parentCore = item;
        }
    }

    public void ChangeParent(Keyword2Find? item, int pos = -1)
    {
        _parentCore = item;
        if (pos <= 0 || _parentCore.ChildrenCore.Count <= pos)
            _parentCore.ChildrenCore.Add(this);
        else
            _parentCore.ChildrenCore.Insert(pos, this);
    }

    public Keyword2Find? GetParent()
    {
        return _parentCore;
    }

    public void RemoveFromParent(Keyword2Find targetSearchQuery)
    {
        targetSearchQuery.ChildrenCore.Remove(this);
    }

    public bool IsMatch(string title, string description)
    {
        if (LuQuery == null)
        {
            XtraMessageBox.Show($"Search - {_parentCore.Alias}\nSub Search - {Alias}\nEmpty Keywords value");
            return false;
        }

        var isMatch = KeywordHelpers.IsMatch(title.ToLower(), LuQuery);
        if (isMatch)
            return true;

        if (!SearchInDescription)
        {
            return false;
        }

        if (string.IsNullOrEmpty(description))
        {
            return false;
        }

        return KeywordHelpers.IsMatch(description.ToLower(), LuQuery);
    }

    public void Dispose()
    {

    }
}
