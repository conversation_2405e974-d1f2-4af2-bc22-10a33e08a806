﻿using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Text.RegularExpressions;
using CefSharp.WinForms;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.ExternalData
{
    internal class ExternalDataManager
    {
        public static string BuildExternalDataUrl(string url, DataRow row)
        {
            var pattern = @"\{(.*?)\}";

            // Use Regex.Matches to find all matches
            var matches = Regex.Matches(url, pattern);

            // Create a list to store the extracted strings
            var extractedStrings = new List<string>();

            // Iterate through all matches and add them to the list
            foreach (Match match in matches)
            {
                if (match.Success)
                {
                    extractedStrings.Add(match.Groups[1].Value);
                }
            }

            if (row == null)
                return url;
            // Retrieve the DataList from the DataRow (if applicable)
            var d = row["Blob"] as DataList;

            foreach (var columnName in extractedStrings)
            {
                var rowValue = GetRowValue(row, columnName, d);
                rowValue = WebUtility.UrlEncode(rowValue);
                url = url.Replace("{" + columnName + "}", rowValue);
            }

            // If there are still placeholders remaining, append them as query parameters
            if (Regex.IsMatch(url, pattern))
            {
                var queryParams = new List<string>();
                foreach (Match match in Regex.Matches(url, pattern))
                {
                    if (match.Success)
                    {
                        var columnName = match.Groups[1].Value;
                        var rowValue = GetRowValue(row, columnName, d);
                        rowValue = WebUtility.UrlEncode(rowValue);
                        queryParams.Add($"{columnName}={rowValue}");
                        url = url.Replace("{" + columnName + "}", ""); // Remove placeholders
                    }
                }

                if (queryParams.Count > 0)
                {
                    var separator = url.Contains("?") ? "&" : "?";
                    url += separator + string.Join("&", queryParams);
                }
            }

            return url;
        }
        public static string GetRowValue(DataRow row, string columnName, DataList d)
        {
            switch (columnName)
            {
                case "TotalPrice":
                    return d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();

                case "ItemPrice":
                    return d.ItemPricing.ItemPrice.FormatPrice();

                case "AuctionPrice":
                    return d.ItemPricing.AuctionPrice?.FormatPrice();

                case "Shipping":
                    return d.ItemShipping.FullSingleShippingPrice.FormatPrice();

                case "ShipAdditionalItem":
                    return d.ItemShipping.ShipAdditionalItem.FormatPrice();

                case "FoundTime":
                    return d.FoundTime.ToString();

                case "Description":
                    var description = row["Description"].ToString();

                    if (description.Length > 3000)
                        return "";

                    description = WebUtility.HtmlDecode(description);
                    description = Regex.Replace(description, "(<style.+?</style>)|(<script.+?</script>)", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    var plainDescription = Regex.Replace(description, @"<[^>]*>", string.Empty, RegexOptions.Compiled);

                    return plainDescription;

                case "ItemID":
                    return "";

                default:
                    foreach (DataColumn dataColumn in row.Table.Columns)
                    {
                        if (dataColumn.ColumnName.Replace(" ", "") == columnName)
                        {
                            if (!row.IsNull(dataColumn.ColumnName))
                            {
                                return row[dataColumn.ColumnName].ToString();
                            }
                        }
                    }

                    return "";
            }
        }

        public static List<string> GetImagePaths(DataRow row)
        {
            if (row == null)
                return new List<string>();
            var d = row["Blob"] as DataList;

            var imagePaths = new List<string>();
            if (!string.IsNullOrEmpty(d?.GalleryUrl))
            {
                imagePaths.Add(d.GalleryUrl);
            }
            if (d?.Pictures != null)
            {
                imagePaths.AddRange(d.Pictures);
            }

            return imagePaths;
        }

        public static string GetDescription(DataRow row)
        {
            if (row == null)
                return "";

            var description = row["Description"].ToString();


            description = WebUtility.HtmlDecode(description);
            description = Regex.Replace(description, "(<style.+?</style>)|(<script.+?</script>)", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
            var plainDescription = Regex.Replace(description, @"<[^>]*>", " ", RegexOptions.Compiled);
            plainDescription = Regex.Replace(plainDescription, @"[ \r\f\v]+", " ");   // Collapse spaces/other non-newline/tab whitespace
            plainDescription = Regex.Replace(plainDescription, @"\n+", "\n");         // Collapse multiple newlines
            plainDescription = Regex.Replace(plainDescription, @"\t+", "\t");         // Collapse multiple tabs
            plainDescription = Regex.Replace(plainDescription, @" (?=\n|\t)|(?<=\n|\t) ", ""); // Remove spaces adjacent to \n or \t
            plainDescription = plainDescription.Trim();
            return plainDescription;
        }

        public static void SendRequestWithData(ChromiumWebBrowser chromiumWebBrowser, string url, DataRow row)
        {
            if (UserSettings.SendDescriptionAndPictures)
            {
                var description = GetDescription(row);
                var imagePaths = GetImagePaths(row);
                // Set up custom request handler for POST request
                chromiumWebBrowser.RequestHandler = new CustomRequestHandler(imagePaths, description);
            }
            else
            {
                chromiumWebBrowser.RequestHandler = null;
            }

            // For both GET and POST, load the URL
            chromiumWebBrowser.LoadUrl(url);
        }
    }
}
