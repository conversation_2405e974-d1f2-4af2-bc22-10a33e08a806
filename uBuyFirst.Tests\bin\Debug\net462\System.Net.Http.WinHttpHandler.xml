<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Net.Http.WinHttpHandler</name>
    </assembly>
    <members>
        <member name="M:Interop.Kernel32.GetMessage(System.Int32)">
            <summary>
                Returns a string message for the specified Win32 error code.
            </summary>
        </member>
        <member name="M:System.Net.HttpKnownHeaderNames.TryGetHeaderName(System.Char[],System.Int32,System.Int32,System.String@)">
            <summary>
            Gets a known header name string from a matching char[] array segment, using a case-sensitive
            ordinal comparison. Used to avoid allocating new strings for known header names.
            </summary>
        </member>
        <member name="M:System.Net.HttpKnownHeaderNames.TryGetHeaderName(System.IntPtr,System.Int32,System.String@)">
            <summary>
            Gets a known header name string from a matching IntPtr buffer, using a case-sensitive
            ordinal comparison. Used to avoid allocating new strings for known header names.
            </summary>
        </member>
        <member name="M:System.Net.HttpKnownHeaderNames.TryMatch``1(System.String,``0,System.Int32,System.Int32,System.Func{System.String,``0,System.Int32,System.Int32,System.Boolean},System.String@)">
            <summary>
            Returns true if <paramref name="known"/> matches the <paramref name="key"/> char[] array segment,
            using an ordinal comparison.
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpHandlerDefaults">
            <summary>
            Central repository for default values used in http handler settings.  Not all settings are relevant
            to or configurable by all handlers.
            </summary>
        </member>
        <member name="T:System.Net.Http.NoWriteNoSeekStreamContent">
            <summary>Provides an HttpContent for a Stream that is inherently read-only without support for writing or seeking.</summary>
            <remarks>Same as StreamContent, but specialized for no-write, no-seek, and without being constrained by its public API.</remarks>
        </member>
        <member name="P:System.Net.Http.WinHttpHandler.TcpKeepAliveEnabled">
            <summary>
            Gets or sets a value indicating whether TCP keep-alive is enabled.
            </summary>
            <remarks>
            Only supported on Windows 10 version 2004 or newer.
            If enabled, the values of <see cref="P:System.Net.Http.WinHttpHandler.TcpKeepAliveInterval" /> and <see cref="P:System.Net.Http.WinHttpHandler.TcpKeepAliveTime"/> will be forwarded
            to set WINHTTP_OPTION_TCP_KEEPALIVE, enabling and configuring TCP keep-alive for the backing TCP socket.
            </remarks>
        </member>
        <member name="P:System.Net.Http.WinHttpHandler.TcpKeepAliveTime">
            <summary>
            Gets or sets the TCP keep-alive timeout.
            </summary>
            <remarks>
            Only supported on Windows 10 version 2004 or newer.
            Has no effect if <see cref="P:System.Net.Http.WinHttpHandler.TcpKeepAliveEnabled"/> is <see langword="false" />.
            The default value of this property is 2 hours.
            </remarks>
        </member>
        <member name="P:System.Net.Http.WinHttpHandler.TcpKeepAliveInterval">
            <summary>
            Gets or sets the TCP keep-alive interval.
            </summary>
            <remarks>
            Only supported on Windows 10 version 2004 or newer.
            Has no effect if <see cref="P:System.Net.Http.WinHttpHandler.TcpKeepAliveEnabled"/> is <see langword="false" />.
            The default value of this property is 1 second.
            </remarks>
        </member>
        <member name="T:System.Net.Http.WinHttpRequestCallback">
            <summary>
            Static class containing the WinHttp global callback and associated routines.
            </summary>
        </member>
        <member name="T:System.Net.Http.WinHttpResponseHeaderReader">
            <summary>
            Used to read header lines of an HTTP response, where each line is separated by "\r\n".
            </summary>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseHeaderReader.ReadHeader(System.String@,System.String@)">
            <summary>
            Reads a header line.
            Empty header lines are skipped, as are malformed header lines that are missing a colon character.
            </summary>
            <returns>true if the next header was read successfully, or false if all characters have been read.</returns>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseHeaderReader.ReadLine">
            <summary>
            Reads lines separated by "\r\n".
            </summary>
            <returns>true if the next line was read successfully, or false if all characters have been read.</returns>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseHeaderReader.ReadLine(System.Int32@,System.Int32@)">
            <summary>
            Reads lines separated by "\r\n".
            </summary>
            <param name="startIndex">The start of the line segment.</param>
            <param name="length">The length of the line segment.</param>
            <returns>true if the next line was read successfully, or false if all characters have been read.</returns>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseParser.GetResponseHeaderNumberInfo(Interop.WinHttp.SafeWinHttpHandle,System.UInt32)">
            <summary>
            Returns the first header or throws if the header isn't found.
            </summary>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseParser.GetResponseHeader(Interop.WinHttp.SafeWinHttpHandle,System.UInt32,System.Char[])">
            <summary>
            Fills the buffer with the header value and returns the length, or returns 0 if the header isn't found.
            </summary>
        </member>
        <member name="M:System.Net.Http.WinHttpResponseParser.GetResponseHeaderCharBufferLength(Interop.WinHttp.SafeWinHttpHandle,System.Boolean)">
            <summary>
            Returns the size of the char array buffer.
            </summary>
        </member>
        <member name="T:System.Net.NetEventSource">
            <summary>Provides logging facilities for System.Net libraries.</summary>
        </member>
        <member name="F:System.Net.NetEventSource.Log">
            <summary>The single event source instance to use for all logging.</summary>
        </member>
        <member name="M:System.Net.NetEventSource.Info(System.Object,System.FormattableString,System.String)">
            <summary>Logs an information message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Info(System.Object,System.Object,System.String)">
            <summary>Logs an information message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Error(System.Object,System.FormattableString,System.String)">
            <summary>Logs an error message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Error(System.Object,System.Object,System.String)">
            <summary>Logs an error message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Verbose(System.Object,System.FormattableString,System.String)">
            <summary>Logs an info message at verbose mode.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Verbose(System.Object,System.Object,System.String)">
            <summary>Logs an info at verbose mode.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.Byte[],System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="buffer">The buffer to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.Byte[],System.Int32,System.Int32,System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="buffer">The buffer to be logged.</param>
            <param name="offset">The starting offset from which to log.</param>
            <param name="count">The number of bytes to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.IntPtr,System.Int32,System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="bufferPtr">The starting location of the buffer to be logged.</param>
            <param name="count">The number of bytes to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Associate(System.Object,System.Object,System.String)">
            <summary>Logs a relationship between two objects.</summary>
            <param name="first">The first object.</param>
            <param name="second">The second object.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Associate(System.Object,System.Object,System.Object,System.String)">
            <summary>Logs a relationship between two objects.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="first">The first object.</param>
            <param name="second">The second object.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Runtime.ExceptionServices.ExceptionStackTrace.AddCurrentStack(System.Exception)">
            <summary>In debug builds, appends the current stack trace to the exception.</summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Threading.Tasks.RendezvousAwaitable`1">
            <summary>Provides a reusable object that can be awaited by a consumer and manually completed by a producer.</summary>
            <typeparam name="TResult">The type of data being passed from producer to consumer.</typeparam>
        </member>
        <member name="F:System.Threading.Tasks.RendezvousAwaitable`1.s_completionSentinel">
            <summary>Sentinel object indicating that the operation has completed prior to OnCompleted being called.</summary>
        </member>
        <member name="F:System.Threading.Tasks.RendezvousAwaitable`1._continuation">
            <summary>
            The continuation to invoke when the operation completes, or <see cref="F:System.Threading.Tasks.RendezvousAwaitable`1.s_completionSentinel"/> if the operation
            has completed before OnCompleted is called.
            </summary>
        </member>
        <member name="F:System.Threading.Tasks.RendezvousAwaitable`1._error">
            <summary>The exception representing the failed async operation, if it failed.</summary>
        </member>
        <member name="F:System.Threading.Tasks.RendezvousAwaitable`1._result">
            <summary>The result of the async operation, if it succeeded.</summary>
        </member>
        <member name="P:System.Threading.Tasks.RendezvousAwaitable`1.RunContinuationsAsynchronously">
            <summary>true if the producer should invoke the continuation asynchronously; otherwise, false.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.GetAwaiter">
            <summary>Gets this instance as an awaiter.</summary>
        </member>
        <member name="P:System.Threading.Tasks.RendezvousAwaitable`1.IsCompleted">
            <summary>Whether the operation has already completed.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.SetResult(`0)">
            <summary>Set the result of the operation.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.SetCanceled(System.Threading.CancellationToken)">
            <summary>Set that the operation was canceled.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.SetException(System.Exception)">
            <summary>Set the failure for the operation.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.NotifyAwaiter">
            <summary>Alerts any awaiter that the operation has completed.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.OnCompleted(System.Action)">
            <summary>Register the continuation to invoke when the operation completes.</summary>
        </member>
        <member name="M:System.Threading.Tasks.RendezvousAwaitable`1.UnsafeOnCompleted(System.Action)">
            <summary>Register the continuation to invoke when the operation completes.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm">
            <summary>
            Provides support for efficiently using Tasks to implement the APM (Begin/End) pattern.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.Begin(System.Threading.Tasks.Task,System.AsyncCallback,System.Object)">
            <summary>
            Marshals the Task as an IAsyncResult, using the supplied callback and state
            to implement the APM pattern.
            </summary>
            <param name="task">The Task to be marshaled.</param>
            <param name="callback">The callback to be invoked upon completion.</param>
            <param name="state">The state to be stored in the IAsyncResult.</param>
            <returns>An IAsyncResult to represent the task's asynchronous operation.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End``1(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.GetTask(System.IAsyncResult)">
            <summary>Gets the task represented by the IAsyncResult.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.ThrowArgumentException(System.IAsyncResult)">
            <summary>Throws an argument exception for the invalid <paramref name="asyncResult"/>.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm.TaskAsyncResult">
            <summary>Provides a simple IAsyncResult that wraps a Task.</summary>
            <remarks>
            We could use the Task as the IAsyncResult if the Task's AsyncState is the same as the object state,
            but that's very rare, in particular in a situation where someone cares about allocation, and always
            using TaskAsyncResult simplifies things and enables additional optimizations.
            </remarks>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task">
            <summary>The wrapped Task.</summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._callback">
            <summary>Callback to invoke when the wrapped task completes.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.#ctor(System.Threading.Tasks.Task,System.Object,System.AsyncCallback)">
            <summary>Initializes the IAsyncResult with the Task to wrap and the associated object state.</summary>
            <param name="task">The Task to wrap.</param>
            <param name="state">The new AsyncState value.</param>
            <param name="callback">Callback to invoke when the wrapped task completes.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.InvokeCallback">
            <summary>Invokes the callback.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncState">
            <summary>Gets a user-defined object that qualifies or contains information about an asynchronous operation.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.CompletedSynchronously">
            <summary>Gets a value that indicates whether the asynchronous operation completed synchronously.</summary>
            <remarks>This is set lazily based on whether the <see cref="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task"/> has completed by the time this object is created.</remarks>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.IsCompleted">
            <summary>Gets a value that indicates whether the asynchronous operation has completed.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncWaitHandle">
            <summary>Gets a <see cref="T:System.Threading.WaitHandle"/> that is used to wait for an asynchronous operation to complete.</summary>
        </member>
        <member name="T:System.IO.StreamHelpers">
            <summary>Provides methods to help in the implementation of Stream-derived types.</summary>
        </member>
        <member name="M:System.IO.StreamHelpers.ValidateCopyToArgs(System.IO.Stream,System.IO.Stream,System.Int32)">
            <summary>Validate the arguments to CopyTo, as would Stream.CopyTo.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="P:System.SR.net_securityprotocolnotsupported">
            <summary>The requested security protocol is not supported.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_cookiecontainer">
            <summary>When using CookieUsePolicy.UseSpecifiedCookieContainer, the CookieContainer property must not be null.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_proxyusepolicy">
            <summary>When using a non-null Proxy, the WindowsProxyUsePolicy property must be set to WindowsProxyUsePolicy.UseCustomProxy.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_proxy">
            <summary>When using WindowsProxyUsePolicy.UseCustomProxy, the Proxy property must not be null.</summary>
        </member>
        <member name="P:System.SR.net_http_handler_norequest">
            <summary>A request message must be provided. It cannot be null.</summary>
        </member>
        <member name="P:System.SR.net_http_operation_started">
            <summary>This instance has already started one or more requests. Properties can only be modified before sending the first request.</summary>
        </member>
        <member name="P:System.SR.net_http_client_execution_error">
            <summary>An error occurred while sending the request.</summary>
        </member>
        <member name="P:System.SR.net_http_io_read">
            <summary>The read operation failed, see inner exception.</summary>
        </member>
        <member name="P:System.SR.net_http_io_read_incomplete">
            <summary>Unable to read data from the transport connection. The connection was closed before all data could be read. Expected {0} bytes, read {1} bytes.</summary>
        </member>
        <member name="P:System.SR.net_http_io_write">
            <summary>The write operation failed, see inner exception.</summary>
        </member>
        <member name="P:System.SR.net_http_chunked_not_allowed_with_empty_content">
            <summary>'Transfer-Encoding: chunked' header can not be used when content object is not specified.</summary>
        </member>
        <member name="P:System.SR.net_http_invalid_enable_first">
            <summary>The {0} property must be set to '{1}' to use this property.</summary>
        </member>
        <member name="P:System.SR.net_http_value_must_be_greater_than">
            <summary>The specified value must be greater than {0}.</summary>
        </member>
        <member name="P:System.SR.net_http_username_empty_string">
            <summary>The username for a credential object cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.net_http_no_concurrent_io_allowed">
            <summary>The stream does not support concurrent I/O read or write operations.</summary>
        </member>
        <member name="P:System.SR.net_http_buffer_insufficient_length">
            <summary>The buffer was not long enough.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedPosNum">
            <summary>Positive number required.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnreadableStream">
            <summary>Stream does not support reading.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnwritableStream">
            <summary>Stream does not support writing.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_StreamClosed">
            <summary>Cannot access a closed stream.</summary>
        </member>
        <member name="P:System.SR.net_http_content_stream_already_read">
            <summary>The stream was already consumed. It cannot be read again.</summary>
        </member>
        <member name="P:System.SR.net_http_winhttp_error">
            <summary>Error {0} calling {1}, '{2}'.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_WinHttpHandler">
            <summary>WinHttpHandler is only supported on .NET Framework and .NET runtimes on Windows. It is not supported for Windows Store Applications (UWP) or Unix platforms.</summary>
        </member>
        <member name="P:System.SR.net_http_unsupported_requesturi_scheme">
            <summary>The '{0}' scheme is not supported.</summary>
        </member>
        <member name="P:System.SR.net_http_client_invalid_requesturi">
            <summary>An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.</summary>
        </member>
    </members>
</doc>
