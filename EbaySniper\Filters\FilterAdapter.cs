﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.XtraEditors.Filtering;
using DevExpress.XtraEditors.Repository;
using uBuyFirst.Data.Filters;

namespace uBuyFirst.Data
{
    [ToolboxItem(true)]
    public sealed class FilterAdapter : Component, IFilteredComponent, ISupportInitialize
    {
        private CriteriaOperator _fRowCriteria;
        private static readonly object FRowFilterChanged = new object();

        [AttributeProvider(typeof(IListSource))]
        public object DataSource
        {
            get => _fDataSource;
            set
            {
                if (_fDataSource is IBindingList oldBindingList)
                    oldBindingList.ListChanged -= OnListChanged;
                _fDataSource = value;
                if (_fDataSource is IBindingList newBindingList)
                    newBindingList.ListChanged += OnListChanged;
            }
        }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public CriteriaOperator RowCriteria
        {
            get => _fRowCriteria;
            set
            {
                if (ReferenceEquals(_fRowCriteria, value)) return;
                _fRowCriteria = value;
                RaiseRowFilterChanged();
            }
        }

        private object _fDataSource;
        private static readonly RepositoryItemTextEdit DefaultEditor = new RepositoryItemTextEdit();
        private static readonly RepositoryItemDateEdit DefaultDateEditor = new RepositoryItemDateEdit();

        private FilterColumnCollection GetFilterColumns()
        {
            var pdc = GetProperties();
            var td = FilterPropertyDescriptionCollection(pdc);
            return GetFilterColumnsCollection(td);
        }

        public PropertyDescriptorCollection GetProperties()
        {
            var list = DataSource as IList;
            if (list == null)
                if (DataSource is IListSource source)
                    list = source.GetList();

            if (list == null)
                return new PropertyDescriptorCollection(new PropertyDescriptor[0], true);

            if (!(list is ITypedList typedList))
            {
                if (list.Count > 0)
                    return TypeDescriptor.GetProperties(list[0]);
            }
            else
            {
                return typedList.GetItemProperties(new PropertyDescriptor[0]);
            }

            return new PropertyDescriptorCollection(new PropertyDescriptor[0], true);
        }

        private static List<PropertyDescriptor> FilterPropertyDescriptionCollection(PropertyDescriptorCollection pdc)
        {
            var td = new List<PropertyDescriptor>();
            foreach (PropertyDescriptor pd in pdc)
                if (!pd.PropertyType.IsClass || pd.PropertyType == typeof(string))
                    td.Add(pd);

            return td;
        }

        private FilterColumnCollection GetFilterColumnsCollection(List<PropertyDescriptor> td)
        {
            var filterCollection = new FilterColumnCollection();
            foreach (var pd in td)
            {
                var ufc = new CustomFilterColumn(pd.DisplayName, pd.Name, pd.PropertyType, GetDefaultEditor(pd.PropertyType), GetClauseClass(pd.PropertyType));
                filterCollection.Add(ufc);
            }

            return filterCollection;
        }

        private static FilterColumnClauseClass GetClauseClass(Type type)
        {
            if (type == typeof(string))
                return FilterColumnClauseClass.String;
            if (type == typeof(DateTime))
                return FilterColumnClauseClass.DateTime;
            return FilterColumnClauseClass.Generic;
        }

        private static RepositoryItem GetDefaultEditor(Type type)
        {
            if (type == typeof(DateTime))
                return DefaultDateEditor;
            return DefaultEditor;
        }

        private void OnListChanged(object sender, ListChangedEventArgs e)
        {
            if (e.ListChangedType == ListChangedType.PropertyDescriptorAdded || e.ListChangedType == ListChangedType.PropertyDescriptorDeleted || e.ListChangedType == ListChangedType.PropertyDescriptorChanged)
                RaisePropertiesChanged();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
                if (_fDataSource is IBindingList oldBindingList)
                    oldBindingList.ListChanged -= OnListChanged;

            base.Dispose(disposing);
        }

        public event EventHandler RowFilterChanged
        {
            add => Events.AddHandler(FRowFilterChanged, value);
            remove => Events.RemoveHandler(FRowFilterChanged, value);
        }

        #region IFilteredComponent

        IBoundPropertyCollection IFilteredComponent.CreateFilterColumnCollection()
        {
            if (_initializing) return new FilterColumnCollection();
            return GetFilterColumns();
        }

        private static readonly object FPropertiesChanged = new object();

        event EventHandler IFilteredComponentBase.PropertiesChanged
        {
            add => Events.AddHandler(FPropertiesChanged, value);
            remove => Events.RemoveHandler(FPropertiesChanged, value);
        }

        private void RaisePropertiesChanged()
        {
            if (Events[FPropertiesChanged] is EventHandler handler)
                handler(this, EventArgs.Empty);
        }

        CriteriaOperator IFilteredComponentBase.RowCriteria
        {
            get => RowCriteria;
            set => RowCriteria = value;
        }

        event EventHandler IFilteredComponentBase.RowFilterChanged
        {
            add => RowFilterChanged += value;
            remove => RowFilterChanged -= value;
        }

        private void RaiseRowFilterChanged()
        {
            (Events[FRowFilterChanged] as EventHandler)?.Invoke(this, EventArgs.Empty);
        }

        #endregion IFilteredComponent

        #region ISupportInitialize

        private bool _initializing;

        void ISupportInitialize.BeginInit()
        {
            _initializing = true;
        }

        void ISupportInitialize.EndInit()
        {
            _initializing = false;
            RaisePropertiesChanged();
        }

        #endregion ISupportInitialize
    }
}