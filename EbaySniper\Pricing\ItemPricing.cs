﻿namespace uBuyFirst.Pricing
{
    public class ItemPricing
    {
        private CurrencyAmount _totalPrice;
        public CurrencyAmount? ItemPrice;
        public CurrencyAmount? AuctionPrice;

        public CurrencyAmount GetTotalPrice(CurrencyAmount? fullSingleShippingPrice)
        {
            if (fullSingleShippingPrice?.Value > 0)
                _totalPrice = new CurrencyAmount(ItemPrice.Value + fullSingleShippingPrice.Value, ItemPrice.Currency);
            else
                _totalPrice = ItemPrice;
            if (ItemPrice.Value.Equals(0.0))
                _totalPrice.Value = 0;
            return _totalPrice;
        }
    }
}
