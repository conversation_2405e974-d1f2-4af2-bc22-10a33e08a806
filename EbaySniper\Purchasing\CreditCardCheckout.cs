﻿using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Purchasing
{
    public static class CreditCardCheckout
    {
        public static void ExecuteCreditCardCheckout(DataList d)
        {
            var quantity = Placeoffer.PurchaseAllQuantity ? d.QuantityAvailable : 1;

            // Create a BuyOrder if needed
            if (d.Order == null || d.Order.OrderAction != Placeoffer.OrderAction.PayWithCreditCard)
                d.Order = new BuyingService.BuyOrder(d.ItemID,d.Title, d.EBaySite, quantity, d.ItemPricing.ItemPrice, Placeoffer.OrderAction.PayWithCreditCard);
            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started");
            d.Order.CookieContainer = CookieManager.ReadCookiesFirefox(new[] { $".{d.Order.EbaySite.Domain}" });
            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped");
            switch (d.Order.CheckoutStatus)
            {
                case BuyingService.Order.CheckoutState.NotStarted:
                    CreditCardService.CreditCartCheckoutCompleteSequence(d).ContinueWith(_ =>
                    {
                        // Check the final status on the order object after the sequence completes
                        if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess)
                        {
                            // Instantiate FlyoutPanelSnackBar directly
                            if (d.GridControl != null)
                            {
                                var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                                flyoutSnackBar.ShowSuccess(d.GridControl, $"Credit card payment successful for {d.Title}.");
                                // FlyoutPanelSnackBar handles its own disposal
                            }
                            d.SetStatus(ItemStatus.Active); // Or ItemStatus.Sold? Set appropriate status on success
                        }
                        else // Handle various failure states (PaymentFailed, SessionCreationFailed, ConfirmationFailed, etc.)
                        {
                            var failureMessage = d.Order.FailureReasonMessage ?? "Credit card payment failed (Unknown reason).";
                            // Instantiate FlyoutPanelSnackBar directly
                            if (d.GridControl != null)
                            {
                                var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                                flyoutSnackBar.ShowFailure(d.GridControl, $"Credit card payment failed for {d.Title}: {failureMessage}");
                                // FlyoutPanelSnackBar handles its own disposal
                            }
                        }
                        // Original line: d.SetStatus(ItemStatus.Unknown); - Replaced by specific status setting above.

                    }, TaskScheduler.FromCurrentSynchronizationContext());

                    break;
                case BuyingService.Order.CheckoutState.CreatingSession:
                    d.Order.AutoConfirmationAllowed = true;

                    break;

                case BuyingService.Order.CheckoutState.SessionCreated:
                    d.SetStatus(ItemStatus.PaymentInProgress);
                    d.Order.AutoConfirmationAllowed = true;
                    // This case handles resuming after session creation. The confirmation call itself now sets the status.
                    // We need a similar ContinueWith block here to show the flyout after confirmation finishes.
                    CreditCardService.ConfirmCreditCardPayment((BuyingService.BuyOrder)d.Order, d.Title).ContinueWith(task =>
                    {
                         // Check the final status on the order object after the confirmation completes
                        if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess)
                        {
                            // Instantiate FlyoutPanelSnackBar directly
                            if (d.GridControl != null)
                            {
                                var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                                flyoutSnackBar.ShowSuccess(d.GridControl, $"Credit card payment successful for {d.Title}.");
                                // FlyoutPanelSnackBar handles its own disposal
                            }
                            d.SetStatus(ItemStatus.Active); // Or ItemStatus.Sold? Set appropriate status on success
                        }
                        else // Handle various failure states (PaymentFailed, ConfirmationFailed, etc.)
                        {
                            var failureMessage = d.Order.FailureReasonMessage ?? "Credit card payment failed (Unknown reason).";
                            // Instantiate FlyoutPanelSnackBar directly
                            if (d.GridControl != null)
                            {
                                var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                                flyoutSnackBar.ShowFailure(d.GridControl, $"Credit card payment failed for {d.Title}: {failureMessage}");
                                // FlyoutPanelSnackBar handles its own disposal
                            }
                        }
                        // Original line: d.SetStatus(ItemStatus.Unknown); - Replaced by specific status setting above.

                    }, TaskScheduler.FromCurrentSynchronizationContext());

                    break;
            }
        }
    }
}
