﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using System.Xml.Serialization;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraTreeList;
using eBay.Service.Core.Sdk;
using eBay.Services.Finding;
using Lucene.Net.Search;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst
{
    public enum ListingType
    {
        BuyItNow,
        AuctionsStartedNow,
        AuctionsEndingNow,
        OutOfStock
    }

    [XmlInclude(typeof(ChildTerm))]
    public class Keyword2Find : TreeList.IVirtualTreeListData
    {
        private string _availableTo;
        private string _categories;
        private string[] _categoriesArr;
        private string _categoriesForRssEpn;
        private BindingList<ChildTerm> _childrenCore;
        private string[] _condition;
        private string _conditionEpn;
        private string _conditionRss2;
        private EBaySite _eBaySite;
        private string _ebaySiteName;
        private string _includeSellersEpn;
        private string _locatedIn;

        private QueryList _parentCore;
        private string[] _sellers;
        private string _sellersRss2;
        private string _sellerType;
        private string _zip;

        private Stopwatch _sw;
        public string Id { get; set; }= Guid.NewGuid().ToString();
        public string Alias;
        public CheckState KeywordEnabled;
        public string Kws;
        public bool SearchInDescription;
        public int Threads;

        [XmlIgnore]
        public string AvailableToRss2;

        private ListingType[] _listingType;

        //Type of listing
        public ListingType[] ListingType
        {
            get
            {
                if (_listingType == null || _listingType?.Length == 0)
                    return _listingType = new[] { uBuyFirst.ListingType.BuyItNow };
                else
                    return _listingType;
            }
            set => _listingType = value;
        }

        public string Zip
        {
            get
            {
                if (_zip != null)
                    return _zip;

                return "";
            }
            set => _zip = value;
        }

        public string LocatedIn
        {
            get => _locatedIn ?? CountryProvider.GenerateCountryCodeListWithAny()[0];
            set => _locatedIn = value ?? CountryProvider.GenerateCountryCodeListWithAny()[0];
        }

        public string AvailableTo
        {
            get => _availableTo ?? CountryProvider.GenerateCountryCodeList()[0];
            set => _availableTo = value ?? CountryProvider.GenerateCountryCodeList()[0];
        }

        public string ConditionRss2
        {
            get => _conditionRss2 ?? (_conditionRss2 = "");
            set => _conditionRss2 = value;
        }

        public string ConditionEpn
        {
            get => _conditionEpn ?? (_conditionEpn = "");
            set
            {
                if (value == "&condition1=")
                    _conditionEpn = "";
                else
                    _conditionEpn = value;
            }
        }

        public string[] Condition
        {
            get => _condition ?? (_condition = new string[] { });
            set
            {
                _condition = value;
                if (Condition.Length > 0 && !string.IsNullOrEmpty(Condition[0]))
                {
                    ConditionEpn = "";
                    ConditionRss2 = "&LH_ItemCondition=" + string.Join("|", Condition).Replace("Unspecified", "10");
                    var builder = new System.Text.StringBuilder();
                    builder.Append(ConditionEpn);
                    for (var i = 0; i < Condition.Length; i++)
                        if (!string.IsNullOrEmpty(Condition[i]))
                            builder.Append("&condition").Append((i + 1)).Append("=").Append(Condition[i]);
                    ConditionEpn = builder.ToString();
                }
                else
                {
                    ConditionEpn = "";
                    ConditionRss2 = "";
                }
            }
        }

        [XmlIgnore]
        public string SellersRss2
        {
            get
            {
                if (_sellersRss2 == null)
                    _sellersRss2 = "";

                return _sellersRss2;
            }
            private set => _sellersRss2 = value;
        }

        [XmlIgnore]
        public string SellersEpn
        {
            get
            {
                if (_includeSellersEpn == null)
                    _includeSellersEpn = "";

                return _includeSellersEpn;
            }
            private set => _includeSellersEpn = value;
        }

        public string[] Sellers
        {
            get => _sellers ?? (_sellers = new string[] { });
            set
            {
                _sellers = value;
                for (var i = 0; i < Sellers.Length; i++)
                    Sellers[i] = Sellers[i].Trim();
                if (string.IsNullOrEmpty(string.Join(",", Sellers)) || string.IsNullOrEmpty(SellerType))
                {
                    SellersRss2 = "";
                    SellersEpn = "";

                    return;
                }

                if (SellerType == "Include")
                    SellersRss2 = "&_fss=1&_saslop=1&_sasl=" + string.Join(",", Sellers);
                else
                    SellersRss2 = "&_fss=1&_saslop=2&_sasl=" + string.Join(",", Sellers);

                SellersEpn = "";
                for (var i = 0; i < Sellers.Length; i++)
                    if (SellerType == "Include")
                        SellersEpn += "&sellerId" + (i + 1) + "=" + Sellers[i];
                    else
                        SellersEpn += "&exclSellerId" + (i + 1) + "=" + Sellers[i];
            }
        }

        [XmlIgnore]
        public string SellersStr
        {
            get => string.Join(",", Sellers);
            private set => Sellers = value?.Split(',') ?? new string[] { };
        }

        public EBaySite EBaySite
        {
            get => _eBaySite ?? CountryProvider.GetEbaySite("eBay US");
            set => _eBaySite = value ?? CountryProvider.GetEbaySite("eBay US");
        }

        public string Categories4Api
        {
            get
            {
                if (_categories != null)
                    return _categories;

                return "";
            }
            set
            {
                _categories = value;
                SetCategories(_categories);
            }
        }

        public string CategoriesForRss2
        {
            get
            {
                if (_categoriesArr == null || _categoriesArr.Length == 0)
                    return "";
                if (_categoriesArr.Length == 1)
                    return _categoriesArr[0];

                return _categoriesArr[new Random().Next(0, _categoriesArr.Length)];
            }
        }

        public string CategoriesForRssEpn
        {
            get
            {
                if (_categoriesForRssEpn != null)
                    return _categoriesForRssEpn;

                return "";
            }
        }

        [XmlIgnore]
        public FindItemsAdvancedRequest BinAuctionRequest { get; set; }

        [XmlIgnore]
        public FindItemsAdvancedRequest RequestEndNow { get; set; }

        [XmlIgnore]
        public TimeSpan Frequency { get; set; }

        [XmlElement("Interval")]
        public long TimeSinceLastEventTicks
        {
            get => Frequency.Ticks;
            set => Frequency = new TimeSpan(value);
        }

        public Stopwatch Sw => _sw ?? (_sw = new Stopwatch());
        public DateTime LastRequested;

        public string SellerType
        {
            get
            {
                if (_sellerType != null)
                    return _sellerType;

                return "";
            }
            set
            {
                _sellerType = value;
                Sellers = Sellers;
            }
        }

        public string EbaySiteName
        {
            get
            {
                if (_ebaySiteName != null)
                    return _ebaySiteName;

                return "";
            }
            set => _ebaySiteName = value;
        }

        public double PriceMax { get; set; }

        public double PriceMin { get; set; }

        [XmlIgnore]
        public bool IgnorePostalCodeError { get; set; }

        public QueryList ParentCore
        {
            get => _parentCore;
            set
            {
                _parentCore = value;
                _parentCore?.ChildrenCore.Add(this);
            }
        }

        public BindingList<ChildTerm> ChildrenCore
        {
            get => _childrenCore ?? (_childrenCore = new BindingList<ChildTerm>());
            set => _childrenCore = value;
        }

        [XmlIgnore]
        public DateTime LastCheckSoldList { get; set; }

        [XmlIgnore]
        public GridControl GridControl { get; set; }

        public string ViewName { get; set; }
        public bool IsEndingNow => ListingType.Contains(uBuyFirst.ListingType.AuctionsEndingNow);

        [XmlIgnore]
        public int InitialSearchLimit { get; set; }

        [XmlIgnore]
        public int InitialSearchCount { get; set; }
        [XmlIgnore]
        public Query LuceneQuery { get; set; }

        [XmlIgnore]
        public DateTime EndNowRequestTimestamp;

        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            switch (info.Column.Caption)
            {
                case "Enabled":
                    info.CellData = KeywordEnabled == CheckState.Checked;

                    break;

                case "Alias":
                    info.CellData = Alias;

                    break;

                case "Keywords":
                    info.CellData = Kws;

                    break;

                case "Search in description":
                    info.CellData = SearchInDescription;

                    break;

                case "Price Min":
                    info.CellData = PriceMin;

                    break;

                case "Price Max":
                    info.CellData = PriceMax;

                    break;

                case "Category ID":
                    info.CellData = Categories4Api;

                    break;

                case "Condition":
                    info.CellData = string.Join(",", Condition);

                    break;

                case "Site":
                    info.CellData = EBaySite;

                    break;

                case "Located in":
                    info.CellData = LocatedIn;

                    break;

                case "Ships to":
                    info.CellData = AvailableTo;

                    break;

                case "Ship Zipcode":
                    info.CellData = Zip;

                    break;

                case "Sellers":
                    info.CellData = SellersStr;

                    break;

                case "Seller type":
                    info.CellData = SellerType;

                    break;

                case "Interval":
                    info.CellData = Frequency;

                    break;

                case "Threads":
                    info.CellData = Threads;

                    break;

                case "View":
                    info.CellData = ViewName;

                    break;

                case "Type":
                    info.CellData = string.Join(",", ListingType);

                    break;

                default:
                    info.CellData = "Null";

                    break;
            }
        }

        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            info.Children = _childrenCore;
        }

        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            if (info.Node is Keyword2Find)
                switch (info.Column.Caption)
                {
                    case "Enabled":
                        if (info.NewCellData is bool dataIsBool)
                        {
                            KeywordEnabled = dataIsBool ? CheckState.Checked : CheckState.Unchecked;
                        }

                        if (info.NewCellData is string dataIsString)
                        {
                            KeywordEnabled = dataIsString == "Checked" ? CheckState.Checked : CheckState.Unchecked;
                        }

                        info.Column.TreeList.PostEditor();

                        break;
                    case "Alias":
                        Alias = info.NewCellData as string;

                        break;

                    case "Keywords":
                        Kws = (info.NewCellData as string)?.Trim().Trim(',');

                        break;

                    case "Search in description":
                        SearchInDescription = (bool)info.NewCellData;

                        break;

                    case "Price Min":
                        var newCellData = info.NewCellData.ToString();
                        if (double.TryParse(newCellData, out var priceMin))
                            PriceMin = priceMin;
                        else
                            PriceMin = 0;

                        break;

                    case "Price Max":
                        if (double.TryParse(info.NewCellData.ToString(), out var priceMax))
                            PriceMax = priceMax;
                        else
                            PriceMax = 0;

                        break;

                    case "Category ID":
                        if (info.NewCellData is string categoryStr)
                        {
                            Categories4Api = categoryStr;
                        }

                        if (info.NewCellData is GUI.Category categoryCategory)
                        {
                            Categories4Api = categoryCategory.CategoryID;
                        }

                        break;

                    case "Condition":
                        if (string.IsNullOrEmpty(info.NewCellData as string))
                        {
                            Condition = new string[] { };

                            break;
                        }

                        var s = (string)info.NewCellData;
                        if (s == "1000, 1500, 1750, 2000, 2010, 2020, 2030, 2500, 2750, 3000, 4000, 5000, 6000, 7000, Unspecified")
                        {
                            Condition = new string[] { };

                            break;
                        }

                        var v = s.Split(',');
                        for (var index = 0; index < v.Length; index++)
                            v[index] = v[index].Trim();
                        Condition = v;

                        break;

                    case "Site":
                        EBaySite = CountryProvider.GetEbaySite(info.NewCellData as string);
                        EbaySiteName = info.NewCellData as string;

                        break;

                    case "Located in":
                        LocatedIn = info.NewCellData as string;

                        break;

                    case "Ships to":
                        AvailableTo = info.NewCellData as string;

                        break;

                    case "Ship Zipcode":
                        Zip = info.NewCellData as string;

                        break;

                    case "Sellers":
                        SellersStr = info.NewCellData as string;

                        break;

                    case "Seller type":
                        SellerType = info.NewCellData as string;

                        break;

                    case "Interval":
                        if (info.NewCellData != null)
                            Frequency = (TimeSpan)info.NewCellData;

                        break;

                    case "Threads":
                        if (info.NewCellData == null || (int)info.NewCellData < 1)
                            Threads = 1;
                        else
                            Threads = (int)info.NewCellData;

                        break;

                    case "View":
                        ViewName = info.NewCellData as string;

                        break;

                    case "Type":
                        if (string.IsNullOrEmpty(info.NewCellData as string))
                        {
                            break;
                        }

                        var enabledListingTypesStr = (string)info.NewCellData;
                        var enabledTypes = enabledListingTypesStr.Split(',').Select(enabledType => (ListingType)Enum.Parse(typeof(ListingType), enabledType.Trim())).ToList();
                        if (enabledTypes.Contains(uBuyFirst.ListingType.OutOfStock) && enabledTypes.Count > 1)
                        {
                            ListingType = new[] { uBuyFirst.ListingType.OutOfStock };
                            info.Column.TreeList.PostEditor();
                            info.Column.TreeList.RefreshDataSource(); //
                            XtraMessageBox.Show("Out of stock monitoring is enabled.\r\n"
                                                + "Please, enter your items IDs separated by comma into \"Keywords\" column.\r\n"
                                                + "BuyItNow and Auction search have been disabled.");
                        }
                        else
                        {
                            ListingType = enabledTypes.Distinct().ToArray();
                        }

                        break;
                }
        }

        private void SetCategories(string categories)
        {
            if (string.IsNullOrEmpty(categories?.Trim()))
                return;

            _categories = categories;
            _categoriesArr = categories.Split(',');
            _categoriesForRssEpn = "";
            for (var i = 0; i < _categoriesArr.Length; i++)
            {
                _categoriesArr[i] = _categoriesArr[i].Trim();
                _categoriesForRssEpn += "&categoryId" + (i + 1) + "=" + _categoriesArr[i];
            }
        }

        public override string ToString()
        {
            return Alias;
        }

        
        
    }
}
