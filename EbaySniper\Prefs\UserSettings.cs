﻿using System;
using System.Collections.Generic;
using System.Drawing;
using uBuyFirst.Tools;

namespace uBuyFirst.Prefs
{
    public static class UserSettings
    {
        public static string BrowserBg;
        public static int BrowserFontSize;
        public static Highlights Highlightsvalues;
        public static int HighlightsvaluesVersion;
        public static bool CanShowEbaySearchEditor = true;
        public static int MaxInitialRows = 300;
        public static TimeZoneInfo CurrentTimeZoneInfo;
        public static decimal GridRowHeight;
        public static Font GridViewFont { get; set; }
        public static OpenInBrowserEnum OpenInBrowser { get; set; }
        public static SerializableDictionary<string, string> MakeOfferMessages { get; set; }
        public static string MakeOfferSelectedMessage { get; set; }
        public static bool SkipBuyConfirmation { get; set; }
        public static int InitialResultsLimit { get; set; } = 10;
        public static int MaxResultsCount { get; set; } = 500;
        public static bool ClickOnPriceOpensProductPage { get; set; }
        public static HashSet<string> BlockedSellers { get; set; } = new();
        public static string SyncSearchTermsUrl = "";
        public static bool SyncSearchTermsEnabled = false;
        public static int SyncSearchTermsInterval = 3600;
        public static string SyncSearchTermsFileHash { get; set; } = "";
        public static bool ExternalDataEnabled { get; set; }
        public static string ExternalEndpointUrl { get; set; } = "";
        public static string InternalEndpointUrl { get; set; } = "http://localhost:8000/match_mydata";
        public static bool IsInternalEndpoint { get; set; } = false;
        public static bool SendDescriptionAndPictures { get; set; }

        public struct Highlights
        {
            public Color Color1;
            public Color Color2;
            public Color Color3;
            public string HexColor1;
            public string HexColor2;
            public string HexColor3;
            public string[] Words1;
            public string[] Words2;
            public string[] Words3;
        }

        public static class Shortcuts
        {
            public static int BuyModifier { get; set; }
            public static int BuyKey { get; set; }
            public static int GoToCheckoutModifier { get; set; }
            public static int GoToCheckoutKey { get; set; }
            public static int QuickBuyModifier { get; set; }
            public static int QuickBuyKey { get; set; }
            public static int MakeOfferModifier { get; set; }
            public static int MakeOfferKey { get; set; }
            public static int ImmediateBuyKey { get; set; }
            public static int ImmediateBuyModifier { get; set; }
        }

        public enum OpenInBrowserEnum
        {
            Disabled,
            ItemPage,
            CheckoutPage
        }
    }
}
