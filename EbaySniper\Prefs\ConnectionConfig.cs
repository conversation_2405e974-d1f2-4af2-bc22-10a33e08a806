﻿using System;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;
using System.Windows.Forms;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using Newtonsoft.Json;
using uBuyFirst.Search.Status;

namespace uBuyFirst.Prefs
{
    static class ConnectionConfig
    {
        public static string TradingApiHost = "http://data.ubuyfirst.net";
        public static string FindingApiHost = "http://data.ubuyfirst.net";
        public static string ShoppingApiHost = "http://data.ubuyfirst.net";
        public static string TradingApiKey = "ubuyfirstapikey";
        public static string FindingApiKey = "ubuyfirstapikey";
        public static string ShoppingApiKey = "ubuyfirstapikey";
        public static string RuName = "ubuyfirstruname";
        public static string SoapServerAuth = "https://data.ubuyfirst.net/uBuyFirst/f/data.php";
        private static string _browseAPIUrl = "http://data.ubuyfirst.net/buy/browse/v1/";
        public static string BrowseAPIUrlFallback = "http://data.ubuyfirst.net/buy/browse/v1/";
        public static string TaxonomyAPIURL = "http://data.ubuyfirst.net/commerce/taxonomy/v1/";
        public static string SellerUserInfoUrl = "https://userinfo.ubuyfirst.net/v1/userinfo";
        public static bool TradingAPIEnabled = true;
        public static bool FindingAPIEnabled = true;
        public static bool ShoppingAPIEnabled = true;
        public static bool BrowseAPIEnabled = true; //Debugger.IsAttached;
        public static int WatchlistUpdateInterval = 600;
        public static string Token = "";
        public static string Token2 = "";
        public static int ItemStatusInterval = 5;

        public static string[] CA = Array.Empty<string>();

        public static event EventHandler<string> BrowseAPIUrlChanged;

        public static string BrowseAPIUrl => _browseAPIUrl;

        private static void ParseServerJson(string data)
        {
            if (string.IsNullOrEmpty(data))
                return;

            try
            {
                dynamic json = JsonConvert.DeserializeObject(data);
                TradingApiHost = json.TradingApiHost;
                FindingApiHost = json.FindingApiHost;
                ShoppingApiHost = json.ShoppingApiHost;
                TradingApiKey = json.TradingApiKey;
                FindingApiKey = json.FindingApiKey;
                ShoppingApiKey = json.ShoppingApiKey;
                RuName = json.RuName;
                SoapServerAuth = json.SoapServerAuth;
                TradingAPIEnabled = json.TradingAPIEnabled;
                FindingAPIEnabled = json.FindingAPIEnabled;
                ShoppingAPIEnabled = json.ShoppingAPIEnabled;
                BrowseAPIEnabled = json.BrowseAPIEnabled;
                CheckoutEnabled = json.CheckoutEnabled;
                SkipBuyConfirmation = json.SkipBuyConfirmation;
                Token = json.Token;
                TaxonomyAPIURL = json.TaxonomyAPIURL;
                BrowseAPIUrlFallback = json.BrowseAPIUrl;
                MQTTEndPoint1 = json.MQTTEndPoint1;
                MQTTEndPoint2 = json.MQTTEndPoint2;
                MQTTEnabled = json.MQTTEnabled;
                WatchlistUpdateInterval = json.WatchlistUpdateInterval;
                SellerUserInfoUrl = json.SellerUserInfoUrl;
                ShowDataButton = json.ShowDataButton;
                //TradingAPIEnabled = false;
                Token2 = json.Token2;

                if (int.TryParse(json.ItemStatusInterval?.ToString(), out int itemStatusInterval))
                {
                    if (itemStatusInterval > 0)
                        GetItemsStatus.CheckInterval = itemStatusInterval;
                }

                if (int.TryParse(json.MaxUpdateSeconds?.ToString(), out int maxUpdateSeconds))
                {
                    if (maxUpdateSeconds > 0)
                        GetItemsStatus.MaxUpdateSeconds = maxUpdateSeconds;
                }

                if (double.TryParse(json.BrowseSearchCoefficient?.ToString(), out double browseSearchCoefficient))
                {
                    if (browseSearchCoefficient >= 0)
                        Config.BrowseSearchCoefficient = browseSearchCoefficient;
                }

                if (double.TryParse(json.FindingAPICoefficient?.ToString(), out double findingAPICoefficient))
                {
                    if (findingAPICoefficient >= 0)
                        Config.FindingAPICoefficient = findingAPICoefficient;
                }

                if (string.IsNullOrEmpty(Token))
                {
                    SetBrowseAPIUrl(json.BrowseAPIUrl.ToString());
                }
                else
                {
                    SetBrowseAPIUrl("https://api.ebay.com/buy/browse/v1/");
                }

                CA = json.CA.ToString().Split(';');
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
        }

        public static bool MQTTEnabled { get; set; } = true;
        public static string MQTTEndPoint1 { get; set; } = "wss://sync.ubuyfirst.net/ws1";
        public static string MQTTEndPoint2 { get; set; } = "wss://sync.ubuyfirst.net/ws2";

        public static bool CheckoutEnabled { get; set; }
        public static bool SkipBuyConfirmation { get; set; }
        public static bool ShowDataButton { get; set; }
        public static void SetBrowseAPIUrl(string url)
        {
            _browseAPIUrl = url;
            BrowseAPIUrlChanged.Invoke(null, _browseAPIUrl);
        }

        private static ApiCredential GetApiCredential()
        {
            var apiCredential = new ApiCredential();
            apiCredential.eBayToken = "Token";
            apiCredential.ApiAccount.Application = "AppID";
            apiCredential.ApiAccount.Developer = "DevID";
            apiCredential.ApiAccount.Certificate = "CertID";
            return apiCredential;
        }

        public static ApiContext GetGuestApiContext(SiteCodeType siteCodeType)
        {
            var apiContext = new ApiContext();
            apiContext.RuName = RuName;
            apiContext.Site = siteCodeType;
            apiContext.ApiCredential = GetApiCredential();
            apiContext.SoapApiServerUrl = TradingApiHost + "/wsapi";
            return apiContext;
        }

        public static ApiContext GetAuthenticatedApiContext(SiteCodeType siteCodeType)
        {
            var apiContext = new ApiContext();
            apiContext.RuName = RuName;
            apiContext.Site = siteCodeType;
            apiContext.ApiCredential = GetApiCredential();
            apiContext.SoapApiServerUrl = SoapServerAuth;
            apiContext.SignInUrl = "https://signin.ebay.com/ws/eBayISAPI.dll?SignIn";
            return apiContext;
        }

        public static ApiContext GetApiContextPlaceOffer(SiteCodeType siteCodeType, string ebayToken)
        {
            var apiContext = GetAuthenticatedApiContext(siteCodeType);
            apiContext.ApiCredential.eBayToken = ebayToken;
            return apiContext;
        }

        internal static async Task SetApiConfig()
        {
            await Task.Run(() =>
            {
                try
                {
                    if (Program.Sandbox)
                    {
                        // Set sandbox configuration
                        SetSandboxConfig();
                        return;
                    }

                    // Attempt to fetch and parse the server configuration up to 3 times
                    for (var attempt = 1; attempt <= 3; attempt++)
                    {
                        try
                        {
                            var host = "data.ubuyfirst.net";
                            if (attempt == 3)
                            {
                                FetchOldWay();
                                return;
                            }

                            var securedConfigPath = $"https://{host}/uBuyFirst/f/data.php"
                                                    + $"?id={ProgramState.HWID}&sn={ProgramState.SerialNumber}"
                                                    + $"&ver={ProgramState.UBFVersion}&rand={new Random().Next(100, 10000)}";
                            var response = new WebClient().DownloadString(securedConfigPath);
                            var decryptedMessage = Decryptor.DecryptMessage(response);

                            if (!string.IsNullOrEmpty(decryptedMessage))
                            {
                                ParseServerJson(decryptedMessage);
                                BrowseAPIUrlChanged.Invoke(null, _browseAPIUrl);
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            if (attempt == 3)
                            {
                                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Form1.Log.Error("Unexpected error: {0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                }
            });
        }

        private static void SetSandboxConfig()
        {
            //ConnectionConfig.TradingApiHost = json.TradingApiHost;
            TradingApiKey = "ub-appone-SBX-d4d8cb72c-1e6c2b6d";

            RuName = "Roman_Johns-ub-appone-SBX-d-fypipihmu";

            //ConnectionConfig.SoapServerAuth = json.SoapServerAuth;
            //ConnectionConfig.Certificates = json.Certificates.ToString().Split(';');
        }

        private static void FetchOldWay()
        {

            var host = "ubuyfirst.com";

            var message = $@"{{""hwid"":""{ProgramState.HWID}"",""sn"":""{ProgramState.SerialNumber}"",""ver"":""{ProgramState.UBFVersion}""}}";
            var securedConfigPath = $"https://{host}/uBuyFirst/f/data.php?id={ProgramState.HWID}&sn={ProgramState.SerialNumber}&ver={ProgramState.UBFVersion}&rand={new Random().Next(100, 10000)}";

            Form1.Secure.SetRemotePhpScriptLocation(securedConfigPath);
            var response = TrySendMessage(message);

            if (!string.IsNullOrEmpty(response))
            {
                ParseServerJson(response);

                BrowseAPIUrlChanged.Invoke(null, _browseAPIUrl);
            }
        }

        private static string TrySendMessage(string message)
        {
            try
            {
                return Form1.Secure.SendMessage(message);
            }
            catch (Exception ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                if (ex.Message.Contains("Could not establish trust relationship for the SSL/TLS secure channel"))
                    Application.Exit();
            }

            return "";
        }
    }
}
