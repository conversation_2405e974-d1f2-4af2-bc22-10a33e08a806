<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MQTTnet.Extensions.ManagedClient</name>
    </assembly>
    <members>
        <member name="P:MQTTnet.Extensions.ManagedClient.ApplicationMessageProcessedEventArgs.Exception">
            <summary>
            Then this is _null_ the message was processed successfully without any error.
            </summary>
        </member>
        <member name="P:MQTTnet.Extensions.ManagedClient.ConnectingFailedEventArgs.ConnectResult">
            <summary>
            This is null when the connection was failing and the server was not reachable.
            </summary>
        </member>
        <member name="F:MQTTnet.Extensions.ManagedClient.ManagedMqttClient._reconnectSubscriptions">
            <summary>
                The subscriptions are managed in 2 separate buckets:
                <see
                    cref="F:MQTTnet.Extensions.ManagedClient.ManagedMqttClient._subscriptions" />
                and
                <see
                    cref="F:MQTTnet.Extensions.ManagedClient.ManagedMqttClient._unsubscriptions" />
                are processed during normal operation
                and are moved to the
                <see
                    cref="F:MQTTnet.Extensions.ManagedClient.ManagedMqttClient._reconnectSubscriptions" />
                when they get processed. They can be accessed by
                any thread and are therefore mutex'ed.
                <see
                    cref="F:MQTTnet.Extensions.ManagedClient.ManagedMqttClient._reconnectSubscriptions" />
                get sent to the broker
                at reconnect and are solely owned by
                <see
                    cref="M:MQTTnet.Extensions.ManagedClient.ManagedMqttClient.MaintainConnectionAsync(System.Threading.CancellationToken)" />
                .
            </summary>
        </member>
        <member name="P:MQTTnet.Extensions.ManagedClient.ManagedMqttClientOptions.MaxTopicFiltersInSubscribeUnsubscribePackets">
            <summary>
            Defines the maximum amount of topic filters which will be sent in a SUBSCRIBE/UNSUBSCRIBE packet.
            Amazon Web Services (AWS) limits this number to 8. The default is int.MaxValue.
            </summary>
        </member>
    </members>
</doc>
