﻿using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.GUI;
using uBuyFirst.Images;
using uBuyFirst.Item;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst.Grid
{
    internal static class GridBuilder
    {
        public static DataTable DefaultDataTable;

        public static GridControl CreateGridControl(string viewName)
        {
            DefaultDataTable = GetDefaultDataTable();
            var gridControl = GetDefaultGridControl(viewName);
            gridControl.DataSource = DefaultDataTable;

            var grView = (AdvBandedGridView)gridControl.MainView;
            grView.Appearance.Row.Font = UserSettings.GridViewFont;
            grView.RowHeight = (int)UserSettings.GridRowHeight;
            grView.PopulateColumns(gridControl.DataSource);

            ResetGridViewLayout(grView);

            foreach (var specific in ItemSpecifics.CategorySpecificsList)
            {
                if (DefaultDataTable.Columns.Contains(specific.CategoryName))
                    continue;

                DefaultDataTable.Columns.Add(specific.CategoryName);
                ItemSpecifics.AddItemSpecificColumnToGridView(grView, specific.CategoryName);
            }

            FormatRuleManager.ApplyGridFormatRules(grView);

            return gridControl;
        }

        public static void CopyLayoutFromExistingResultsGrid(string viewName)
        {
            Stream layoutStream = new MemoryStream();
            (ResultsView.ViewsDict["Results"].MainView as AdvBandedGridView)?.SaveLayoutToStream(layoutStream);
            layoutStream.Seek(0, SeekOrigin.Begin);
            (ResultsView.ViewsDict[viewName].MainView as AdvBandedGridView)?.RestoreLayoutFromStream(layoutStream);
        }

        public static DataTable GetDefaultDataTable()
        {
            var dt = new DataTable();
            dt.Columns.Add("ItemID");
            dt.Columns["ItemID"].Unique = true;
            var primaryKeyColumns = new DataColumn[1];
            primaryKeyColumns[0] = dt.Columns["ItemID"];
            dt.PrimaryKey = primaryKeyColumns;
            dt.Columns.Add("Term");
            dt.Columns.Add("Thumbnail", typeof(Image));
            dt.Columns.Add("Title");
            dt.Columns.Add("Total Price", typeof(double));
            dt.Columns.Add("Condition");
            dt.Columns.Add("Returns");
            dt.Columns.Add("Best Offer");
            dt.Columns.Add("Commit To Buy");
            dt.Columns.Add("Quantity", typeof(decimal));
            dt.Columns.Add("Posted Time", typeof(DateTime));
            dt.Columns.Add("Found Time", typeof(DateTime));
            dt.Columns.Add("Sold Time", typeof(DateTime));
            if (Debugger.IsAttached)
            {
                dt.Columns.Add("Status Time", typeof(DateTime));
            }

            dt.Columns.Add("Status");
            dt.Columns.Add("Location");
            dt.Columns.Add("From Country");
            dt.Columns.Add("To Country");
            dt.Columns.Add("AutoPay");

            dt.Columns.Add("Category ID");
            dt.Columns.Add("Category Name");
            dt.Columns.Add("Condition Description");

            dt.Columns.Add("Description");
            dt.Columns.Add("Feedback Rating", typeof(decimal));
            dt.Columns.Add("Feedback Score", typeof(decimal));
            dt.Columns.Add("Item Price", typeof(double));

            dt.Columns.Add("Seller Name");
            dt.Columns.Add("Shipping", typeof(double));
            dt.Columns.Add("Shipping Type");
            dt.Columns.Add("Shipping Delivery");
            dt.Columns.Add("Ship Additional Item", typeof(double));

            dt.Columns.Add("Source");
            dt.Columns.Add("Ebay Website");
            dt.Columns.Add("Page Views", typeof(int));
            dt.Columns.Add("Best Offer Count", typeof(int));
            dt.Columns.Add("Relist Parent ID");
            dt.Columns.Add("Brand");
            dt.Columns.Add("Model");
            dt.Columns.Add("UPC");
            dt.Columns.Add("MPN");
            dt.Columns.Add("Product Reference ID");
            dt.Columns.Add("VAT Number");
            dt.Columns.Add("Listing Type");
            dt.Columns.Add("Bids", typeof(int));
            dt.Columns.Add("Time Left", typeof(TimeSpan));
            dt.Columns.Add("Auction Price", typeof(double));
            dt.Columns.Add("Sub Search");
            dt.Columns.Add("Store Name");
            dt.Columns.Add("Payment");
            dt.Columns.Add("Variation");
            dt.Columns.Add("Shipping Days", typeof(int));
            dt.Columns.Add("Dispatch Days", typeof(int));
            dt.Columns.Add("Seller Registration", typeof(DateTimeOffset));
            dt.Columns.Add("Seller Country");
            dt.Columns.Add("Seller Store");
            dt.Columns.Add("Seller Business");
            dt.Columns.Add("Title Match");
            dt.Columns.Add("Authenticity");

            dt.Columns.Add("Blob", typeof(DataList));

            return dt;
        }

        private static GridControl GetDefaultGridControl(string alias)
        {
            var gridControl1 = new GridControl();
            gridControl1.Text = alias;
            var gridView1 = new AdvBandedGridView();
            ((ISupportInitialize)gridControl1).BeginInit();
            ((ISupportInitialize)gridView1).BeginInit();
            var gridBand1 = new GridBand();
            var gridBand2 = new GridBand();
            var gridBand3 = new GridBand();
            var repositoryItemImageEdit1 = new RepositoryItemImageEdit();

            var repositoryItemTextEditEmpty = new RepositoryItemTextEdit();
            ((ISupportInitialize)repositoryItemTextEditEmpty).BeginInit();
            repositoryItemTextEditEmpty.AutoHeight = false;
            repositoryItemTextEditEmpty.ContextImageOptions.Alignment = ContextImageAlignment.Far;
            repositoryItemTextEditEmpty.Name = "repositoryItemTextEditEmpty";
            repositoryItemTextEditEmpty.ReadOnly = true;
            ((ISupportInitialize)repositoryItemTextEditEmpty).EndInit();

            var repositoryItemTextEditCart = new RepositoryItemTextEdit();
            ((ISupportInitialize)repositoryItemTextEditCart).BeginInit();
            repositoryItemTextEditCart.AutoHeight = false;
            repositoryItemTextEditCart.ContextImageOptions.Alignment = ContextImageAlignment.Far;
            repositoryItemTextEditCart.ContextImageOptions.Image = Resources.Cart16;
            repositoryItemTextEditCart.Name = "repositoryItemTextEditCart";
            repositoryItemTextEditCart.ReadOnly = true;
            ((ISupportInitialize)repositoryItemTextEditCart).EndInit();

            var repositoryItemTextEditPaypal = new RepositoryItemTextEdit();
            ((ISupportInitialize)repositoryItemTextEditPaypal).BeginInit();
            repositoryItemTextEditPaypal.AutoHeight = false;
            repositoryItemTextEditPaypal.ContextImageOptions.Alignment = ContextImageAlignment.Far;
            repositoryItemTextEditPaypal.ContextImageOptions.Image = null;
            repositoryItemTextEditPaypal.ContextImageOptions.SvgImage = Resources.CreditCard;
            repositoryItemTextEditPaypal.ContextImageOptions.SvgImageSize = new Size(16, 16);
            repositoryItemTextEditPaypal.Name = "repositoryItemTextEditPaypal";
            repositoryItemTextEditPaypal.ReadOnly = true;
            ((ISupportInitialize)repositoryItemTextEditPaypal).EndInit();
            var repositoryItemTextBlockedSeller = new RepositoryItemTextEdit();
            ((ISupportInitialize)repositoryItemTextBlockedSeller).BeginInit();
            repositoryItemTextBlockedSeller.AutoHeight = false;
            repositoryItemTextBlockedSeller.ContextImageOptions.Alignment = ContextImageAlignment.Far;
            repositoryItemTextBlockedSeller.ContextImageOptions.Image = null;
            repositoryItemTextBlockedSeller.ContextImageOptions.SvgImage = Resources.Blocked;
            repositoryItemTextBlockedSeller.ContextImageOptions.SvgImageSize = new Size(16, 16);
            repositoryItemTextBlockedSeller.Name = "repositoryItemTextBlockSeller";

            repositoryItemTextBlockedSeller.ReadOnly = true;
            ((ISupportInitialize)repositoryItemTextBlockedSeller).EndInit();

            gridControl1.AllowRestoreSelectionAndFocusedRow = DefaultBoolean.True;
            gridControl1.Dock = DockStyle.Fill;
            gridControl1.Location = new Point(0, 0);
            gridControl1.MainView = gridView1;
            gridControl1.Name = "gridControl" + alias;
            gridControl1.RepositoryItems.AddRange(new[]
            {
                repositoryItemImageEdit1, repositoryItemTextEditEmpty, repositoryItemTextEditCart, repositoryItemTextEditPaypal, repositoryItemTextBlockedSeller, repositoryItemTextBlockedSeller
            });

            gridControl1.Size = new Size(200, 320);
            gridControl1.TabIndex = 0;
            gridControl1.ViewCollection.AddRange(new BaseView[] { gridView1 });

            gridControl1.Paint += GridViewEvents.gridControl1_Paint;
            //todo:gridControl1.MouseEnter += new System.EventHandler(gridControl1_MouseEnter);
            //
            // gridView1
            //
            gridView1.GridControl = gridControl1;
            gridView1.Bands.AddRange(new[] { gridBand1, gridBand2, gridBand3 });

            gridView1.Appearance.Row.Options.UseTextOptions = true;
            gridView1.Appearance.Row.TextOptions.HAlignment = HorzAlignment.Near;
            gridView1.Appearance.Row.TextOptions.VAlignment = VertAlignment.Center;
            gridView1.Appearance.Row.TextOptions.WordWrap = WordWrap.Wrap;

            gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;

            gridView1.GroupSummary.AddRange(new GridSummaryItem[] { new GridGroupSummaryItem(SummaryItemType.Count, "ItemID", null, "     [{0}]", 1) });

            gridView1.Name = "gridView1";
            gridView1.OptionsBehavior.AllowDeleteRows = DefaultBoolean.True;
            gridView1.OptionsBehavior.AllowIncrementalSearch = true;
            gridView1.OptionsBehavior.KeepGroupExpandedOnSorting = false;
            gridView1.OptionsCustomization.AllowChangeBandParent = true;
            gridView1.OptionsCustomization.AllowChangeColumnParent = true;
            gridView1.OptionsCustomization.CustomizationFormSearchBoxVisible = true;
            gridView1.OptionsCustomization.UseAdvancedCustomizationForm = DefaultBoolean.True;
            gridView1.OptionsFilter.UseNewCustomFilterDialog = true;
            gridView1.OptionsMenu.ShowAddNewSummaryItem = DefaultBoolean.False;
            gridView1.OptionsMenu.ShowGroupSummaryEditorItem = true;
            gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            gridView1.OptionsSelection.EnableAppearanceHideSelection = false;
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsView.AllowGlyphSkinning = true;
            gridView1.OptionsView.GroupDrawMode = GroupDrawMode.Office;
            gridView1.OptionsView.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
            gridView1.OptionsView.ShowGroupPanel = false;

            gridView1.RowCellClick += GridViewEvents.gridView1_RowCellClick;
            gridView1.PopupMenuShowing += GridViewEvents.gridView1_PopupMenuShowing;
            gridView1.ShowCustomizationForm += GridViewEvents.gridView1_ShowBands;
            gridView1.HideCustomizationForm += GridViewEvents.gridView1_ShowBands;
            gridView1.FocusedRowChanged += GridViewEvents.gridView1_FocusedRowChanged;
            gridView1.SelectionChanged += GridViewEvents.gridView1_SelectionChanged;
            gridView1.TopRowChanged += GridViewEvents.gridView1_TopRowChanged;
            gridView1.RowCountChanged += GridViewEvents.gridView1_RowCountChanged;
            gridView1.RowDeleted += GridViewEvents.gridView1_RowDeleted;
            gridView1.CustomColumnDisplayText += GridViewEvents.gridView1_CustomColumnDisplayText;
            gridView1.MouseMove += GridViewEvents.gridView1_MouseMove;
            gridView1.MouseLeave += GridViewEvents.gridView1_MouseLeave;

            gridView1.DoubleClick += GridViewEvents.gridView1_DoubleClick;
            gridView1.KeyDown += GridViewKeyDown;
            gridView1.KeyUp += GridViewKeyUp;
            gridView1.CustomRowCellEdit += ShowGridPriceButtons;
            gridView1.Click += GridViewEvents.GridView1_Click;
            gridView1.GotFocus += GridViewEvents.GridView1_GotFocus;
            gridView1.FormatRuleDataUpdateCustomTrigger += GridViewEvents.GridView1_FormatRuleDataUpdateCustomTrigger;
            //
            // gridBand1
            //

            gridBand1.Caption = "General";
            gridBand1.Name = "gridBand1";
            gridBand1.OptionsBand.AllowMove = false;
            gridBand1.OptionsBand.AllowPress = false;
            gridBand1.OptionsBand.ShowCaption = false;
            gridBand1.VisibleIndex = 0;
            gridBand1.Width = 75;

            //
            // gridBand2
            //
            gridBand2.Caption = "Other";
            gridBand2.Name = "gridBand2";
            gridBand2.OptionsBand.ShowCaption = false;
            gridBand2.VisibleIndex = 1;
            //
            // gridBand3
            //
            gridBand3.Caption = "Custom";
            gridBand3.Name = "gridBand3";
            gridBand3.OptionsBand.ShowCaption = false;
            gridBand3.VisibleIndex = 2;
            //
            // repositoryItemImageEdit1
            //
            repositoryItemImageEdit1.AutoHeight = false;
            repositoryItemImageEdit1.Buttons.AddRange(new[] { new EditorButton(ButtonPredefines.Combo) });

            repositoryItemImageEdit1.Name = "repositoryItemImageEdit1";
            gridView1.DataController.KeepGroupRowsExpandedOnRefresh = false;
            var gridViewToolTipController = new ToolTipController();
            gridControl1.ToolTipController = gridViewToolTipController;
            gridViewToolTipController.InitialDelay = 100;
            gridViewToolTipController.Rounded = false;
            gridViewToolTipController.ShowShadow = false;

            gridViewToolTipController.GetActiveObjectInfo += ToolTipController2_GetActiveObjectInfo;

            ((ISupportInitialize)gridControl1).EndInit();
            ((ISupportInitialize)gridView1).EndInit();

            return gridControl1;
        }

        private static void ToolTipController2_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            ShowTooltipThumbnail(e);
        }

        private static void ShowTooltipThumbnail(ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            if (!(e.SelectedControl is GridControl gridControl1))
                return;

            ToolTipControlInfo info = null;
            try
            {
                if (!(gridControl1.GetViewAt(e.ControlMousePosition) is GridView view))
                    return;

                GridHitInfo hitInfo = view.CalcHitInfo(e.ControlMousePosition);

                //System.Diagnostics.Debug.WriteLine(hitInfo.Column?.RealColumnEdit.GetType().ToString());
                if (hitInfo.HitTest != GridHitTest.RowCell)
                    return;

                if (hitInfo.Column.FieldName == "Item Price" || hitInfo.Column.FieldName == "Total Price")
                {
                    if (view.GetViewInfo() is GridViewInfo viewInfo)
                    {
                        var cellInfo = viewInfo.GetGridCellInfo(hitInfo.RowHandle, hitInfo.Column);
                        var editInfo = cellInfo.ViewInfo as DevExpress.XtraEditors.ViewInfo.TextEditViewInfo;
                        Point point = e.ControlMousePosition;
                        point.Offset(-cellInfo.Bounds.X, -cellInfo.Bounds.Y);
                        if (editInfo != null && editInfo.ContextImageBounds.Contains(point))
                        {
                            if (cellInfo.Editor.Name == "repositoryItemTextEditPaypal")
                            {
                                info = new ToolTipControlInfo(hitInfo.RowHandle + "," + hitInfo.Column, "Checkout");
                                info.Interval = 500;
                            }

                            if (cellInfo.Editor.Name == "repositoryItemTextEditCart")
                            {
                                info = new ToolTipControlInfo(hitInfo.RowHandle + "," + hitInfo.Column, "Commit To Buy");
                                info.Interval = 500;
                            }
                        }

                        if (cellInfo.CellValue is DBNull)
                        {
                            info = new ToolTipControlInfo(hitInfo.RowHandle + "," + hitInfo.Column, "See 'Auction Price' column");
                            info.Interval = 1000;
                        }
                    }
                }

                if (hitInfo.Column.FieldName == "Thumbnail")
                {
                    if (hitInfo.RowHandle < 0)
                        return;

                    if (view.GetRowCellValue(hitInfo.RowHandle, "Thumbnail") is Bitmap cellIm)
                    {
                        info = CreateThumbnailTooltip(cellIm, hitInfo);
                    }
                    else
                    {
                        try
                        {
                            var row = view.GetDataRow(hitInfo.RowHandle);

                            if (row == null)
                                return;

                            DownloadRowThumbnail(row);
                        }
                        catch (Exception exception)
                        {
                            Debug.WriteLine(exception);
                        }
                    }
                }
            }
            finally
            {
                e.Info = info;
            }
        }

        private static async void DownloadRowThumbnail(DataRow row)
        {
            if (row.RowState == DataRowState.Detached || row["Blob"] == DBNull.Value)
                return;

            var d = (DataList)row["Blob"];
            var downloadPath = ImageTools.UrlToFilePath(d.GalleryUrl);
            if (!File.Exists(downloadPath))
                await ImageTools.DownloadImageToDiskAsync(d.GalleryUrl, downloadPath);

            var image = await ImageTools.ReadFileToBitmap(downloadPath);

            if (row.RowState == DataRowState.Detached || row["Blob"] == DBNull.Value)
                row["Thumbnail"] = image;
        }

        private static ToolTipControlInfo CreateThumbnailTooltip(Bitmap bitmap, GridHitInfo hitInfo)
        {
            ToolTipControlInfo info;
            var toolTipItem = new ToolTipItem();
            toolTipItem.Image = bitmap;

            var superTip = new SuperToolTip();
            superTip.Items.Add(toolTipItem);

            info = new ToolTipControlInfo(hitInfo.RowHandle + "," + hitInfo.Column, "");
            info.SuperTip = superTip;

            return info;
        }

        public static void GridViewKeyDown(object sender, KeyEventArgs e)
        {
            var grView = (GridView)sender;
            try
            {
                ProgramState.Idlesw.Restart();

                if (((DataTable)grView.GridControl.DataSource).Rows.Count <= 0 || grView.SelectedRowsCount <= 0)
                    return;

                if ((int)e.KeyCode == UserSettings.Shortcuts.QuickBuyKey && (int)e.Modifiers == UserSettings.Shortcuts.QuickBuyModifier)
                {
                    var row = grView.GetDataRow(grView.GetSelectedRows()[0]);
                    var d = (DataList)row["Blob"];
                    var isKeyProcessed = QuickPurchaseManager.QuickPurchase(grView, d);
                    if (isKeyProcessed)
                        return;
                }

                if ((int)e.KeyCode == UserSettings.Shortcuts.ImmediateBuyKey && (int)e.Modifiers == UserSettings.Shortcuts.ImmediateBuyModifier)
                {
                    e.Handled = true;
                    var row = grView.GetDataRow(grView.GetSelectedRows()[0]);
                    Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy, quickPurchase: true);
                    return;
                }

                if ((int)e.KeyCode == UserSettings.Shortcuts.BuyKey && (int)e.Modifiers == UserSettings.Shortcuts.BuyModifier)
                {

                    e.Handled = true;
                    var row = grView.GetDataRow(grView.GetSelectedRows()[0]);
                    if (UserSettings.Shortcuts.BuyKey == UserSettings.Shortcuts.GoToCheckoutKey &&
                        UserSettings.Shortcuts.BuyModifier == UserSettings.Shortcuts.GoToCheckoutModifier)
                    {
                        if (row == null)
                            return;
                        var d = (DataList)row["Blob"];
                        if (d.CommitToBuy == false)
                        {
                            Browser.LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetCheckoutLink());
                            return;
                        }
                    }
                    Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
                    return;
                }

                if ((int)e.KeyCode == UserSettings.Shortcuts.GoToCheckoutKey && (int)e.Modifiers == UserSettings.Shortcuts.GoToCheckoutModifier)
                {
                    e.Handled = true;
                    var row = grView.GetDataRow(grView.GetSelectedRows()[0]);
                    var d = (DataList)row["Blob"];
                    Browser.LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetCheckoutLink());
                    return;
                }

                if ((int)e.KeyCode == UserSettings.Shortcuts.MakeOfferKey && (int)e.Modifiers == UserSettings.Shortcuts.MakeOfferModifier)
                {
                    var row = grView.GetDataRow(grView.GetSelectedRows()[0]);
                    var d = (DataList)row["Blob"];
                    if (d.BestOffer)
                    {
                        e.Handled = true;
                        Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
                    }
                }

                if (e.KeyCode == Keys.Delete)
                {
                    // Check if this is the watchlist grid
                    if (grView.GridControl.Text == "Watchlist")
                    {
                        GridViewEvents.HandleWatchlistDeletionRequest((AdvBandedGridView)grView);
                    }
                    else
                    {
                        grView.DeleteSelectedRows();
                        if (grView.FocusedRowHandle >= 0)
                        {
                            grView.SelectRow(grView.FocusedRowHandle);
                        }
                    }
                    
                    e.Handled = true;
                }

                if (e.KeyCode == Keys.Down || e.KeyCode == Keys.Up)
                {
                    GridViewEvents.GridUpDownHold = true;
                }

                //AutoMeasurement.Client.TrackEvent(e.KeyData.ToString(), "Grid keypress", Analytics.GAid);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("dgv1KeyDown: ", ex);
            }
        }

        private static void GridViewKeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Down || e.KeyCode == Keys.Up)
            {
                GridViewEvents.GridUpDownHold = false;
                if (GridViewEvents.FocusedRowChanged)
                {
                    GridViewEvents.FocusedRowChanged = false;
                    GridViewEvents.UpdateDataOnRowChange((AdvBandedGridView)sender, ((AdvBandedGridView)sender).FocusedRowHandle);
                }
            }
        }

        public static void ResetGridViewLayout(AdvBandedGridView grView)
        {
            var visibleColumnsList = new[]
            {
                "Term", "Thumbnail", "Title", "Total Price", "Condition", "Returns", "Best Offer", "Commit To Buy", "Quantity", "Found Time", "Posted Time", "Sold Time", "Status",
            };

            foreach (GridColumn column in grView.Columns)
            {
                if (visibleColumnsList.Contains(column.FieldName) || ItemSpecifics.CategorySpecificsList.Count(c => c.CategoryName == column.FieldName) > 0)
                {
                    column.Visible = true;
                }
                else
                {
                    column.Visible = false;
                }
            }

            var titleColumn = grView.Columns.ColumnByFieldName("Title");
            if (titleColumn != null)
            {
                titleColumn.Width = 500;
            }

            var thumbColumn = grView.Columns.ColumnByFieldName("Thumbnail");
            if (thumbColumn != null)
            {
                thumbColumn.Width = 140;
            }

            var conditionColumn = grView.Columns.ColumnByFieldName("Condition");
            if (conditionColumn != null)
            {
                conditionColumn.Width = 125;
            }

            var returnsColumn = grView.Columns.ColumnByFieldName("Returns");
            if (returnsColumn != null)
            {
                returnsColumn.Width = 150;
            }

            var bestofferColumn = grView.Columns.ColumnByFieldName("Best Offer");
            if (bestofferColumn != null)
            {
                bestofferColumn.Width = 70;
            }

            var foundTimeColumn = grView.Columns.ColumnByFieldName("Found Time");
            if (foundTimeColumn != null)
            {
                foundTimeColumn.Width = 100;
            }

            var timeLeftColumn = grView.Columns.ColumnByFieldName("Time Left");
            if (timeLeftColumn != null)
            {
                timeLeftColumn.SortMode = ColumnSortMode.Value;
            }

            var itemIDColumn = grView.Columns.ColumnByFieldName("ItemID");
            if (itemIDColumn != null)
            {
                itemIDColumn.Width = 100;
            }

            foreach (BandedGridColumn column in grView.Columns)
            {
                column.AutoFillDown = true;
                column.OptionsColumn.AllowEdit = false;
            }

            ApplyPersistentColumnAppearance(grView);
            FormatRuleManager.ApplyGridFormatRules(grView);
        }

        public static void ApplyPersistentColumnAppearance(GridView grView)
        {
            var titleColumn = grView.Columns.ColumnByFieldName("Title");
            if (titleColumn != null)
            {
                titleColumn.AppearanceCell.Options.UseTextOptions = true;
                titleColumn.AppearanceCell.TextOptions.Trimming = Trimming.EllipsisWord;
                titleColumn.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                titleColumn.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                titleColumn.ColumnEdit = new RepositoryItemMemoEdit();

                grView.OptionsView.RowAutoHeight = true;
            }

            var termColumn = grView.Columns.ColumnByFieldName("Term");
            if (titleColumn != null)
            {
                termColumn.AppearanceCell.Options.UseTextOptions = true;
                termColumn.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                termColumn.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                termColumn.ColumnEdit = new RepositoryItemMemoEdit();
            }

            var itemIDColumn = grView.Columns.ColumnByFieldName("ItemID");
            if (itemIDColumn != null)
            {
                itemIDColumn.AppearanceCell.Font = new Font("Tahoma", 8, FontStyle.Underline, GraphicsUnit.Point, 204);
                itemIDColumn.AppearanceCell.ForeColor = Color.Blue;
            }

            var feedbackRatingColumn = grView.Columns.ColumnByFieldName("Feedback Rating");
            if (feedbackRatingColumn != null)
            {
                feedbackRatingColumn.DisplayFormat.FormatType = FormatType.Numeric;
                feedbackRatingColumn.DisplayFormat.FormatString = "0.#\\%";
            }

            var foundTimeColumn = grView.Columns.ColumnByFieldName("Found Time");
            if (foundTimeColumn != null)
            {
                grView.ClearSorting();
                foundTimeColumn.SortOrder = ColumnSortOrder.Descending;
                foundTimeColumn.AppearanceCell.Options.UseTextOptions = true;
                foundTimeColumn.AppearanceCell.TextOptions.Trimming = Trimming.Word;
                foundTimeColumn.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                foundTimeColumn.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                foundTimeColumn.ColumnEdit = new RepositoryItemMemoEdit();
            }

            var timeLeftColumn = grView.Columns.ColumnByFieldName("Time Left");
            if (timeLeftColumn != null)
            {
                timeLeftColumn.SortMode = ColumnSortMode.Value;
            }

            var postedTimeColumn = grView.Columns.ColumnByFieldName("Posted Time");
            if (postedTimeColumn != null)
            {
                postedTimeColumn.DisplayFormat.FormatType = FormatType.DateTime;
                postedTimeColumn.DisplayFormat.FormatString = "HH:mm:ss dd-MMM-yyyy";
                postedTimeColumn.AppearanceCell.Options.UseTextOptions = true;
                postedTimeColumn.AppearanceCell.TextOptions.Trimming = Trimming.Word;
                postedTimeColumn.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                postedTimeColumn.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                postedTimeColumn.ColumnEdit = new RepositoryItemMemoEdit();
            }

            if (Debugger.IsAttached)
            {
                var statusTimeColumn = grView.Columns.ColumnByFieldName("Status Time");
                if (statusTimeColumn != null)
                {
                    statusTimeColumn.DisplayFormat.FormatType = FormatType.DateTime;
                    statusTimeColumn.DisplayFormat.FormatString = "HH:mm:ss dd-MMM-yyyy";
                    statusTimeColumn.AppearanceCell.TextOptions.Trimming = Trimming.Word;
                }
            }

            var soldTimeColumn = grView.Columns.ColumnByFieldName("Sold Time");
            if (soldTimeColumn != null)
            {
                soldTimeColumn.AppearanceCell.Options.UseTextOptions = true;
                soldTimeColumn.AppearanceCell.TextOptions.Trimming = Trimming.Word;
                soldTimeColumn.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                soldTimeColumn.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                soldTimeColumn.ColumnEdit = new RepositoryItemMemoEdit();
            }

            var sellerRegistrationColumn = grView.Columns.ColumnByFieldName("Seller Registration");
            if (sellerRegistrationColumn != null)
            {
                sellerRegistrationColumn.AppearanceCell.Options.UseTextOptions = true;
                sellerRegistrationColumn.AppearanceCell.TextOptions.Trimming = Trimming.Character;
            }

            var thumbColumn = grView.Columns.ColumnByFieldName("Thumbnail");
            if (thumbColumn != null)
            {
                var icon = new RepositoryItemPictureEdit
                {
                    SizeMode = PictureSizeMode.Zoom,
                    NullText = @" "
                };

                icon.EnableLODImages = true;
                icon.PictureStoreMode = PictureStoreMode.ByteArray;
                grView.GridControl.RepositoryItems.Add(icon);
                thumbColumn.ColumnEdit = icon;
            }

            var totalPrice = grView.Columns.ColumnByFieldName("Total Price");
            if (totalPrice != null)
            {
                totalPrice.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
            }

            var itemPrice = grView.Columns.ColumnByFieldName("Item Price");
            if (itemPrice != null)
            {
                itemPrice.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
            }

            var auctionPrice = grView.Columns.ColumnByFieldName("Auction Price");
            if (auctionPrice != null)
            {
                auctionPrice.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
            }

            var conditionDescription = grView.Columns.ColumnByFieldName("Condition Description");
            if (conditionDescription != null)
            {
                conditionDescription.AppearanceCell.Options.UseTextOptions = true;
                conditionDescription.AppearanceCell.TextOptions.Trimming = Trimming.EllipsisWord;
                conditionDescription.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                conditionDescription.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                conditionDescription.ColumnEdit = new RepositoryItemMemoEdit();
            }

            foreach (BandedGridColumn column in grView.Columns)
            {
                column.AutoFillDown = true;
                column.OptionsColumn.AllowEdit = false;
            }

            if (grView.Columns.ColumnByFieldName("Description") != null)
                grView.Columns.Remove(grView.Columns.ColumnByFieldName("Description"));

            if (grView.Columns.ColumnByFieldName("Blob") != null)
                grView.Columns.Remove(grView.Columns.ColumnByFieldName("Blob"));

            if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("3C8B") || ProgramState.SerialNumber.StartsWith("AD3A"))
            {
                if (grView.Columns.ColumnByFieldName("Source") != null)
                    grView.Columns.Remove(grView.Columns.ColumnByFieldName("Source"));
            }

            grView.DataController.KeepGroupRowsExpandedOnRefresh = false;
        }

        private static void ShowGridPriceButtons(object sender, CustomRowCellEditEventArgs e)
        {
            try
            {
                var notPriceColumn = e.Column.FieldName != "Total Price" && e.Column.FieldName != "Item Price";

                var grView = (AdvBandedGridView)sender;
                var row = grView.GetDataRow(e.RowHandle);

                if (row == null)
                    return;
                if (e.Column.FieldName == "Seller Name")
                {
                    if (e.RowHandle == GridViewEvents.hoveredRowHandle)
                    {
                        e.RepositoryItem = grView.GridControl.RepositoryItems["repositoryItemTextBlockSeller"];
                    }
                    else
                    {
                        e.RepositoryItem = null;
                    }
                }

                if (notPriceColumn)
                    return;

                if (Program.Sandbox)
                {
                    if (Form1.EBayAccountsList?.Count == 0)
                        return;

                    if (e.Column.ShowButtonMode != ShowButtonModeEnum.ShowAlways)
                    {
                        e.Column.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
                    }

                    if (row["From Country"].ToString().Contains("US")
                        && row["To Country"].ToString().Contains("US")
                        && row["Condition"].ToString().Contains("New")
                        && row["Variation"].ToString() == "False")
                    {
                        e.RepositoryItem = grView.GridControl.RepositoryItems["repositoryItemTextEditCart"];
                    }
                    else
                    {
                        //e.RepositoryItem = grView.GridControl.RepositoryItems["repositoryItemTextEditBrowser"];
                    }

                    return;
                }

                var d = (DataList)row["Blob"];
                var zeroPrice = d.ItemPricing.ItemPrice.Value.Equals(0.0);

                if (d.Variation || zeroPrice)
                    return;

                if (d.CommitToBuy)
                {
                    if (Form1.EBayAccountsList?.Count == 0)
                        return;

                    if (e.Column.ShowButtonMode != ShowButtonModeEnum.ShowAlways)
                    {
                        e.Column.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
                    }

                    e.RepositoryItem = grView.GridControl.RepositoryItems["repositoryItemTextEditCart"];
                }
                else
                {
                    if (!CreditCardService.CreditCardPaymentEnabled)
                        return;

                    if (e.Column.ShowButtonMode != ShowButtonModeEnum.ShowAlways)
                    {
                        e.Column.ShowButtonMode = ShowButtonModeEnum.ShowAlways;
                    }

                    e.RepositoryItem = grView.GridControl.RepositoryItems["repositoryItemTextEditPaypal"];
                }
            }
            catch (Exception exception)
            {
                Debug.WriteLine(exception);
            }
        }

        public static List<AdvBandedGridView> GetUniqGrids(List<Keyword2Find> ebaySearchesChildrenCore)
        {
            var uniqGrids = new List<AdvBandedGridView>();
            foreach (var keyword2Find in ebaySearchesChildrenCore)
            {
                var grView = (AdvBandedGridView)keyword2Find.GridControl?.MainView;

                if (grView == null)
                    continue;

                if (!uniqGrids.Contains(grView))
                    uniqGrids.Add(grView);
            }

            return uniqGrids;
        }
    }
}
