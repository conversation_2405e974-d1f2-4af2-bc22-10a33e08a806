﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Data;
using uBuyFirst.Filters;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Action for formatting individual cells
    /// </summary>
    public class FormatCellsAction : IFilterAction
    {
        public const string IDENTIFIER = "FORMAT_CELLS";

        public string DisplayName => "Format cells";
        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.GridView != null && context.FilterRule.GridFormatRule != null)
                {
                    context.GridView.FormatRules.Add(context.FilterRule.GridFormatRule);
                    return FilterActionResult.CreateSuccess("Format cells rule applied");
                }
                return FilterActionResult.CreateFailure("Missing grid view or format rule");
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to apply format cells: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(filter.FormatColumn))
            {
                errorMessage = "Format column must be specified for cell formatting";
                return false;
            }
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for formatting entire rows
    /// </summary>
    public class FormatRowsAction : IFilterAction
    {
        public const string IDENTIFIER = "FORMAT_ROWS";

        public string DisplayName => "Format rows";
        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.GridView != null && context.FilterRule.GridFormatRule != null)
                {
                    context.GridView.FormatRules.Add(context.FilterRule.GridFormatRule);
                    return FilterActionResult.CreateSuccess("Format rows rule applied");
                }
                return FilterActionResult.CreateFailure("Missing grid view or format rule");
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to apply format rows: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            return true; // Row formatting doesn't require additional validation
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for removing rows that match criteria
    /// </summary>
    public class RemoveRowsAction : IFilterAction
    {
        public const string IDENTIFIER = "REMOVE_ROWS";

        public string DisplayName => "Remove rows";
        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.SourceDataTable == null || context.FilterRule == null)
                    return FilterActionResult.CreateFailure("Missing data table or filter rule");

                var removedCount = 0;
                var i = 0;
                while (context.SourceDataTable.Rows.Count > i)
                {
                    if (context.FilterRule.GetEvaluator().Fit(context.SourceDataTable.Rows[i]))
                    {
                        context.SourceDataTable.Rows[i].Delete();
                        removedCount++;
                    }
                    else
                    {
                        i++;
                    }
                }

                return FilterActionResult.CreateSuccess($"Removed {removedCount} rows", removedCount);
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to remove rows: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            return true; // Row removal doesn't require additional validation
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for sending notifications to Telegram
    /// </summary>
    public class SendToTelegramAction : IFilterAction
    {
        public const string IDENTIFIER = "SEND_TO_TELEGRAM";

        public string DisplayName => "Send to Telegram";
        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return FilterActionResult.CreateFailure("Missing row data or filter rule");

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    // TODO: Implement actual Telegram sending logic here
                    // For now, just return success to indicate the match
                    return FilterActionResult.CreateSuccess($"Telegram match: {context.FilterRule.Alias}");
                }

                return FilterActionResult.CreateSuccess("No match for Telegram");
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to process Telegram action: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            // TODO: Add validation for Telegram configuration if needed
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }

        /// <summary>
        /// Helper method to check if a row matches for Telegram (for backward compatibility)
        /// </summary>
        public static string IsMatchForTelegram(DataRow row, IEnumerable<XFilterClass> filters)
        {
            var telegramFilters = filters
                .Where(f => f.Enabled && f.ActionHandler is SendToTelegramAction)
                .ToArray();

            if (!telegramFilters.Any())
                return "No telegram filters";

            foreach (var filter in telegramFilters)
            {
                try
                {
                    if (filter.GetEvaluator().Fit(row))
                        return filter.Alias;
                }
                catch (Exception)
                {
                    // Ignore evaluation errors
                }
            }

            return "";
        }
    }

    /// <summary>
    /// Action for buying items with a specific eBay account
    /// </summary>
    public class BuyWithAccountAction : IFilterAction
    {
        public const string IDENTIFIER = "BUY_WITH_ACCOUNT";

        public string AccountUsername { get; set; }

        public string DisplayName => string.IsNullOrEmpty(AccountUsername)
            ? "Buy with account"
            : $"Buy with {AccountUsername}";

        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.SourceDataTable == null || context.FilterRule == null)
                    return FilterActionResult.CreateFailure("Missing data table or filter rule");

                if (string.IsNullOrEmpty(AccountUsername))
                    return FilterActionResult.CreateFailure("No account username specified");

                var eBayAccount = Form1.EBayAccountsList?.FirstOrDefault(account =>
                    account.UserName.Equals(AccountUsername, StringComparison.OrdinalIgnoreCase));

                if (eBayAccount == null)
                    return FilterActionResult.CreateFailure($"eBay account '{AccountUsername}' not found");

                var updatedCount = 0;
                var i = 0;
                while (context.SourceDataTable.Rows.Count > i)
                {
                    var row = context.SourceDataTable.Rows[i];
                    if (context.FilterRule.GetEvaluator().Fit(row))
                    {
                        var d = (DataList)row["Blob"];
                        d.EbayAccount = eBayAccount;
                        row["Blob"] = d;
                        updatedCount++;
                    }
                    i++;
                }

                return FilterActionResult.CreateSuccess($"Updated {updatedCount} items with account {AccountUsername}", updatedCount);
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to apply buy with account: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(AccountUsername))
            {
                errorMessage = "Account username must be specified";
                return false;
            }
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(AccountUsername))
            {
                data["AccountUsername"] = AccountUsername;
            }
            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data.TryGetValue("AccountUsername", out var username))
            {
                AccountUsername = username?.ToString();
            }
        }

        // Helper method to load data from filter
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter.ActionData.TryGetValue("AccountUsername", out var username))
            {
                AccountUsername = username?.ToString();
            }
        }

        // Helper method to save data to filter
        public void SaveToFilter(XFilterClass filter)
        {
            if (!string.IsNullOrEmpty(AccountUsername))
            {
                filter.ActionData["AccountUsername"] = AccountUsername;
            }
        }
    }

    /// <summary>
    /// Example of a new extensible action - Send to Webhook
    /// This demonstrates how easy it is to add new actions
    /// </summary>
    public class SendToWebhookAction : IFilterAction
    {
        public const string IDENTIFIER = "SEND_TO_WEBHOOK";

        public string WebhookUrl { get; set; }

        public string DisplayName => "Send to Webhook";
        public string ActionTypeIdentifier => IDENTIFIER;



        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return FilterActionResult.CreateFailure("Missing row data or filter rule");

                if (string.IsNullOrEmpty(WebhookUrl))
                    return FilterActionResult.CreateFailure("Webhook URL not configured");

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    // TODO: Implement actual webhook sending logic here
                    // For now, just return success to indicate the match
                    return FilterActionResult.CreateSuccess($"Would send to webhook: {WebhookUrl}");
                }

                return FilterActionResult.CreateSuccess("No match for webhook");
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to process webhook action: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(WebhookUrl))
            {
                errorMessage = "Webhook URL must be configured";
                return false;
            }

            // TODO: Add URL validation
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(WebhookUrl))
            {
                data["WebhookUrl"] = WebhookUrl;
            }
            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data.TryGetValue("WebhookUrl", out var url))
            {
                WebhookUrl = url?.ToString();
            }
        }

        // Helper method to load data from filter
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter.ActionData.TryGetValue("WebhookUrl", out var url))
            {
                WebhookUrl = url?.ToString();
            }
        }

        // Helper method to save data to filter
        public void SaveToFilter(XFilterClass filter)
        {
            if (!string.IsNullOrEmpty(WebhookUrl))
            {
                filter.ActionData["WebhookUrl"] = WebhookUrl;
            }
        }
    }
}
