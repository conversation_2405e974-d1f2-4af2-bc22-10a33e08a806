<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Lucene.Net.QueryParser</name>
    </assembly>
    <members>
        <member name="T:Lucene.Net.QueryParsers.Analyzing.AnalyzingQueryParser">
            <summary>
            Overrides Lucene's default <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> so that Fuzzy-, Prefix-, Range-, and WildcardQuerys
            are also passed through the given analyzer, but wildcard characters <c>*</c> and
            <c>?</c> don't get removed from the search terms.
            <para/>
            <b>Warning:</b> This class should only be used with analyzers that do not use stopwords
            or that add tokens. Also, several stemming analyzers are inappropriate: for example, <see cref="T:Lucene.Net.Analysis.De.GermanAnalyzer"/>  
            will turn <c>Häuser</c> into <c>hau</c>, but <c>H?user</c> will 
            become <c>h?user</c> when using this parser and thus no match would be found (i.e.
            using this parser will be no improvement over Query<PERSON>ars<PERSON> in such cases). 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Analyzing.AnalyzingQueryParser.GetWildcardQuery(System.String,System.String)">
            <summary>
            Called when parser parses an input term
            that uses prefix notation; that is, contains a single '*' wildcard
            character as its last character. Since this is a special case
            of generic wildcard term, and such a query can be optimized easily,
            this usually results in a different query object.
            <para/>
            Depending on analyzer and settings, a prefix term may (most probably will)
            be lower-cased automatically. It <b>will</b> go through the default Analyzer.
            <para/>
            Overrides super class, by passing terms through analyzer.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term to use for building term for the query
            (<b>without</b> trailing '*' character!)</param>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Analyzing.AnalyzingQueryParser.GetPrefixQuery(System.String,System.String)">
            <summary>
            Called when parser parses an input term
            that uses prefix notation; that is, contains a single '*' wildcard
            character as its last character. Since this is a special case
            of generic wildcard term, and such a query can be optimized easily,
            this usually results in a different query object.
            <para/>
            Depending on analyzer and settings, a prefix term may (most probably will)
            be lower-cased automatically. It <b>will</b> go through the default Analyzer.
            <para/>
            Overrides super class, by passing terms through analyzer.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term to use for building term for the query (<b>without</b> trailing '*' character!)</param>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Analyzing.AnalyzingQueryParser.GetFuzzyQuery(System.String,System.String,System.Single)">
            <summary>
            Called when parser parses an input term that has the fuzzy suffix (~) appended.
            <para/>
            Depending on analyzer and settings, a fuzzy term may (most probably will)
            be lower-cased automatically. It <b>will</b> go through the default Analyzer.
            <para/>
            Overrides super class, by passing terms through analyzer.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term to use for building term for the query</param>
            <param name="minSimilarity"></param>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Analyzing.AnalyzingQueryParser.AnalyzeSingleChunk(System.String,System.String,System.String)">
            <summary>
            Returns the analyzed form for the given chunk.
            
            If the analyzer produces more than one output token from the given chunk,
            a ParseException is thrown.
            </summary>
            <param name="field">The target field</param>
            <param name="termStr">The full term from which the given chunk is excerpted</param>
            <param name="chunk">The portion of the given termStr to be analyzed</param>
            <returns>The result of analyzing the given chunk</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">ParseException when analysis returns other than one output token</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.ICharStream">
            <summary> 
            This interface describes a character stream that maintains line and
            column number positions of the characters.  It also has the capability
            to backup the stream to some extent.  An implementation of this
            interface is used in the TokenManager implementation generated by
            JavaCCParser.
            
            All the methods except <see cref="M:Lucene.Net.QueryParsers.Classic.ICharStream.BackUp(System.Int32)"/> can be implemented in any fashion. <see cref="M:Lucene.Net.QueryParsers.Classic.ICharStream.BackUp(System.Int32)"/>
            needs to be implemented correctly for the correct operation of the lexer.
            Rest of the methods are all used to get information like line number,
            column number and the string that constitutes a token and are not used
            by the lexer. Hence their implementation won't affect the generated lexer's
            operation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ICharStream.ReadChar">
            <summary> 
            Returns the next character from the selected input.  The method
            of selecting the input is the responsibility of the class
            implementing this interface.  Can throw any <see cref="T:System.IO.IOException"/>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.Column">
            <summary>
            Returns the column position of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Classic.ICharStream.EndColumn"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.Line">
            <summary>
            Returns the line number of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Classic.ICharStream.EndLine"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.EndColumn">
            <summary>
            Returns the column number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.EndLine">
            <summary> 
            Returns the line number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.BeginColumn">
            <summary> 
            Returns the column number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.BeginLine">
            <summary> 
            Returns the line number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ICharStream.BackUp(System.Int32)">
            <summary> 
            Backs up the input stream by amount steps. Lexer calls this method if it
            had already read some characters, but could not use them to match a
            (longer) token. So, they will be used again as the prefix of the next
            token and it is the implemetation's responsibility to do this right.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ICharStream.BeginToken">
            <summary> 
            Returns the next character that marks the beginning of the next token.
            All characters must remain in the buffer between two successive calls
            to this method to implement <see cref="M:Lucene.Net.QueryParsers.Classic.ICharStream.BackUp(System.Int32)"/> correctly.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ICharStream.Image">
            <summary> 
            Returns a string made up of characters from the marked token beginning
            to the current buffer position. Implementations have the choice of returning
            anything that they want to. For example, for efficiency, one might decide
            to just return null, which is a valid implementation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ICharStream.GetSuffix(System.Int32)">
            <summary> 
            Returns an array of characters that make up the suffix of length 'len' for
            the currently matched token. This is used to build up the matched string
            for use in actions in the case of MORE. A simple and inefficient
            implementation of this is as follows :
            
            <code>
            {
                string t = Image;
                return t.Substring(t.Length - len, len).ToCharArray();
            }
            </code>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ICharStream.Done">
            <summary> 
            The lexer calls this function to indicate that it is done with the stream
            and hence implementations can free any resources held by this class.
            Again, the body of this function can be just empty and it will not
            affect the lexer's operation.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.FastCharStream">
            <summary>
            An efficient implementation of JavaCC's <see cref="T:Lucene.Net.QueryParsers.Classic.ICharStream"/> interface.  
            <para/>
            Note that
            this does not do line-number counting, but instead keeps track of the
            character position of the token in the input, as required by Lucene's <see cref="T:Lucene.Net.Analysis.Token" />
            API.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.FastCharStream.#ctor(System.IO.TextReader)">
            <summary>
            Constructs from a <see cref="T:System.IO.TextReader"/>. 
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser">
            <summary> 
            A <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> which constructs queries to search multiple fields.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser.#ctor(Lucene.Net.Util.LuceneVersion,System.String[],Lucene.Net.Analysis.Analyzer,System.Collections.Generic.IDictionary{System.String,System.Single})">
            <summary> 
            Creates a <see cref="T:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser"/>. Allows passing of a map with term to
            Boost, and the boost to apply to each term.
            
            <para/>
            It will, when <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Parse(System.String)"/> is called, construct a query like this
            (assuming the query consists of two terms and you specify the two fields
            <c>title</c> and <c>body</c>):
            <para/>
            
            <code>
            (title:term1 body:term1) (title:term2 body:term2)
            </code>
            
            <para/>
            When <see cref="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.DefaultOperator"/> is set to <see cref="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.AND_OPERATOR"/>, the result will be:
            <para/>
            
            <code>
            +(title:term1 body:term1) +(title:term2 body:term2)
            </code>
            
            <para/>
            When you pass a boost (title=>5 body=>10) you can get
            <para/>
            
            <code>
            +(title:term1^5.0 body:term1^10.0) +(title:term2^5.0 body:term2^10.0)
            </code>
            
            <para/>
            In other words, all the query's terms must appear, but it doesn't matter
            in what fields they appear.
            <para/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser.#ctor(Lucene.Net.Util.LuceneVersion,System.String[],Lucene.Net.Analysis.Analyzer)">
            <summary> 
            Creates a MultiFieldQueryParser.
            
            <para/>
            It will, when <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Parse(System.String)"/> is called, construct a query like this
            (assuming the query consists of two terms and you specify the two fields
            <c>title</c> and <c>body</c>):
            <para/>
            
            <code>
            (title:term1 body:term1) (title:term2 body:term2)
            </code>
            
            <para/>
            When <see cref="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.DefaultOperator"/> is set to <see cref="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.AND_OPERATOR"/>, the result will be:
            <para/>
            
            <code>
            +(title:term1 body:term1) +(title:term2 body:term2)
            </code>
            
            <para/>
            In other words, all the query's terms must appear, but it doesn't matter
            in what fields they appear.
            <para/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser.Parse(Lucene.Net.Util.LuceneVersion,System.String[],System.String[],Lucene.Net.Analysis.Analyzer)">
            <summary> 
            Parses a query which searches on the fields specified.
            <para/>
            If x fields are specified, this effectively constructs:
            
            <code>
            (field1:query1) (field2:query2) (field3:query3)...(fieldx:queryx)
            </code>
            
            </summary>
            <param name="matchVersion">Lucene version to match; this is passed through to
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>.</param>
            <param name="queries">Queries strings to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">if query parsing fails</exception>
            <exception cref="T:System.ArgumentException">
            if the length of the queries array differs from the length of
            the fields array
            </exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser.Parse(Lucene.Net.Util.LuceneVersion,System.String,System.String[],Lucene.Net.Search.Occur[],Lucene.Net.Analysis.Analyzer)">
            <summary> 
            Parses a query, searching on the fields specified. Use this if you need
            to specify certain fields as required, and others as prohibited.
            <para/>
            Usage:
            <code>
                string[] fields = {&quot;filename&quot;, &quot;contents&quot;, &quot;description&quot;};
                Occur[] flags = {Occur.SHOULD,
                    Occur.MUST,
                    Occur.MUST_NOT};
                MultiFieldQueryParser.Parse(&quot;query&quot;, fields, flags, analyzer);
            </code>
            <para/>
            The code above would construct a query:
            
            <code>
            (filename:query) +(contents:query) -(description:query)
            </code>
            
            </summary>
            <param name="matchVersion">Lucene version to match; this is passed through to
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>.</param>
            <param name="query">Query string to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="flags">Flags describing the fields</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">if query parsing fails</exception>
            <exception cref="T:System.ArgumentException">
            if the length of the fields array differs from the length of
            the flags array
            </exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.MultiFieldQueryParser.Parse(Lucene.Net.Util.LuceneVersion,System.String[],System.String[],Lucene.Net.Search.Occur[],Lucene.Net.Analysis.Analyzer)">
            <summary> 
            Parses a query, searching on the fields specified. Use this if you need
            to specify certain fields as required, and others as prohibited.
            <para/>
            Usage:
            <code>
                string[] query = {&quot;query1&quot;, &quot;query2&quot;, &quot;query3&quot;};
                string[] fields = {&quot;filename&quot;, &quot;contents&quot;, &quot;description&quot;};
                Occur[] flags = {Occur.SHOULD,
                    Occur.MUST,
                    Occur.MUST_NOT};
                MultiFieldQueryParser.Parse(query, fields, flags, analyzer);
            </code>
            <para/>
            The code above would construct a query:
            
            <code>
            (filename:query1) +(contents:query2) -(description:query3)
            </code>
            
            </summary>
            <param name="matchVersion">Lucene version to match; this is passed through to
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>.</param>
            <param name="queries">Queries string to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="flags">Flags describing the fields</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">if query parsing fails</exception>
            <exception cref="T:System.ArgumentException">if the length of the queries, fields, and flags array differ</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.ParseException">
            <summary> 
            This exception is thrown when parse errors are encountered.
            You can explicitly create objects of this exception type by
            calling the method GenerateParseException in the generated
            parser.
            
            You can modify this class to customize your error reporting
            mechanisms so long as you retain the public fields.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ParseException.#ctor(Lucene.Net.QueryParsers.Classic.Token,System.Int32[][],System.String[])">
            <summary>
            This constructor is used by the method GenerateParseException()
            in the generated parser.  Calling this constructor generates
            a new object of this type with the fields <paramref name="currentToken"/>,
            <paramref name="expectedTokenSequences"/>, and <paramref name="tokenImage"/> set.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ParseException.#ctor">
            The following constructors are for use by you for whatever
            purpose you can think of.  Constructing the exception in this
            manner makes the exception behave in the normal way - i.e., as
            documented in the class "Throwable".  The fields "errorToken",
            "expectedTokenSequences", and "tokenImage" do not contain
            relevant information.  The JavaCC generated code does not use
            these constructors.
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ParseException.CurrentToken">
            <summary> 
            This is the last token that has been consumed successfully.  If
            this object has been created due to a parse error, the token
            following this token will (therefore) be the first error token.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ParseException.ExpectedTokenSequences">
            <summary> 
            Each entry in this array is an array of integers.  Each array
            of integers represents a sequence of tokens (by their ordinal
            values) that is expected at this point of the parse.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.ParseException.TokenImage">
            <summary> 
            This is a reference to the "tokenImage" array of the generated
            parser within which the parse error occurred.  This array is
            defined in the generated ...Constants interface.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ParseException.Initialize(Lucene.Net.QueryParsers.Classic.Token,System.Int32[][],System.String[])">
            <summary>
            It uses <paramref name="currentToken"/> and <paramref name="expectedTokenSequences"/> to generate a parse
            error message and returns it.  If this object has been created
            due to a parse error, and you do not catch it (it gets thrown
            from the parser) the correct error message
            gets displayed.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.ParseException.eol">
            <summary> 
            The end of line string for this machine.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.ParseException.AddEscapes(System.String)">
            <summary> 
            Used to convert raw characters to their escaped version
            when these raw version cannot be used as part of an ASCII
            string literal.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.QueryParser">
            <summary> This class is generated by JavaCC.  The most important method is
            <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Parse(System.String)" />.
            <para/>
            The syntax for query strings is as follows:
            A Query is a series of clauses.
            A clause may be prefixed by:
            <list type="bullet">
            <item><description> a plus (<c>+</c>) or a minus (<c>-</c>) sign, indicating
            that the clause is required or prohibited respectively; or</description></item>
            <item><description> a term followed by a colon, indicating the field to be searched.
            This enables one to construct queries which search multiple fields.</description></item>
            </list>
            
            <para/>
            A clause may be either:
            <list type="bullet">
            <item><description> a term, indicating all the documents that contain this term; or</description></item>
            <item><description> a nested query, enclosed in parentheses.  Note that this may be used
            with a <c>+</c>/<c>-</c> prefix to require any of a set of
            terms.</description></item>
            </list>
            
            <para/>
            Thus, in BNF, the query grammar is:
            <code>
                Query  ::= ( Clause )*
                Clause ::= ["+", "-"] [&lt;TERM&gt; ":"] ( &lt;TERM&gt; | "(" Query ")" )
            </code>
            
            <para>
            Examples of appropriately formatted queries can be found in the <a
            href="../../../../../../queryparsersyntax.html">query syntax
            documentation</a>.
            </para>
            
            <para>
            In <see cref="T:Lucene.Net.Search.TermRangeQuery" />s, QueryParser tries to detect date values, e.g.
            <tt>date:[6/1/2005 TO 6/4/2005]</tt> produces a range query that searches
            for "date" fields between 2005-06-01 and 2005-06-04. Note that the format
            of the accepted input depends on the <see cref="T:System.Globalization.CultureInfo" />.
            A <see cref="T:Lucene.Net.Documents.DateTools.Resolution" /> has to be set,
            if you want to use <see cref="T:Lucene.Net.Documents.DateTools"/> for date conversion.<p/>
            </para>
            <para>
            The date resolution that shall be used for RangeQueries can be set
            using <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.SetDateResolution(Lucene.Net.Documents.DateTools.Resolution)" />
            or <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.SetDateResolution(System.String,Lucene.Net.Documents.DateTools.Resolution)" />. The former
            sets the default date resolution for all fields, whereas the latter can
            be used to set field specific date resolutions. Field specific date
            resolutions take, if set, precedence over the default date resolution.
            </para>
            <para>
            If you don't use <see cref="T:Lucene.Net.Documents.DateTools" /> in your index, you can create your own
            query parser that inherits <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> and overwrites
            <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetRangeQuery(System.String,System.String,System.String,System.Boolean,System.Boolean)" /> to
            use a different method for date conversion.
            </para>
            
            <para>Note that <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> is <em>not</em> thread-safe.</para>
            
            <para><b>NOTE</b>: there is a new QueryParser in contrib, which matches
            the same syntax as this class, but is more modular,
            enabling substantial customization to how a query is created.
            </para>
            
            <b>NOTE</b>: You must specify the required <see cref="T:Lucene.Net.Util.LuceneVersion" /> compatibility when
            creating QueryParser:
            <list type="bullet">
            <item><description>As of 3.1, <see cref="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.AutoGeneratePhraseQueries"/> is false by default.</description></item>
            </list>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.#ctor(Lucene.Net.Util.LuceneVersion,System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Constructs a query parser.
            </summary>
            <param name="matchVersion">Lucene version to match.</param>
            <param name="f">the default field for query terms.</param>
            <param name="a">used to find terms in the query text.</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParser.TokenSource">
            <summary>Generated Token Manager.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParser.Token">
            <summary>Current token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParser.Jj_nt">
            <summary>Next token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.#ctor(Lucene.Net.QueryParsers.Classic.ICharStream)">
            <summary>Constructor with user supplied <see cref="T:Lucene.Net.QueryParsers.Classic.ICharStream"/>. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.ReInit(Lucene.Net.QueryParsers.Classic.ICharStream)">
            <summary>Reinitialize. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.#ctor(Lucene.Net.QueryParsers.Classic.QueryParserTokenManager)">
            <summary>Constructor with generated Token Manager. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.ReInit(Lucene.Net.QueryParsers.Classic.QueryParserTokenManager)">
            <summary>Reinitialize. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.LookaheadSuccess.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.GetNextToken">
            <summary>Get the next Token. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.GetToken(System.Int32)">
            <summary>Get the specific Token. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.GenerateParseException">
            <summary>Generate ParseException. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.Enable_tracing">
            <summary>Enable tracing. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParser.Disable_tracing">
            <summary>Disable tracing. </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.Operator">
            <summary>
            The default operator for parsing queries. 
            Use <see cref="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.DefaultOperator"/> to change it.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.QueryParserBase">
            <summary>
            This class is overridden by <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.QueryParserBase.MethodRemovedUseAnother">
            <summary>
            Do not catch this exception in your code, it means you are using methods that you should no longer use.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.MethodRemovedUseAnother.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.AND_OPERATOR">
            <summary>
            Alternative form of <see cref="F:Lucene.Net.QueryParsers.Classic.Operator.AND"/> 
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.OR_OPERATOR">
            <summary>
            Alternative form of <see cref="F:Lucene.Net.QueryParsers.Classic.Operator.OR"/> 
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.dateResolution">
            <summary>
            the default date resolution
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.fieldToDateResolution">
            <summary>
             maps field names to date resolutions
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.analyzeRangeTerms">
            <summary>
            Whether or not to analyze range terms when constructing RangeQuerys
            (For example, analyzing terms into collation keys for locale-sensitive RangeQuery)
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.#ctor">
            <summary>
            So the generated QueryParser(CharStream) won't error out
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Init(Lucene.Net.Util.LuceneVersion,System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Initializes a query parser.  Called by the QueryParser constructor
            </summary>
            <param name="matchVersion">Lucene version to match.</param>
            <param name="f">the default field for query terms.</param>
            <param name="a">used to find terms in the query text.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Parse(System.String)">
            <summary>
            Parses a query string, returning a <see cref="T:Lucene.Net.Search.Query"/>.
            </summary>
            <param name="query">the query string to be parsed.</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">if the parsing fails</exception>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.Field">
            <summary>
            Returns the default field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.AutoGeneratePhraseQueries">
            <summary>
            Set to true if phrase queries will be automatically generated
            when the analyzer returns more than one term from whitespace
            delimited text.
            NOTE: this behavior may not be suitable for all languages.
            <para/>
            Set to false if phrase queries should only be generated when
            surrounded by double quotes.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.FuzzyMinSim">
            <summary>
            Get or Set the minimum similarity for fuzzy queries.
            Default is 2f.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.FuzzyPrefixLength">
            <summary>
            Get or Set the prefix length for fuzzy queries. 
            Default is 0.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.PhraseSlop">
            <summary>
            Gets or Sets the default slop for phrases. 
            If zero, then exact phrase matches are required. 
            Default value is zero.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.AllowLeadingWildcard">
            <summary>
            Set to <c>true</c> to allow leading wildcard characters.
            <para/>
            When set, <c>*</c> or <c>?</c> are allowed as
            the first character of a PrefixQuery and WildcardQuery.
            Note that this can produce very slow
            queries on big indexes.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.DefaultOperator">
            <summary>
            Gets or Sets the boolean operator of the QueryParser.
            In default mode (<see cref="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.OR_OPERATOR"/>) terms without any modifiers
            are considered optional: for example <c>capital of Hungary</c> is equal to
            <c>capital OR of OR Hungary</c>.
            <para/>
            In <see cref="F:Lucene.Net.QueryParsers.Classic.QueryParserBase.AND_OPERATOR"/> mode terms are considered to be in conjunction: the
            above mentioned query is parsed as <c>capital AND of AND Hungary</c>
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.LowercaseExpandedTerms">
            <summary>
            Whether terms of wildcard, prefix, fuzzy and range queries are to be automatically
            lower-cased or not.  Default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.MultiTermRewriteMethod">
            <summary>
            By default QueryParser uses <see cref="F:Lucene.Net.Search.MultiTermQuery.CONSTANT_SCORE_AUTO_REWRITE_DEFAULT"/>
            when creating a <see cref="T:Lucene.Net.Search.PrefixQuery"/>, <see cref="T:Lucene.Net.Search.WildcardQuery"/> or <see cref="T:Lucene.Net.Search.TermRangeQuery"/>. This implementation is generally preferable because it
            a) Runs faster b) Does not have the scarcity of terms unduly influence score
            c) avoids any <see cref="T:Lucene.Net.Search.BooleanQuery.TooManyClausesException"/> exception.
            However, if your application really needs to use the
            old-fashioned <see cref="T:Lucene.Net.Search.BooleanQuery"/> expansion rewriting and the above
            points are not relevant then use this to change
            the rewrite method.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.Locale">
            <summary>
            Get or Set locale used by date range parsing, lowercasing, and other
            locale-sensitive operations.
            <para/>
            By default, the culture is <c>null</c>, which indicates to read the culture on the fly 
            from <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/>. This ensures if you change the culture on
            the current thread, QueryParser will utilize it. You can also explicitly set a culture.
            Setting the culture to <c>null</c> will restore the default behavior if you have explicitly set a culture.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.TimeZone">
            <summary>
            Get or Set the current time zone for date and time parsing operations.
            <para/>
            By default, the time zone is <c>null</c>, which indicates to read the time zone on the fly 
            from <see cref="P:System.TimeZoneInfo.Local"/>. This ensures if you change the time zone on
            the current system, QueryParser will utilize it. You can also explicitly set a time zone.
            Setting the time zone to <c>null</c> will restore the default behavior if you have explicitly set a time zone.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.SetDateResolution(Lucene.Net.Documents.DateTools.Resolution)">
            <summary>
            Gets or Sets the default date resolution used by RangeQueries for fields for which no
            specific date resolutions has been set. Field specific resolutions can be set
            with <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.SetDateResolution(System.String,Lucene.Net.Documents.DateTools.Resolution)"/>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.SetDateResolution(System.String,Lucene.Net.Documents.DateTools.Resolution)">
            <summary>
            Sets the date resolution used by RangeQueries for a specific field.
            </summary>
            <param name="fieldName">field for which the date resolution is to be set</param>
            <param name="dateResolution">date resolution to set</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetDateResolution(System.String)">
            <summary>
            Returns the date resolution that is used by RangeQueries for the given field.
            Returns null, if no default or field specific date resolution has been set 
            for the given field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.QueryParserBase.AnalyzeRangeTerms">
            <summary>
            Get or Set whether or not to analyze range terms when constructing <see cref="T:Lucene.Net.Search.TermRangeQuery"/>s.
            For example, setting this to true can enable analyzing terms into 
            collation keys for locale-sensitive <see cref="T:Lucene.Net.Search.TermRangeQuery"/>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetFieldQuery(System.String,System.String,System.Boolean)">
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewFieldQuery(Lucene.Net.Analysis.Analyzer,System.String,System.String,System.Boolean)">
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetFieldQuery(System.String,System.String,System.Int32)">
            <summary>
            Base implementation delegates to <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetFieldQuery(System.String,System.String,System.Boolean)"/>.
            This method may be overridden, for example, to return
            a <see cref="T:Lucene.Net.Search.Spans.SpanNearQuery"/> instead of a <see cref="T:Lucene.Net.Search.PhraseQuery"/>.
            </summary>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewBooleanClause(Lucene.Net.Search.Query,Lucene.Net.Search.Occur)">
            <summary>Builds a new <see cref="T:Lucene.Net.Search.BooleanClause"/> instance</summary>
            <param name="q">sub query</param>
            <param name="occur">how this clause should occur when matching documents</param>
            <returns> new <see cref="T:Lucene.Net.Search.BooleanClause"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewPrefixQuery(Lucene.Net.Index.Term)">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.PrefixQuery"/> instance
            </summary>
            <param name="prefix">Prefix term</param>
            <returns>new <see cref="T:Lucene.Net.Search.PrefixQuery"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewRegexpQuery(Lucene.Net.Index.Term)">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.RegexpQuery"/> instance
            </summary>
            <param name="regexp">Regexp term</param>
            <returns>new <see cref="T:Lucene.Net.Search.RegexpQuery"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewFuzzyQuery(Lucene.Net.Index.Term,System.Single,System.Int32)">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.FuzzyQuery"/> instance
            </summary>
            <param name="term">Term</param>
            <param name="minimumSimilarity">minimum similarity</param>
            <param name="prefixLength">prefix length</param>
            <returns>new <see cref="T:Lucene.Net.Search.FuzzyQuery"/> Instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewRangeQuery(System.String,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.TermRangeQuery"/> instance
            </summary>
            <param name="field">Field</param>
            <param name="part1">min</param>
            <param name="part2">max</param>
            <param name="startInclusive">true if the start of the range is inclusive</param>
            <param name="endInclusive">true if the end of the range is inclusive</param>
            <returns>new <see cref="T:Lucene.Net.Search.TermRangeQuery"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewMatchAllDocsQuery">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.MatchAllDocsQuery"/> instance
            </summary>
            <returns>new <see cref="T:Lucene.Net.Search.MatchAllDocsQuery"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.NewWildcardQuery(Lucene.Net.Index.Term)">
            <summary>
            Builds a new <see cref="T:Lucene.Net.Search.WildcardQuery"/> instance
            </summary>
            <param name="t">wildcard term</param>
            <returns>new <see cref="T:Lucene.Net.Search.WildcardQuery"/> instance</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetBooleanQuery(System.Collections.Generic.IList{Lucene.Net.Search.BooleanClause})">
            <summary>
            Factory method for generating query, given a set of clauses.
            By default creates a boolean query composed of clauses passed in.
            <para/>
            Can be overridden by extending classes, to modify query being
            returned.
            </summary>
            <param name="clauses">List that contains <see cref="T:Lucene.Net.Search.BooleanClause"/> instances 
            to join.</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> object.</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetBooleanQuery(System.Collections.Generic.IList{Lucene.Net.Search.BooleanClause},System.Boolean)">
            <summary>
            Factory method for generating query, given a set of clauses.
            By default creates a boolean query composed of clauses passed in.
            <para/>
            Can be overridden by extending classes, to modify query being
            returned.
            </summary>
            <param name="clauses">List that contains <see cref="T:Lucene.Net.Search.BooleanClause"/> instances
            to join.</param>
            <param name="disableCoord">true if coord scoring should be disabled.</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> object.</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetWildcardQuery(System.String,System.String)">
            <summary>
            Factory method for generating a query. Called when parser
            parses an input term token that contains one or more wildcard
            characters (? and *), but is not a prefix term token (one
            that has just a single * character at the end)
            <para/>
            Depending on settings, prefix term may be lower-cased
            automatically. It will not go through the default Analyzer,
            however, since normal Analyzers are unlikely to work properly
            with wildcard templates.
            <para/>
            Can be overridden by extending classes, to provide custom handling for
            wildcard queries, which may be necessary due to missing analyzer calls.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term token that contains one or more wild card
            characters (? or *), but is not simple prefix term</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetRegexpQuery(System.String,System.String)">
            <summary>
            Factory method for generating a query. Called when parser
            parses an input term token that contains a regular expression
            query.
            <para/>
            Depending on settings, pattern term may be lower-cased
            automatically. It will not go through the default Analyzer,
            however, since normal Analyzers are unlikely to work properly
            with regular expression templates.
            <para/>
            Can be overridden by extending classes, to provide custom handling for
            regular expression queries, which may be necessary due to missing analyzer
            calls.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term token that contains a regular expression</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetPrefixQuery(System.String,System.String)">
            <summary>
            Factory method for generating a query (similar to
            <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetWildcardQuery(System.String,System.String)"/>). Called when parser parses an input term
            token that uses prefix notation; that is, contains a single '*' wildcard
            character as its last character. Since this is a special case
            of generic wildcard term, and such a query can be optimized easily,
            this usually results in a different query object.
            <para/>
            Depending on settings, a prefix term may be lower-cased
            automatically. It will not go through the default Analyzer,
            however, since normal Analyzers are unlikely to work properly
            with wildcard templates.
            <para/>
            Can be overridden by extending classes, to provide custom handling for
            wild card queries, which may be necessary due to missing analyzer calls.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term token to use for building term for the query</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetFuzzyQuery(System.String,System.String,System.Single)">
            <summary>
            Factory method for generating a query (similar to
            <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.GetWildcardQuery(System.String,System.String)"/>). Called when parser parses
            an input term token that has the fuzzy suffix (~) appended.
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="termStr">Term token to use for building term for the query</param>
            <param name="minSimilarity">minimum similarity</param>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">throw in overridden method to disallow</exception>
            <returns>Resulting <see cref="T:Lucene.Net.Search.Query"/> built for the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.DiscardEscapeChar(System.String)">
            <summary>
            Returns a string where the escape char has been
            removed, or kept only once if there was a double escape.
            <para/>
            Supports escaped unicode characters, e. g. translates 
            <c>\\u0041</c> to <c>A</c>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.HexToInt32(System.Char)">
            <summary>
            Returns the numeric value of the hexadecimal character
            <para/>
            NOTE: This was hexToInt() in Lucene
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Escape(System.String)">
            <summary>
            Returns a string where those characters that QueryParser
            expects to be escaped are escaped by a preceding <code>\</code>.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.EOF">
            <summary>End of File. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.NUM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.ESCAPED_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.TERM_START_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.TERM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.WHITESPACE">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.QUOTED_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.AND">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.OR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.NOT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.PLUS">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.MINUS">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.BAREOPER">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.LPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.COLON">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.STAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.CARAT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.QUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.TERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.FUZZY_SLOP">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.PREFIXTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.WILDTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.REGEXPTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGEIN_START">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGEEX_START">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.NUMBER">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGE_TO">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGEIN_END">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGEEX_END">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGE_QUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.RegexpToken.RANGE_GOOP">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.LexicalToken.Boost">
            <summary>Lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.LexicalToken.Range">
            <summary>Lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.LexicalToken.DEFAULT">
            <summary>Lexical state.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.QueryParserConstants">
            <summary> Token literal values and constants.
            Generated by org.javacc.parser.OtherFilesGen#start()
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserConstants.TokenImage">
            <summary>Literal token values. </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager">
            <summary>Token Manager. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.debugStream">
            <summary>Debug output. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.SetDebugStream(System.IO.TextWriter)">
            <summary>Set debug output. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.jjstrLiteralImages">
            <summary>Token literal values. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.lexStateNames">
            <summary>Lexer state names. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.jjnewLexState">
            <summary>Lex State array. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.#ctor(Lucene.Net.QueryParsers.Classic.ICharStream)">
            <summary>Constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.#ctor(Lucene.Net.QueryParsers.Classic.ICharStream,System.Int32)">
            <summary>Constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.ReInit(Lucene.Net.QueryParsers.Classic.ICharStream)">
            <summary>Reinitialise parser. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.ReInit(Lucene.Net.QueryParsers.Classic.ICharStream,System.Int32)">
            <summary>Reinitialise parser. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.SwitchTo(System.Int32)">
            <summary>Switch to specified lex state. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.QueryParserTokenManager.GetNextToken">
            <summary>Get the next Token. </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.Token">
            <summary> 
            Describes the input token stream.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.Kind">
            <summary> 
            An integer that describes the kind of this token.  This numbering
            system is determined by JavaCCParser, and a table of these numbers is
            stored in the file ...Constants.java.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.BeginLine">
            <summary>The line number of the first character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.BeginColumn">
            <summary>The column number of the first character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.EndLine">
            <summary>The line number of the last character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.EndColumn">
            <summary>The column number of the last character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.Image">
            <summary>The string image of the token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.Next">
            <summary> 
            A reference to the next regular (non-special) token from the input
            stream.  If this is the last token from the input stream, or if the
            token manager has not read tokens beyond this one, this field is
            set to null.  This is true only if this token is also a regular
            token.  Otherwise, see below for a description of the contents of
            this field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.SpecialToken">
            <summary> 
            This field is used to access special tokens that occur prior to this
            token, but after the immediately preceding regular (non-special) token.
            If there are no such special tokens, this field is set to null.
            When there are more than one such special token, this field refers
            to the last of these special tokens, which in turn refers to the next
            previous special token through its specialToken field, and so on
            until the first special token (whose specialToken field is null).
            The next fields of special tokens refer to other special tokens that
            immediately follow it (without an intervening regular token).  If there
            is no such token, this field is null.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.Token.Value">
            <summary> 
            An optional attribute value of the Token.
            Tokens which are not used as syntactic sugar will often contain
            meaningful values that will be used later on by the compiler or
            interpreter. This attribute value is often different from the image.
            Any subclass of Token that actually wants to return a non-null value can
            override this method as appropriate.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.Token.#ctor">
            <summary> 
            No-argument constructor
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.Token.#ctor(System.Int32)">
            <summary> 
            Constructs a new token for the specified Image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.Token.#ctor(System.Int32,System.String)">
            <summary> 
            Constructs a new token for the specified Image and Kind.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.Token.ToString">
            <summary> 
            Returns the image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.Token.NewToken(System.Int32,System.String)">
            <summary> 
            Returns a new Token object, by default. However, if you want, you
            can create and return subclass objects based on the value of ofKind.
            Simply add the cases to the switch for all those special cases.
            For example, if you have a subclass of Token called IDToken that
            you want to create if ofKind is ID, simply add something like :
            
            case MyParserConstants.ID : return new IDToken(ofKind, image);
            
            to the following switch statement. Then you can cast matchedToken
            variable to the appropriate type and use sit in your lexical actions.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Classic.TokenMgrError">
            <summary>Token Manager Error. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.TokenMgrError.LEXICAL_ERROR">
            <summary> Lexical error occurred.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.TokenMgrError.STATIC_LEXER_ERROR">
            <summary> An attempt was made to create a second instance of a static token manager.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.TokenMgrError.INVALID_LEXICAL_STATE">
            <summary> Tried to change to an invalid lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.TokenMgrError.LOOP_DETECTED">
            <summary> Detected (and bailed out of) an infinite loop in the token manager.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Classic.TokenMgrError.errorCode">
            <summary> Indicates the reason why the exception is thrown. It will have
            one of the above 4 values.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.AddEscapes(System.String)">
            <summary> 
            Replaces unprintable characters by their escaped (or unicode escaped)
            equivalents in the given string
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.LexicalError(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char)">
            <summary>
            Returns a detailed message for the Error when it is thrown by the
            token manager to indicate a lexical error.
            </summary>
            <remarks>You can customize the lexical error message by modifying this method.</remarks>
            <param name="eofSeen">indicates if EOF caused the lexical error</param>
            <param name="lexState">lexical state in which this error occurred</param>
            <param name="errorLine">line number when the error occurred</param>
            <param name="errorColumn">column number when the error occurred</param>
            <param name="errorAfter">prefix that was seen before this error occurred</param>
            <param name="curChar">the offending character</param>
            <returns>Detailed error message</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Classic.TokenMgrError.Message">
            <summary> 
            You can also modify the body of this method to customize your error messages.
            For example, cases like LOOP_DETECTED and INVALID_LEXICAL_STATE are not
            of end-users concern, so you can return something like :
            
            "Internal Error : Please file a bug report .... "
            
            from this method for such cases in the release version of your parser.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.#ctor">
            <summary>No arg constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.#ctor(System.String,System.Int32)">
            <summary>Constructor with message and reason. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.#ctor(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char,System.Int32)">
            <summary>Full Constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Classic.TokenMgrError.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.ComplexPhrase.ComplexPhraseQueryParser">
            <summary>
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> which permits complex phrase query syntax eg "(john jon
            jonathan~) peters*".
            <para>
            Performs potentially multiple passes over Query text to parse any nested
            logic in PhraseQueries. - First pass takes any PhraseQuery content between
            quotes and stores for subsequent pass. All other query content is parsed as
            normal - Second pass parses any stored PhraseQuery content, checking all
            embedded clauses are referring to the same field and therefore can be
            rewritten as Span queries. All PhraseQuery clauses are expressed as
            ComplexPhraseQuery objects
            </para>
            <para>
            This could arguably be done in one pass using a new <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> but here I am
            working within the constraints of the existing parser as a base class. This
            currently simply feeds all phrase content through an analyzer to select
            phrase terms - any "special" syntax such as * ~ * etc are not given special
            status
            </para>
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.ComplexPhrase.ComplexPhraseQueryParser.InOrder">
            <summary>
            When <see cref="P:Lucene.Net.QueryParsers.ComplexPhrase.ComplexPhraseQueryParser.InOrder"/> is true, the search terms must
            exists in the documents as the same order as in query.
            Choose between ordered (true) or un-ordered (false) proximity search.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.ComplexPhrase.ComplexPhraseQueryParser.ComplexPhraseQuery">
            <summary>
            Used to handle the query content in between quotes and produced Span-based
            interpretations of the clauses.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser">
            <summary>
            The <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> enables arbitrary query parser extension
            based on a customizable field naming scheme. The lucene query syntax allows
            implicit and explicit field definitions as query prefix followed by a colon
            (':') character. The <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> allows to encode extension
            keys into the field symbol associated with a registered instance of
            <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/>. A customizable separation character separates the
            extension key from the actual field symbol. The <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>
            splits (<see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.SplitExtensionField(System.String,System.String)"/>) the
            extension key from the field symbol and tries to resolve the associated
            <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/>. If the parser can't resolve the key or the field
            token does not contain a separation character, <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>
            yields the same behavior as its super class <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>. Otherwise,
            if the key is associated with a <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> instance, the parser
            builds an instance of <see cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/> to be processed by
            <see cref="M:Lucene.Net.QueryParsers.Ext.ParserExtension.Parse(Lucene.Net.QueryParsers.Ext.ExtensionQuery)"/>.If a extension field does not
            contain a field part the default field for the query will be used.
            <para>
            To guarantee that an extension field is processed with its associated
            extension, the extension query part must escape any special characters like
            '*' or '['. If the extension query contains any whitespace characters, the
            extension query part must be enclosed in quotes.
            Example ('_' used as separation character):
            <pre>
              title_customExt:"Apache Lucene\?" OR content_customExt:prefix\*
            </pre>
            
            Search on the default field:
            <pre>
              _customExt:"Apache Lucene\?" OR _customExt:prefix\*
            </pre>
            </para>
            <para>
            The <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> itself does not implement the logic how
            field and extension key are separated or ordered. All logic regarding the
            extension key and field symbol parsing is located in <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/>.
            Customized extension schemes should be implemented by sub-classing
            <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/>.
            </para>
            <para>
            For details about the default encoding scheme see <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/>.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser.DEFAULT_EXTENSION">
            <summary>
             Default empty extensions instance
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser.#ctor(Lucene.Net.Util.LuceneVersion,System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Creates a new <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> instance
            </summary>
            <param name="matchVersion">the lucene version to use.</param>
            <param name="f">the default query field</param>
            <param name="a">the analyzer used to find terms in a query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser.#ctor(Lucene.Net.Util.LuceneVersion,System.String,Lucene.Net.Analysis.Analyzer,Lucene.Net.QueryParsers.Ext.Extensions)">
            <summary>
            Creates a new <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> instance
            </summary>
            <param name="matchVersion">the lucene version to use.</param>
            <param name="f">the default query field</param>
            <param name="a">the analyzer used to find terms in a query string</param>
            <param name="ext">the query parser extensions</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser.ExtensionFieldDelimiter">
            <summary>
            Returns the extension field delimiter character.
            </summary>
            <returns>the extension field delimiter character.</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery">
            <summary>
            <see cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/> holds all query components extracted from the original
            query string like the query field and the extension query string.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.ExtensionQuery.#ctor(Lucene.Net.QueryParsers.Classic.QueryParser,System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/>
            </summary>
            <param name="topLevelParser"></param>
            <param name="field">the query field</param>
            <param name="rawQueryString">the raw extension query string</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Ext.ExtensionQuery.Field">
            <summary>
            Returns the query field
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Ext.ExtensionQuery.RawQueryString">
            <summary>
            Returns the raw extension query string
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Ext.ExtensionQuery.TopLevelParser">
            <summary>
            Returns the top level parser which created this <see cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Ext.Extensions">
            <summary>
            The <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> class represents an extension mapping to associate
            <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> instances with extension keys. An extension key is a
            string encoded into a Lucene standard query parser field symbol recognized by
            <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>. The query parser passes each extension field
            token to <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.SplitExtensionField(System.String,System.String)"/> to separate the
            extension key from the field identifier.
            <para/>
            In addition to the key to extension mapping this class also defines the field
            name overloading scheme. <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/> uses the given
            extension to split the actual field name and extension key by calling
            <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.SplitExtensionField(System.String,System.String)"/>. To change the order or the key
            / field name encoding scheme users can subclass <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> to
            implement their own.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Ext.Extensions.DEFAULT_EXTENSION_FIELD_DELIMITER">
            <summary>
            The default extension field delimiter character. This constant is set to ':'
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.#ctor">
            <summary>
            Creates a new <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> instance with the
            <see cref="F:Lucene.Net.QueryParsers.Ext.Extensions.DEFAULT_EXTENSION_FIELD_DELIMITER"/> as a delimiter character.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.#ctor(System.Char)">
            <summary>
            Creates a new <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> instance
            </summary>
            <param name="extensionFieldDelimiter">the extensions field delimiter character</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.Add(System.String,Lucene.Net.QueryParsers.Ext.ParserExtension)">
            <summary>
            Adds a new <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> instance associated with the given key.
            </summary>
            <param name="key">the parser extension key</param>
            <param name="extension">the parser extension</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.GetExtension(System.String)">
            <summary>
            Returns the <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> instance for the given key or
            <code>null</code> if no extension can be found for the key.
            </summary>
            <param name="key">the extension key</param>
            <returns>the <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> instance for the given key or
            <code>null</code> if no extension can be found for the key.</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Ext.Extensions.ExtensionFieldDelimiter">
            <summary>
            Returns the extension field delimiter
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.SplitExtensionField(System.String,System.String)">
            <summary>
            Splits a extension field and returns the field / extension part as a
            <see cref="T:Tuple{string,string}"/>. This method tries to split on the first occurrence of the
            extension field delimiter, if the delimiter is not present in the string
            the result will contain a <code>null</code> value for the extension key and
            the given field string as the field value. If the given extension field
            string contains no field identifier the result pair will carry the given
            default field as the field value.
            </summary>
            <param name="defaultField">the default query field</param>
            <param name="field">the extension field string</param>
            <returns>a {<see cref="T:System.Tuple`2"/> with the field name as the <see cref="P:System.Tuple`2.Item1"/> and the
            extension key as the <see cref="P:System.Tuple`2.Item2"/></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.EscapeExtensionField(System.String)">
            <summary>
            Escapes an extension field. The default implementation is equivalent to
            <see cref="M:Lucene.Net.QueryParsers.Classic.QueryParserBase.Escape(System.String)"/>.
            </summary>
            <param name="extfield">the extension field identifier</param>
            <returns>the extension field identifier with all special chars escaped with
            a backslash character.</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String)">
            <summary>
            Builds an extension field string from a given extension key and the default
            query field. The default field and the key are delimited with the extension
            field delimiter character. This method makes no assumption about the order
            of the extension key and the field. By default the extension key is
            appended to the end of the returned string while the field is added to the
            beginning. Special Query characters are escaped in the result.
            <para>
            Note: <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> subclasses must maintain the contract between
            <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String)"/> and
            <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String,System.String)"/> where the latter inverts the
            former.
            </para>
            </summary>
            <param name="extensionKey">the extension key</param>
            <returns>escaped extension field identifier</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String,System.String)">
            <summary>
            Builds an extension field string from a given extension key and the default
            query field. The default field and the key are delimited with the extension
            field delimiter character. This method makes no assumption about the order
            of the extension key and the field. By default the extension key is
            appended to the end of the returned string while the field is added to the
            beginning. Special Query characters are escaped in the result.
            <para>
            Note: <see cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/> subclasses must maintain the contract between
            <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String)"/> and
            <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String,System.String)"/> where the latter inverts the
            former.
            </para>
            </summary>
            <param name="extensionKey">the extension key</param>
            <param name="field">the field to apply the extension on.</param>
            <returns>escaped extension field identifier</returns>
            <remarks>See <see cref="M:Lucene.Net.QueryParsers.Ext.Extensions.BuildExtensionField(System.String)"/> to use the default query field</remarks>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Ext.ParserExtension">
            <summary>
            This class represents an extension base class to the Lucene standard
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>. The
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> is generated by the JavaCC
            parser generator. Changing or adding functionality or syntax in the standard
            query parser requires changes to the JavaCC source file. To enable extending
            the standard query parser without changing the JavaCC sources and re-generate
            the parser the <see cref="T:Lucene.Net.QueryParsers.Ext.ParserExtension"/> can be customized and plugged into an
            instance of <see cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>, a direct subclass of
            <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.Extensions"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Ext.ExtendableQueryParser"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Ext.ParserExtension.Parse(Lucene.Net.QueryParsers.Ext.ExtensionQuery)">
            <summary>
            Processes the given <see cref="T:Lucene.Net.QueryParsers.Ext.ExtensionQuery"/> and returns a corresponding
            <see cref="T:Lucene.Net.Search.Query"/> instance. Subclasses must either return a <see cref="T:Lucene.Net.Search.Query"/>
            instance or raise a <see cref="T:Lucene.Net.QueryParsers.Classic.ParseException"/>. This method must not return
            <c>null</c>.
            </summary>
            <param name="query">the extension query</param>
            <returns>a new query instance</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Classic.ParseException">if the query can not be parsed.</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1">
            <summary>
            This interface is used by implementors classes that builds some kind of
            object from a query tree.
            
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1.Build(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            Builds some kind of object from a query tree.
            </summary>
            <param name="queryNode">the query tree root node</param>
            <returns>some object generated from the query tree</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1">
            <summary>
            This class should be used when there is a builder for each type of node.
            
            <para>
            The type of node may be defined in 2 different ways: - by the field name,
            when the node implements the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> interface - by its class,
            it keeps checking the class and all the interfaces and classes this class
            implements/extends until it finds a builder for that class/interface
            </para>
            <para>
            This class always check if there is a builder for the field name before it
            checks for the node class. So, field name builders have precedence over class
            builders.
            </para>
            <para>
            When a builder is found for a node, it's called and the node is passed to the
            builder. If the returned built object is not <c>null</c>, it's tagged
            on the node using the tag <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/>.
            </para>
            <para>
            The children are usually built before the parent node. However, if a builder
            associated to a node is an instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1"/>, the node is
            delegated to this builder and it's responsible to build the node and its
            children.
            </para>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1.#ctor">
            <summary>
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1"/> constructor.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1.SetBuilder(System.String,Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder{`0})">
            <summary>
            Associates a field name with a builder.
            </summary>
            <param name="fieldName">the field name</param>
            <param name="builder">the builder to be associated</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1.SetBuilder(System.Type,Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder{`0})">
            <summary>
            Associates a <see cref="T:System.Type">class</see> (that implements <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>) with a builder
            </summary>
            <param name="queryNodeClass">The type (a class that implements <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>)</param>
            <param name="builder">the builder to be associated</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1.Build(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            Builds some kind of object from a query tree. Each node in the query tree
            is built using an specific builder associated to it.
            </summary>
            <param name="queryNode">the query tree root node</param>
            <returns>the built object</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if some node builder throws a 
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException"/> or if there is a node which had no
            builder associated to it</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder">
            <summary>
            LUCENENET specific class for accessing static members of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1"/>
            without referencing its generic closing type.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID">
            <summary>
            This tag is used to tag the nodes in a query tree with the built objects
            produced from their own associated builder.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig">
            <summary>
            This class is the base of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> and <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>.
            It has operations to set, unset and get configuration values.
            <para>
            Each configuration is is a key->value pair. The key should be an unique
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey`1"/> instance and it also holds the value's type.
            </para>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey`1"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig.Get``1(Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey{``0})">
            <summary>
            Returns the value held by the given key.
            </summary>
            <typeparam name="T">the value's type</typeparam>
            <param name="key">the key, cannot be <c>null</c></param>
            <returns>the value held by the given key</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig.Has``1(Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey{``0})">
            <summary>
            Returns <c>true</c> if there is a value set with the given key, otherwise <c>false</c>.
            </summary>
            <typeparam name="T">the value's type</typeparam>
            <param name="key">the key, cannot be <c>null</c></param>
            <returns><c>true</c> if there is a value set with the given key, otherwise <c>false</c></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig.Set``1(Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey{``0},``0)">
            <summary>
            Sets a key and its value.
            </summary>
            <typeparam name="T">the value's type</typeparam>
            <param name="key">the key, cannot be <c>null</c></param>
            <param name="value">value to set</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig.Unset``1(Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey{``0})">
            <summary>
            Unsets the given key and its value.
            </summary>
            <typeparam name="T">the value's type</typeparam>
            <param name="key">the key</param>
            <returns><c>true</c> if the key and value was set and removed, otherwise <c>false</c></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey`1">
            <summary>
            An instance of this class represents a key that is used to retrieve a value
            from <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig"/>. It also holds the value's type, which is
            defined in the generic argument.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.AbstractQueryConfig"/>
            <typeparam name="T">the value's type</typeparam>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey">
            <summary>
            LUCENENET specific class used to access the NewInstance
            static method without referring to the ConfigurationKey{T}'s
            generic closing type.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.ConfigurationKey.NewInstance``1">
            <summary>
            Creates a new instance.
            </summary>
            <typeparam name="T">the value's type</typeparam>
            <returns>a new instance</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig">
            <summary>
            This class represents a field configuration.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig.#ctor(System.String)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>
            </summary>
            <param name="fieldName">the field name, it cannot be null</param>
            <exception cref="T:System.ArgumentException">if the field name is null</exception>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig.Field">
            <summary>
            Gets the field name this configuration represents.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener">
            <summary>
            This interface should be implemented by classes that wants to listen for
            field configuration requests. The implementation receives a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> object and may add/change its configuration.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener.BuildFieldConfig(Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig)">
            <summary>
            This method is called every time a field configuration is requested.
            </summary>
            <param name="fieldConfig">the field configuration requested, should never be null</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler">
            <summary>
            This class can be used to hold any query configuration and no field
            configuration. For field configuration, it creates an empty
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> object and delegate it to field config listeners, 
            these are responsible for setting up all the field configuration.
            <para>
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> should be extended by classes that intends to
            provide configuration to <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/> objects.
            </para>
            <para>
            The class that extends <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> should also provide
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> objects for each collection field.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler.GetFieldConfig(System.String)">
            <summary>
            Returns an implementation of
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> for a specific field name. If the implemented
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> does not know a specific field name, it may
            return <c>null</c>, indicating there is no configuration for that
            field.
            </summary>
            <param name="fieldName">the field name</param>
            <returns>A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>object containing the field name
            configuration or <c>null</c>, if the implemented
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> has no configuration for that field</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler.AddFieldConfigListener(Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener)">
            <summary>
            Adds a listener. The added listeners are called in the order they are
            added.
            </summary>
            <param name="listener">the listener to be added</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages">
            <summary>
            A strongly-typed resource class, for looking up localized strings, etc.
            <para/>
            The localized resources can be replaced by end users by calling the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.SetResourceProvider(Lucene.Net.Util.IResourceProvider)"/>
            method and supplying an instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/> with custom
            <see cref="T:System.Resources.ResourceManager"/> instances provided in its constructor.
            <code>
            QueryParserMessages.SetResourceProvider(new QueryParserResourceProvider(SomeResource.ResourceManager, SomeOtherResource.ResourceManager));
            </code>
            <c>SomeResource</c> and <c>SomeOtherResource</c> are auto-generated designer classes which can be generated by Visual Studio when adding
            resource files (.resx) to a project. These resources can optionally be localized in different cultures and optionally be distributed as satellite
            assemblies. See the <see cref="T:System.Resources.ResourceManager"/> documentation for tips on how to get started, but do note this is a broad topic.
            <para/>
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/> makes no assumptions on the method used to generate or deploy resources, the only requirement is that you provide
            a <see cref="T:System.Resources.ResourceManager"/> that can find them. Note that it is also possible to subclass <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/>
            or implement <see cref="T:Lucene.Net.Util.IResourceProvider"/> directly to provide resources from any source.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.GetResourceProvider">
            <summary>
            Gets the associated resource provider.
            </summary>
            <returns>The current resource provider.</returns>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.SetResourceProvider(Lucene.Net.Util.IResourceProvider)"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.SetResourceProvider(Lucene.Net.Util.IResourceProvider)">
            <summary>
            Sets the <see cref="T:Lucene.Net.Util.IResourceProvider"/> instance used to provide
            localized <see cref="T:System.String"/>s and <see cref="T:System.Object"/>s.
            </summary>
            <param name="provider">The new <see cref="T:Lucene.Net.Util.IResourceProvider"/>.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="provider"/> parameter is <c>null</c>.</exception>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.GetResourceProvider"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.ARGUMENT_CANNOT_BE_NULL">
            <summary>
              Looks up a localized string similar to Value cannot be null..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.COULD_NOT_PARSE_NUMBER">
            <summary>
              Looks up a localized string similar to Could not parse text &quot;{0}&quot; using {1}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.EMPTY_MESSAGE">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX">
            <summary>
              Looks up a localized string similar to Syntax Error: {0}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_CANNOT_PARSE">
            <summary>
              Looks up a localized string similar to Syntax Error, cannot parse {0}: {1}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_ESCAPE_CHARACTER">
            <summary>
              Looks up a localized string similar to Term can not end with escape character..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_ESCAPE_NONE_HEX_UNICODE">
            <summary>
              Looks up a localized string similar to Non-hex character in Unicode escape sequence: {0}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_ESCAPE_UNICODE_TRUNCATION">
            <summary>
              Looks up a localized string similar to Truncated unicode escape sequence..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_FUZZY_EDITS">
            <summary>
              Looks up a localized string similar to Fractional edit distances are not allowed..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.INVALID_SYNTAX_FUZZY_LIMITS">
            <summary>
              Looks up a localized string similar to The similarity value for a fuzzy search must be between 0.0 and 1.0..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.LEADING_WILDCARD_NOT_ALLOWED">
            <summary>
              Looks up a localized string similar to Leading wildcard is not allowed: {0}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.LUCENE_QUERY_CONVERSION_ERROR">
            <summary>
              Looks up a localized string similar to Cannot convert query to lucene syntax: {0} error: {1}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.NODE_ACTION_NOT_SUPPORTED">
            <summary>
              Looks up a localized string similar to This node does not support this action..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.NUMBER_CANNOT_BE_NEGATIVE">
            <summary>
              Looks up a localized string similar to The parameter must be greater than or equal to zero..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.NUMBER_CLASS_NOT_SUPPORTED_BY_NUMERIC_RANGE_QUERY">
            <summary>
              Looks up a localized string similar to Number class not supported by NumericRangeQueryNode: {0}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.NUMERIC_CANNOT_BE_EMPTY">
            <summary>
              Looks up a localized string similar to Field &quot;{0}&quot; is numeric and cannot have an empty value..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.PARAMETER_VALUE_NOT_SUPPORTED">
            <summary>
              Looks up a localized string similar to Parameter {1} with value {0} not supported..
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.TOO_MANY_BOOLEAN_CLAUSES">
            <summary>
              Looks up a localized string similar to Too many boolean clauses, the maximum supported is {0}: {1}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.UNSUPPORTED_NUMERIC_DATA_TYPE">
            <summary>
              Looks up a localized string similar to Unsupported NumericField.DataType: {0}.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.WILDCARD_NOT_SUPPORTED">
            <summary>
              Looks up a localized string similar to Wildcard is not supported for query: {0}.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider">
            <summary>
            The default <see cref="T:Lucene.Net.Util.IResourceProvider"/> implementation for the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages"/> class.
            This class can be set in the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserMessages.SetResourceProvider(Lucene.Net.Util.IResourceProvider)"/> method
            and supplied with one or more <see cref="T:System.Resources.ResourceManager"/> instances that can override the default query parser
            messages (generally, they are exception messages).
            <para/>
            Alternatively, this class may be overridden to provide either a custom <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> or
            to alter the fallback logic in either <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.GetString(System.String,System.Globalization.CultureInfo)"/> or <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.GetObject(System.String,System.Globalization.CultureInfo)"/>.
            The performance of this class may be improved significantly by specifying the <see cref="T:System.Resources.ResourceManager"/> that a specific resource
            can be found in rather than attempting all of them.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/> class with default values.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Resources.ResourceManager[])">
            <summary>
            Initializes a new instance of the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/> class with the specified
            <paramref name="resourceManagers"/>. The <paramref name="resourceManagers"/> may override resources
            in the <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/>, provided they have the same names.
            <para/>
            Note that not all of the resources are required to be provided and if the name doesn't exist it will
            fall back to the next <see cref="T:System.Resources.ResourceManager"/> that is provided and ultimately will try the
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> if the resource is not found.
            </summary>
            <param name="resourceManagers">One or more <see cref="T:System.Resources.ResourceManager"/> instances that provide
            localized resources. The <paramref name="resourceManagers"/> are used in the order they are specified, and the first one
            that provides a non-<c>null</c> value for a given resource name wins.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="resourceManagers"/> is <c>null</c>.</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Collections.Generic.IList{System.Resources.ResourceManager})">
            <summary>
            Initializes a new instance of the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider"/> class with the specified
            <paramref name="resourceManagers"/>. The <paramref name="resourceManagers"/> may override resources
            in the <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/>, provided they have the same names.
            <para/>
            Note that not all of the resources are required to be provided and if the name doesn't exist it will
            fall back to the next <see cref="T:System.Resources.ResourceManager"/> that is provided and ultimately will try the
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> if the resource is not found.
            </summary>
            <param name="resourceManagers">One or more <see cref="T:System.Resources.ResourceManager"/> instances that provide
            localized resources. The <paramref name="resourceManagers"/> are used in the order they are specified, and the first one
            that provides a non-<c>null</c> value for a given resource name wins.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="resourceManagers"/> is <c>null</c>.</exception>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager">
            <summary>
            Gets the cached <see cref="T:System.Resources.ResourceManager"/> instance used as the fallback by this class.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.GetString(System.String,System.Globalization.CultureInfo)">
            <summary>Returns the value of the string resource localized for the specified <paramref name="culture"/>.
            <para/>
            The resource is searched for
            first in the <see cref="T:System.Resources.ResourceManager"/> instances passed in the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Collections.Generic.IList{System.Resources.ResourceManager})"/>
            or <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Resources.ResourceManager[])"/> constructor, in the order they are specified. If not found, the
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> is used. This method may return <c>null</c> if the resource with the given name is not found.</summary>
            <inheritdoc/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.GetObject(System.String,System.Globalization.CultureInfo)">
            <summary>Gets the value of the specified non-string resource localized for the specified <paramref name="culture"/>.
            <para/>
            The resource is searched for
            first in the <see cref="T:System.Resources.ResourceManager"/> instances passed in the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Collections.Generic.IList{System.Resources.ResourceManager})"/>
            or <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Resources.ResourceManager[])"/> constructor, in the order they are specified. If not found, the
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> is used. This method may return <c>null</c> if the resource with the given name is not found.</summary>
            <inheritdoc/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.GetStream(System.String,System.Globalization.CultureInfo)">
            <summary>Returns an unmanaged memory stream object from the specified resource, using the specified <paramref name="culture"/>.
            <para/>
            The resource is searched for
            first in the <see cref="T:System.Resources.ResourceManager"/> instances passed in the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Collections.Generic.IList{System.Resources.ResourceManager})"/>
            or <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.#ctor(System.Resources.ResourceManager[])"/> constructor, in the order they are specified. If not found, the
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.Messages.QueryParserResourceProvider.FallbackResourceManager"/> is used. This method may return <c>null</c> if the resource with the given name is not found.</summary>
            <inheritdoc/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode"/> represents an AND boolean operation performed on a
            list of nodes.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})">
            <summary>
            
            </summary>
            <param name="clauses">the query nodes to be and'ed</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AnyQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AnyQueryNode"/> represents an ANY operator performed on a list of
            nodes.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AnyQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode},System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="clauses">the query nodes to be or'ed</param>
            <param name="field"></param>
            <param name="minimumMatchingElements"></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AnyQueryNode.Field">
            <summary>
            Gets or sets the field name. Returns null if the field was not specified.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AnyQueryNode.GetFieldAsString">
            <summary>
            null if the field was not specified
            </summary>
            <returns>the field as a <see cref="T:System.String"/></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> represents a list of elements which do not have an
            explicit boolean operator defined between them. It can be used to express a
            boolean query that intends to use the default boolean operator.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})">
            <summary>
            
            </summary>
            <param name="clauses">the query nodes to be and'ed</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode"/> boosts the QueryNode tree which is under this node.
            So, it must only and always have one child.
            
            The boost value may vary from 0.0 to 1.0.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode,System.Single)">
            <summary>
            Constructs a boost node
            </summary>
            <param name="query">the query to be boosted</param>
            <param name="value">the boost value, it may vary from 0.0 to 1.0</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode.Child">
            <summary>
            Gets the single child which this node boosts.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode.Value">
            <summary>
            Gets the boost value. It may vary from 0.0 to 1.0.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode.GetValueString">
            <summary>
            Returns the boost value parsed to a string.
            </summary>
            <returns>the parsed value</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.DeletedQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.DeletedQueryNode"/> represents a node that was deleted from the query
            node tree. It can be removed from the tree using the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.RemoveDeletedQueryNodesProcessor"/> processor.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode">
            <summary>
            A query node implements <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> interface to indicate that its
            children and itself are associated to a specific field.
            
            If it has any children which also implements this interface, it must ensure
            the children are associated to the same field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode.Field">
            <summary>
            Gets or Sets the field name associated to the node and every node under it.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> represents a element that contains field/text tuple
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.m_field">
            <summary>
            The term's field
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.m_text">
            <summary>
            The term's text.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.m_begin">
            <summary>
            The term's begin position.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.m_end">
            <summary>
            The term's end position.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.m_positionIncrement">
            <summary>
            The term's position increment.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.GetTextAsString">
            <summary>
            
            </summary>
            <returns>the term</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.GetFieldAsString">
            <summary>
            null if the field was not specified in the query string
            </summary>
            <returns>the field</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode.Text">
            <summary>
            Gets or Sets the "original" form of the term.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldValuePairQueryNode`1">
            <summary>
            This interface should be implemented by <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> that holds a field
            and an arbitrary value.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IValueQueryNode`1"/>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/> represents a element that contains
            field/text/similarity tuple
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode.#ctor(System.String,System.String,System.Single,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="term">Term token to use for building term for the query</param>
            <param name="minSimilarity">similarity value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Single,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="term">Term token to use for building term for the query</param>
            <param name="minSimilarity">similarity value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Single,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">Name of the field query will use.</param>
            <param name="term">Term token to use for building term for the query</param>
            <param name="minSimilarity">similarity value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode.Similarity">
            <summary>
            Gets or Sets the similarity
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode"/> represents a location where the original user typed
            real parenthesis on the query string. This class is useful for queries like:
            a) a AND b OR c b) ( a AND b) OR c
            
            Parenthesis might be used to define the boolean operation precedence.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            This IQueryNode is used to identify parenthesis on the original query string
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchAllDocsQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchAllDocsQueryNode"/> indicates that a query node tree or subtree
            will match all documents if executed in the index.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode"/> indicates that a query node tree or subtree
            will not match any documents if executed in the index.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> indicates the modifier value (+,-,?,NONE) for
            each term on the query string. For example "+t1 -t2 t3" will have a tree of:
            <blockquote>
            &lt;BooleanQueryNode&gt; &lt;ModifierQueryNode modifier="MOD_REQ"&gt; &lt;t1/&gt;
            &lt;/ModifierQueryNode&gt; &lt;ModifierQueryNode modifier="MOD_NOT"&gt; &lt;t2/&gt;
            &lt;/ModifierQueryNode&gt; &lt;t3/&gt; &lt;/BooleanQueryNode&gt;
            </blockquote>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode,Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier)">
            <summary>
            Used to store the modifier value on the original query string
            </summary>
            <param name="query">QueryNode subtree</param>
            <param name="mod">Modifier Value</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier">
            <summary>
            Modifier type: such as required (REQ), prohibited (NOT)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.NoTokenFoundQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.NoTokenFoundQueryNode"/> is used if a term is convert into no tokens
            by the tokenizer/lemmatizer/analyzer (null).
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OpaqueQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OpaqueQueryNode"/> is used for specify values that are not supposed to
            be parsed by the parser. For example: and XPATH query in the middle of a
            query string a b @xpath:'/bookstore/book[1]/title' c d
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OpaqueQueryNode.#ctor(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="schema">schema identifier</param>
            <param name="value">value that was not parsed</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OpaqueQueryNode.Schema">
            <summary>
            Gets the schema
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OpaqueQueryNode.Value">
            <summary>
            Gets the value
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OrQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OrQueryNode"/> represents an OR boolean operation performed on a list
            of nodes.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OrQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})">
            <summary>
            
            </summary>
            <param name="clauses">the query nodes to be or'ed</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode"/> is used to store queries like
            /company/USA/California /product/shoes/brown. QueryText are objects that
            contain the text, begin position and end position in the query.
            <para>
            Example how the text parser creates these objects:
            </para>
            <code>
            IList&lt;PathQueryNode.QueryText&gt; values = new List&lt;PathQueryNode.QueryText&gt;();
            values.Add(new PathQueryNode.QueryText("company", 1, 7));
            values.Add(new PathQueryNode.QueryText("USA", 9, 12));
            values.Add(new PathQueryNode.QueryText("California", 14, 23));
            QueryNode q = new PathQueryNode(values);
            </code>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText">
            <summary>
            Term text with a beginning and end position
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.begin">
            <summary>
            != null The term's begin position.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.end">
            <summary>
            The term's end position.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="value">text value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.Value">
            <summary>
            Gets the value
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.Begin">
            <summary>
            Gets the begin
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText.End">
            <summary>
            Gets the end
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.QueryText})">
            <summary>
            
            </summary>
            <param name="pathElements">List of QueryText objects</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.PathElements">
            <summary>
            Gets or Sets the a List with all QueryText elements
            </summary>
            <returns>QueryText List size</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.GetPathElement(System.Int32)">
            <summary>
            Returns the a specific QueryText element
            </summary>
            <param name="index"></param>
            <returns>QueryText List size</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.GetFirstPathElement">
            <summary>
            Returns the <see cref="T:System.String"/> value of a specific QueryText element
            </summary>
            <returns>The <see cref="T:System.String"/> for a specific QueryText element</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PathQueryNode.GetPathElements(System.Int32)">
            <summary>
            Returns a List QueryText element from position <paramref name="startIndex"/> 
            </summary>
            <param name="startIndex"></param>
            <returns>a List QueryText element from position <paramref name="startIndex"/></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PhraseSlopQueryNode">
            <summary>
            Query node for <see cref="T:Lucene.Net.Search.PhraseQuery"/>'s slop factor.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.PhraseSlopQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode,System.Int32)">
            <summary>
            
            </summary>
            <param name="query"></param>
            <param name="value"></param>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError">throw in overridden method to disallow</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode"/> represents a query where the terms should meet
            specific distance conditions. (a b c) WITHIN [SENTENCE|PARAGRAPH|NUMBER]
            [INORDER] ("a" "b" "c") WITHIN [SENTENCE|PARAGRAPH|NUMBER] [INORDER]
            
            TODO: Add this to the future standard Lucene parser/processor/builder
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.Type">
            <summary>
            Distance condition: PARAGRAPH, SENTENCE, or NUMBER
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode},System.String,Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.Type,System.Int32,System.Boolean)">
            <summary>
            
            </summary>
            <param name="clauses">QueryNode children</param>
            <param name="field">field name</param>
            <param name="type">type of proximity query</param>
            <param name="distance">positive integer that specifies the distance</param>
            <param name="inorder">true, if the tokens should be matched in the order of the clauses</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.#ctor(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode},System.String,Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.Type,System.Boolean)">
            <summary>
            
            </summary>
            <param name="clauses">QueryNode children</param>
            <param name="field">field name</param>
            <param name="type">type of proximity query</param>
            <param name="inorder">true, if the tokens should be matched in the order of the clauses</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.Distance">
            <summary>
            Gets the distance
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.Field">
            <summary>
            Gets or Sets the field. Returns null if the field was not specified in the query string.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.GetFieldAsString">
            <summary>
            Gets the field as a string. Returns null if the field was not specified in the query string.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityQueryNode.IsInOrder">
            <summary>
            terms must be matched in the specified order
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ProximityType">
            <summary>
            utility class containing the distance condition and number
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> is a interface implemented by all nodes on a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
            tree.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.ToQueryString(Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax)">
            <summary>
            convert to a query string understood by the query parser
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.ToString">
            <summary>
            for printing
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.GetChildren">
            <summary>
            get Children nodes
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.IsLeaf">
            <summary>
            verify if a node is a Leaf node
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.ContainsTag(System.String)">
            <summary>
            verify if a node contains a tag
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.GetTag(System.String)">
            <summary>
            Returns object stored under that tag name
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.TryGetTag(System.String,System.Object@)">
            <summary>
            Gets the tag associated with the specified tagName.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.CloneTree">
            <summary>
            Recursive clone the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> tree. The tags are not copied to the new tree
            when you call the <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.CloneTree"/> method.
            </summary>
            <returns>the cloned tree</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.Add(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            add a new child to a non Leaf node
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.Set(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})">
            <summary>
            reset the children of a node
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.SetTag(System.String,System.Object)">
            <summary>
            Associate the specified value with the specified <paramref name="tagName"/>. If the <paramref name="tagName"/>
            already exists, the old value is replaced. The <paramref name="tagName"/> and <paramref name="value"/> cannot be
            null. <paramref name="tagName"/> will be converted to lowercase.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.UnsetTag(System.String)">
            <summary>
            Unset a tag. <paramref name="tagName"/> will be converted to lowercase.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.TagMap">
            <summary>
            Gets a map containing all tags attached to this query node. 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.RemoveFromParent">
            <summary>
            Removes this query node from its parent.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.RemoveChildren(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            Remove a child node.
            </summary>
            <param name="childNode">Which child to remove.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode"/> is the default implementation of the interface
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.PLAINTEXT_FIELD_NAME">
            <summary>
            index default field
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.GetChildren">
            <summary>
            a List for QueryNode object. Returns null, for nodes that do not
            contain children. All leaf Nodes return null.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.ContainsTag(System.String)">
            <summary>
            verify if a node contains a tag
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.TryGetTag(System.String,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.m_toQueryStringIgnoreFields">
            <summary>
            If set to true the the method toQueryString will not write field names
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.IsDefaultField(System.String)">
            <summary>
            This method is use toQueryString to detect if fld is the default field
            </summary>
            <param name="fld">field name</param>
            <returns>true if fld is the default field</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.ToString">
            <summary>
            Every implementation of this class should return pseudo xml like this:
            
            For FieldQueryNode: &lt;field start='1' end='2' field='subject' text='foo'/&gt;
            </summary>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode.ToString"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QueryNode.TagMap">
            <summary>
            Gets a map containing all tags attached to this query node.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QuotedFieldQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QuotedFieldQueryNode"/> represents phrase query. Example:
            "life is great"
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QuotedFieldQueryNode.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QuotedFieldQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.QuotedFieldQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IRangeQueryNode`1">
            <summary>
            This interface should be implemented by an <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> that represents
            some kind of range query.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IRangeQueryNode">
            <summary>
            LUCENENET specific interface for identifying a
            RangeQueryNode without specifying its generic closing type
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> represents phrase query with a slop.
            
            From Lucene FAQ: Is there a way to use a proximity operator (like near or
            within) with Lucene? There is a variable called slop that allows you to
            perform NEAR/WITHIN-like queries. By default, slop is set to 0 so that only
            exact phrases will match. When using TextParser you can use this syntax to
            specify the slop: "doug cutting"~2 will find documents that contain
            "doug cutting" as well as ones that contain "cutting doug".
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode,System.Int32)">
            <summary>
            
            </summary>
            <param name="query">QueryNode Tree with the phrase</param>
            <param name="value">slop value</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ITextableQueryNode">
            <summary>
            Interface for a node that has text as a <see cref="T:J2N.Text.ICharSequence"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode"/> represents a node created by a code that
            tokenizes/lemmatizes/analyzes.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IValueQueryNode`1">
            <summary>
            This interface should be implemented by <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> that holds an
            arbitrary value.
            </summary>
            <typeparam name="T">the value's type</typeparam>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax">
            <summary>
            A parser needs to implement <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax"/> to allow the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> 
            to escape the queries, when the ToQueryString method is called.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax.Escape(System.String,System.Globalization.CultureInfo,Lucene.Net.QueryParsers.Flexible.Core.Parser.EscapeQuerySyntaxType)">
            <summary>
            
            </summary>
            <param name="text"> text to be escaped</param>
            <param name="locale">locale for the current query</param>
            <param name="type">select the type of escape operation to use</param>
            <returns>escaped text</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax.Escape(J2N.Text.ICharSequence,System.Globalization.CultureInfo,Lucene.Net.QueryParsers.Flexible.Core.Parser.EscapeQuerySyntaxType)">
            <summary>
            
            </summary>
            <param name="text"> text to be escaped</param>
            <param name="locale">locale for the current query</param>
            <param name="type">select the type of escape operation to use</param>
            <returns>escaped text</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.EscapeQuerySyntaxType">
            <summary>
            Type of escaping: String for escaping syntax,
            NORMAL for escaping reserved words (like AND) in terms
            </summary>
            <remarks>Renamed from Type in Lucene to prevent collisions with System.Type.</remarks>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser">
            <summary>
            A parser needs to implement <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/> interface
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser.Parse(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="query">query data to be parsed</param>
            <param name="field">default field name</param>
            <returns>QueryNode tree</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.NoChildOptimizationQueryNodeProcessor">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.NoChildOptimizationQueryNodeProcessor"/> removes every
            BooleanQueryNode, BoostQueryNode, TokenizedPhraseQueryNode or
            ModifierQueryNode that do not have a valid children.
            <para>
            Example: When the children of these nodes are removed for any reason then the
            nodes may become invalid.
            </para>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor">
            <summary>
            An <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/> is an interface for classes that process a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> tree.
            <para>
            The implementor of this class should perform some operation on a query node
            tree and return the same or another query node tree.
            </para>
            <para>
            It also may carry a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> object that contains
            configuration about the query represented by the query tree or the
            collection/index where it's intended to be executed.
            </para>
            <para>
            In case there is any <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> associated to the query tree
            to be processed, it should be set using
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/> before
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.Process(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/> is invoked.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.Process(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            Processes a query node tree. It may return the same or another query tree.
            I should never return <c>null</c>.
            </summary>
            <param name="queryTree">tree root node</param>
            <returns>the processed query tree</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            Sets the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> associated to the query tree.
            </summary>
            <param name="queryConfigHandler"></param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.GetQueryConfigHandler">
            <summary>
            Returns the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> associated to the query tree if any,
            otherwise it returns <c>null</c>
            </summary>
            <returns>the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> associated to the query tree if any,
            otherwise it returns <c>null</c></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor">
            <summary>
            This is a default implementation for the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
            interface, it's an abstract class, so it should be extended by classes that
            want to process a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> tree.
            <para>
            This class process <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>s from left to right in the tree. While
            it's walking down the tree, for every node,
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PreProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/> is invoked. After a node's children are
            processed, <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PostProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/> is invoked for that node.
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.SetChildrenOrder(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})"/> is invoked before
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PostProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/> only if the node has at least one child,
            in <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.SetChildrenOrder(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})"/> the implementor might redefine the
            children order or remove any children from the children list.
            </para>
            <para>
            Here is an example about how it process the nodes:
            </para>
            <pre>
                 a
                / \
               b   e
              / \
             c   d
            </pre>
            <para>
            Here is the order the methods would be invoked for the tree described above:
            </para>
            <code>
                PreProcessNode( a );
                PreProcessNode( b );
                PreProcessNode( c );
                PostProcessNode( c );
                PreProcessNode( d );
                PostProcessNode( d );
                SetChildrenOrder( bChildrenList );
                PostProcessNode( b );
                PreProcessNode( e );
                PostProcessNode( e );
                SetChildrenOrder( aChildrenList );
                PostProcessNode( a )
            </code>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.ProcessChildren(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            This method is called every time a child is processed.
            </summary>
            <param name="queryTree">the query node child to be processed</param>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something goes wrong during the query node processing</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            For reference about this method check:
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>.
            </summary>
            <param name="queryConfigHandler">the query configuration handler to be set.</param>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.GetQueryConfigHandler">
            <summary>
            For reference about this method check:
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.GetQueryConfigHandler"/>.
            </summary>
            <returns><see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> the query configuration handler to be set.</returns>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PreProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            This method is invoked for every node when walking down the tree.
            </summary>
            <param name="node">the query node to be pre-processed</param>
            <returns>a query node</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something goes wrong during the query node processing</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PostProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            This method is invoked for every node when walking up the tree.
            </summary>
            <param name="node">node the query node to be post-processed</param>
            <returns>a query node</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something goes wrong during the query node processing</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.SetChildrenOrder(System.Collections.Generic.IList{Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode})">
            <summary>
            This method is invoked for every node that has at least on child. It's
            invoked right before <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessor.PostProcessNode(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/> is invoked.
            </summary>
            <param name="children">the list containing all current node's children</param>
            <returns>a new list containing all children that should be set to the current node</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something goes wrong during the query node processing</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline"/> class should be used to build a query
            node processor pipeline.
            <para/>
            When a query node tree is processed using this class, it passes the query
            node tree to each processor on the pipeline and the result from each
            processor is passed to the next one, always following the order the
            processors were on the pipeline.
            <para/>
            When a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> object is set on a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline"/>, it also takes care of setting this
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> on all processor on pipeline.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.#ctor">
            <summary>
            Constructs an empty query node processor pipeline.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            Constructs with a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> object.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.GetQueryConfigHandler">
            <summary>
            For reference about this method check:
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.GetQueryConfigHandler"/>.
            </summary>
            <returns><see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> the query configuration handler to be set.</returns>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Process(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            For reference about this method check:
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.Process(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)"/>.
            </summary>
            <param name="queryTree">the query node tree to be processed</param>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something goes wrong during the query node processing</exception>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            For reference about this method check:
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>.
            </summary>
            <param name="queryConfigHandler">the query configuration handler to be set.</param>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor.GetQueryConfigHandler"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Add(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.Add(`0)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Clear">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.Clear"/> 
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Item(System.Int32)">
            <summary>
            <see cref="P:System.Collections.Generic.IList`1.Item(System.Int32)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.IndexOf(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            <see cref="M:System.Collections.Generic.IList`1.IndexOf(`0)"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.GetEnumerator">
            <summary>
            <see cref="M:System.Collections.Generic.IEnumerable`1.GetEnumerator"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Remove(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.Remove(`0)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.RemoveAt(System.Int32)">
            <summary>
            <see cref="M:System.Collections.Generic.IList`1.RemoveAt(System.Int32)"/> 
            </summary>
            <param name="index"></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Count">
            <summary>
            <see cref="P:System.Collections.Generic.ICollection`1.Count"/> 
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.IsReadOnly">
            <summary>
            <see cref="P:System.Collections.Generic.ICollection`1.IsReadOnly"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.GetView(System.Int32,System.Int32)">
            <summary>
            <see cref="M:J2N.Collections.Generic.List`1.GetView(System.Int32,System.Int32)"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.System#Collections#Generic#ICollection{Lucene#Net#QueryParsers#Flexible#Core#Processors#IQueryNodeProcessor}#Add(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.Add(`0)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.Contains(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.Contains(`0)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.CopyTo(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor[],System.Int32)">
            <summary>
            <see cref="M:System.Collections.Generic.ICollection`1.CopyTo(`0[],System.Int32)"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            <see cref="M:System.Collections.IEnumerable.GetEnumerator"/> 
            </summary>
            <returns></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.RemoveDeletedQueryNodesProcessor">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.QueryNodeProcessorPipeline"/> class removes every instance of
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.DeletedQueryNode"/> from a query node tree. If the resulting root node
            is a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.DeletedQueryNode"/>, <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode"/> is returned.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError">
            <summary>
            A query node error.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError"/>
            with the specified <paramref name="message"/> and <paramref name="paramName"/>.
            </summary>
            <param name="paramName">The name of the parameter that caused the current error.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError"/>
            with the specified <paramref name="message"/>, <paramref name="paramName"/> and <paramref name="innerException"/>.
            </summary>
            <param name="paramName">The name of the parameter that caused the current error.</param>
            <param name="message">The message that describes the error.</param>
            <param name="innerException">The exception that is the cause of the current exception.
            If the <paramref name="innerException"/> parameter is not a <c>null</c> reference, the
            current exception is raised in a catch block that handles the inner exception.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError"/>
            with the specified <paramref name="message"/>.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError"/>
            with a specified inner exception that is the cause of this exception.
            </summary>
            <param name="innerException">The exception that is the cause of the current exception.
            If the <paramref name="innerException"/> parameter is not a <c>null</c> reference, the
            current exception is raised in a catch block that handles the inner exception.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError"/>
            with a specified error message and a reference to the inner exception
            that is the cause of this exception.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="innerException">The exception that is the cause of the current exception.
            If the <paramref name="innerException"/> parameter is not a <c>null</c> reference, the
            current exception is raised in a catch block that handles the inner exception.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeError.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">
            <summary>
            This exception should be thrown if something wrong happens when dealing with
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>s.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException"/>
            with the specified <paramref name="message"/>.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException"/>
            with a specified inner exception
            that is the cause of this exception.
            </summary>
            <param name="cause">An exception instance to wrap</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException"/>
            with a specified error message and a reference to the inner exception
            that is the cause of this exception.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="innerException">An exception instance to wrap</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeParseException">
            <summary>
            This should be thrown when an exception happens during the query parsing from
            string to the query node tree.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeParseException.ErrorToken">
            <summary>
            The errorToken in the query
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeParseException.BeginLine">
            <summary>
            For EndOfLine and EndOfFile ("&lt;EOF&gt;") parsing problems the last char in the
            string is returned. For the case where the parser is not able to figure out
            the line and column number -1 will be returned.
            Returns line where the problem was found.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeParseException.BeginColumn">
            <summary>
            For EndOfLine and EndOfFile ("&lt;EOF&gt;") parsing problems the last char in the
            string is returned. For the case where the parser is not able to figure out
            the line and column number -1 will be returned. 
            Returns column of the first char where the problem was found.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1">
            <summary>
            This class is a helper for the query parser framework, it does all the three
            query parser phrases at once: text parsing, query processing and query
            building.
            <para>
            It contains methods that allows the user to change the implementation used on
            the three phases.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler,Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser,Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor,Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder{`0})">
            <summary>
            Creates a query parser helper object using the specified configuration,
            text parser, processor and builder.
            </summary>
            <param name="queryConfigHandler">the query configuration handler that will be initially set to this helper</param>
            <param name="syntaxParser">the text parser that will be initially set to this helper</param>
            <param name="processor">the query processor that will be initially set to this helper</param>
            <param name="builder">the query builder that will be initially set to this helper</param>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryNodeProcessor">
            <summary>
            Gets the processor object used to process the query node tree, it
            returns <c>null</c> if no processor is used.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>.
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryNodeProcessor(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryNodeProcessor(Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor)">
            <summary>
            Sets the processor that will be used to process the query node tree. If
            there is any <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> returned by
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryConfigHandler"/>, it will be set on the processor. The
            argument can be <c>null</c>, which means that no processor will be
            used to process the query node tree.
            </summary>
            <param name="processor">the processor that will be used to process the query node tree,
            this argument can be <c>null</c></param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryNodeProcessor"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Processors.IQueryNodeProcessor"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetSyntaxParser(Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser)">
            <summary>
            Sets the text parser that will be used to parse the query string, it cannot
            be <c>null</c>.
            </summary>
            <param name="syntaxParser">the text parser that will be used to parse the query string</param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SyntaxParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryBuilder(Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder{`0})">
            <summary>
            The query builder that will be used to build an object from the query node
            tree. It cannot be <c>null</c>.
            </summary>
            <param name="queryBuilder">the query builder used to build something from the query node tree</param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryBuilder"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryConfigHandler">
            <summary>
            Returns the query configuration handler, which is used during the query
            node tree processing. It can be <c>null</c>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryBuilder">
            <summary>
            Returns the query builder used to build a object from the query node tree.
            The object produced by this builder is returned by <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.Parse(System.String,System.String)"/>.
            </summary>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryBuilder(Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder{`0})"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SyntaxParser">
            <summary>
            Returns the text parser used to build a query node tree from a query
            string. The default text parser instance returned by this method is a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetSyntaxParser(Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser)"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SetQueryConfigHandler(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            Sets the query configuration handler that will be used during query
            processing. It can be <c>null</c>. It's also set to the processor
            returned by <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryNodeProcessor"/>.
            </summary>
            <param name="config">the query configuration handler used during query processing, it
            can be <c>null</c></param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryConfigHandler"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.Parse(System.String,System.String)">
            <summary>
            Parses a query string to an object, usually some query object.
            <para/>
            In this method the three phases are executed:
            <para/>
            <list type="number">
                <item><description>
                the query string is parsed using the
                text parser returned by <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.SyntaxParser"/>, the result is a query
                node tree.
                </description></item>
                <item><description>
                the query node tree is processed by the
                processor returned by <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryNodeProcessor"/>.
                </description></item>
                <item><description>
                a object is built from the query node
                tree using the builder returned by <see cref="P:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.QueryBuilder"/>.
                </description></item>
            </list>
            </summary>
            <param name="query">the query string</param>
            <param name="defaultField">the default field used by the text parser</param>
            <returns>the object built from the query</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something wrong happens along the three phases</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Util.QueryNodeOperation">
            <summary>
            Allow joining 2 QueryNode Trees, into one.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.QueryNodeOperation.LogicalAnd(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode,Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            perform a logical and of 2 QueryNode trees. if q1 and q2 are ANDQueryNode
            nodes it uses head Node from q1 and adds the children of q2 to q1 if q1 is
            a AND node and q2 is not, add q2 as a child of the head node of q1 if q2 is
            a AND node and q1 is not, add q1 as a child of the head node of q2 if q1
            and q2 are not ANDQueryNode nodes, create a AND node and make q1 and q2
            children of that node if q1 or q2 is null it returns the not null node if
            q1 = q2 = null it returns null
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Util.StringUtils">
            <summary>
            String manipulation routines
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence">
            <summary>
            <see cref="T:J2N.Text.ICharSequence"/> with escaped chars information.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.#ctor(System.Char[],System.Boolean[],System.Int32,System.Int32)">
            <summary>
            Create a escaped <see cref="T:J2N.Text.ICharSequence"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.#ctor(J2N.Text.ICharSequence)">
            <summary>
            Create a non-escaped <see cref="T:J2N.Text.ICharSequence"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.#ctor(System.String)">
            <summary>
            Create a non-escaped <see cref="T:System.String"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.#ctor(System.Text.StringBuilder)">
            <summary>
            Create a non-escaped <see cref="T:System.Text.StringBuilder"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.ToStringEscaped">
            <summary>
            Return an escaped <see cref="T:System.String"/>
            </summary>
            <returns>an escaped <see cref="T:System.String"/></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Core.Util.UnescapedCharSequence.ToStringEscaped(System.Char[])">
            <summary>
            Return an escaped <see cref="T:System.String"/>
            </summary>
            <param name="enabledChars">array of chars to be escaped</param>
            <returns>an escaped <see cref="T:System.String"/></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Precedence.PrecedenceQueryParser">
            <summary>
            This query parser works exactly as the standard query parser ( <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/> ), 
            except that it respect the boolean precedence, so &lt;a AND b OR c AND d&gt; is parsed to &lt;(+a +b) (+c +d)&gt;
            instead of &lt;+a +b +c +d&gt;.
            <para>
            EXPERT: This class extends <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/>, but uses <see cref="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.PrecedenceQueryNodeProcessorPipeline"/>
            instead of <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/> to process the query tree.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Precedence.PrecedenceQueryParser.#ctor">
            <summary>
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.#ctor"/> 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Precedence.PrecedenceQueryParser.#ctor(Lucene.Net.Analysis.Analyzer)">
            <summary>
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.#ctor(Lucene.Net.Analysis.Analyzer)"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.BooleanModifiersQueryNodeProcessor">
            <summary>
            This processor is used to apply the correct <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> to <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>s children.
            <para>
            It walks through the query node tree looking for <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>s. If an <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode"/> is found,
            every child, which is not a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> or the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> 
            is <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier.MOD_NONE"/>, becomes a <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier.MOD_REQ"/>. For any other
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> which is not an <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.OrQueryNode"/>, it checks the default operator is <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler.Operator.AND"/>,
            if it is, the same operation when an <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode"/> is found is applied to it.
            </para>
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DEFAULT_OPERATOR"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.PrecedenceQueryNodeProcessorPipeline">
            <summary>
            This processor pipeline extends <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/> and enables
            boolean precedence on it.
            <para>
            EXPERT: the precedence is enabled by removing <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.GroupQueryNodeProcessor"/> from the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/> and appending <see cref="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.BooleanModifiersQueryNodeProcessor"/>
            to the pipeline.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Precedence.PrecedenceQueryParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.PrecedenceQueryNodeProcessorPipeline.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.AnyQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.BooleanQuery"/> of SHOULD clauses, possibly with
            some minimum number to match.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.BooleanQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.BooleanQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> object.
            Every children in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> object must be already tagged
            using <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/> with a <see cref="T:Lucene.Net.Search.Query"/>
            object.
            <para>
            It takes in consideration if the children is a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> to
            define the <see cref="T:Lucene.Net.Search.BooleanClause"/>.
            </para>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.BoostQueryNodeBuilder">
            <summary>
            This builder basically reads the <see cref="T:Lucene.Net.Search.Query"/> object set on the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode"/> child using
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/> and applies the boost value
            defined in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BoostQueryNode"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.DummyQueryNodeBuilder">
            <summary>
            This builder does nothing. Commonly used for <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> objects that
            are built by its parent's builder.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.IStandardQueryBuilder"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder`1"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Builders.DummyQueryNodeBuilder.#ctor">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.DummyQueryNodeBuilder"/> object.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Builders.DummyQueryNodeBuilder.Build(Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode)">
            <summary>
            Always return <c>null</c>.
            </summary>
            <param name="queryNode"></param>
            <returns><c>null</c></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.FieldQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.TermQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.FuzzyQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.FuzzyQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.GroupQueryNodeBuilder">
            <summary>
            Builds no object, it only returns the <see cref="T:Lucene.Net.Search.Query"/> object set on the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode"/> object using a
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/> tag.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.MatchAllDocsQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.MatchAllDocsQuery"/> object from a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchAllDocsQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.MatchNoDocsQueryNodeBuilder">
            <summary>
            Builds an empty <see cref="T:Lucene.Net.Search.BooleanQuery"/> object from a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.ModifierQueryNodeBuilder">
            <summary>
            Builds no object, it only returns the <see cref="T:Lucene.Net.Search.Query"/> object set on the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> object using a
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/> tag.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.MultiPhraseQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.MultiPhraseQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode"/>
            object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.NumericRangeQueryNodeBuilder">
            <summary>
            Builds <see cref="T:Lucene.Net.Search.NumericRangeQuery"/>s out of <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>s.
            </summary>
            <seealso cref="T:Lucene.Net.Search.NumericRangeQuery"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Builders.NumericRangeQueryNodeBuilder.#ctor">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.NumericRangeQueryNodeBuilder"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.PhraseQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.PhraseQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode"/>
            object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.PrefixWildcardQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.PrefixQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode"/>
            object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.RegexpQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.RegexpQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.SlopQueryNodeBuilder">
            <summary>
            This builder basically reads the <see cref="T:Lucene.Net.Search.Query"/> object set on the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> child using
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder.QUERY_TREE_BUILDER_TAGID"/> and applies the slop value
            defined in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardBooleanQueryNodeBuilder">
            <summary>
            This builder does the same as the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.BooleanQueryNodeBuilder"/>, but this
            considers if the built <see cref="T:Lucene.Net.Search.BooleanQuery"/> should have its coord disabled or
            not.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.BooleanQueryNodeBuilder"/>
            <seealso cref="T:Lucene.Net.Search.BooleanQuery"/>
            <seealso cref="M:Lucene.Net.Search.Similarities.Similarity.Coord(System.Int32,System.Int32)"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.IStandardQueryBuilder">
            <summary>
            This interface should be implemented by every class that wants to build
            <see cref="T:Lucene.Net.Search.Query"/> objects from <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> objects. 
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.IQueryBuilder`1"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardQueryTreeBuilder">
            <summary>
            This query tree builder only defines the necessary map to build a
            <see cref="T:Lucene.Net.Search.Query"/> tree object. It should be used to generate a <see cref="T:Lucene.Net.Search.Query"/> tree
            object from a query node tree processed by a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Builders.QueryTreeBuilder"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.TermRangeQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.TermRangeQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>
            object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.WildcardQueryNodeBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.WildcardQuery"/> object from a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/>
            object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration">
            <summary>
            Configuration options common across queryparser implementations.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.LowercaseExpandedTerms">
            <summary>
            Whether terms of multi-term queries (e.g., wildcard,
            prefix, fuzzy and range) should be automatically
            lower-cased or not.  Default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.AllowLeadingWildcard">
            <summary>
            Set to <c>true</c> to allow leading wildcard characters.
            <para/>
            When set, <c>*</c> or <c>?</c> are allowed as the first
            character of a <see cref="T:Lucene.Net.Search.PrefixQuery"/> and <see cref="T:Lucene.Net.Search.WildcardQuery"/>. Note that this can produce
            very slow queries on big indexes.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.EnablePositionIncrements">
            <summary>
            Set to <c>true</c> to enable position increments in result query.
            <para/>
            When set, result phrase and multi-phrase queries will be aware of position
            increments. Useful when e.g. a <see cref="T:Lucene.Net.Analysis.Core.StopFilter"/> increases the position increment
            of the token that follows an omitted token.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.MultiTermRewriteMethod">
            <summary>
            By default, it uses 
            <see cref="F:Lucene.Net.Search.MultiTermQuery.CONSTANT_SCORE_AUTO_REWRITE_DEFAULT"/> when creating a
            prefix, wildcard and range queries. This implementation is generally
            preferable because it a) Runs faster b) Does not have the scarcity of terms
            unduly influence score c) avoids any exception due to too many listeners.
            However, if your application really needs to use the
            old-fashioned boolean queries expansion rewriting and the above points are
            not relevant then use this change the rewrite method.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.FuzzyPrefixLength">
            <summary>
            Get or Set the prefix length for fuzzy queries. Default is 0.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.Locale">
            <summary>
            Get or Set locale used by date range parsing.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.TimeZone">
            <summary>
            Gets or Sets the time zone.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.PhraseSlop">
            <summary>
            Gets or Sets the default slop for phrases. If zero, then exact phrase matches are
            required. Default value is zero.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.FuzzyMinSim">
            <summary>
            Get the minimal similarity for fuzzy queries.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.ICommonQueryParserConfiguration.SetDateResolution(Lucene.Net.Documents.DateTools.Resolution)">
            <summary>
            Sets the default <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> used for certain field when
            no <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> is defined for this field.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.FieldBoostMapFCListener">
            <summary>
            This listener listens for every field configuration request and assign a
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.BOOST"/> to the
            equivalent <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> based on a defined map: fieldName -> boostValue stored in
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_BOOST_MAP"/>.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_BOOST_MAP"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.BOOST"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.FieldDateResolutionFCListener">
            <summary>
            This listener listens for every field configuration request and assign a
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DATE_RESOLUTION"/> to the equivalent <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> based
            on a defined map: fieldName -> <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> stored in
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_DATE_RESOLUTION_MAP"/>.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DATE_RESOLUTION"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_DATE_RESOLUTION_MAP"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.IFieldConfigListener"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.FuzzyConfig">
            <summary>
            Configuration parameters for <see cref="T:Lucene.Net.Search.FuzzyQuery"/>s
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.DateFormat">
            <summary>
            LUCENENET specific enum for mimicking the Java DateFormat
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat">
            <summary>
            This <see cref="T:Lucene.Net.Util.NumberFormat"/> parses <see cref="T:System.Int64"/> into date strings and vice-versa. It
            uses the given <c>dateFormat</c> and <see cref="T:System.Globalization.CultureInfo">locale</see> to parse and format dates, but before, it
            converts <see cref="T:System.Int64"/> to <see cref="T:System.DateTime"/> objects or vice-versa.
            <para/>
            Note that the <see cref="T:System.Int64"/> value the dates are parsed into and out of represent the number of milliseconds
            since January 1, 1970 0:00:00, also known as the "epoch".
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat.#ctor(System.String,System.Globalization.CultureInfo)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat"/> object using the given <paramref name="dateFormat"/>
            and <paramref name="locale"/>.
            </summary>
            <param name="dateFormat">Date format used to parse and format dates</param>
            <param name="locale"></param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Config.DateFormat,Lucene.Net.QueryParsers.Flexible.Standard.Config.DateFormat,System.Globalization.CultureInfo)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat"/> object using the given <paramref name="dateStyle"/>,
            <paramref name="timeStyle"/>, and <paramref name="culture"/>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumberDateFormat.GetDateFormat">
            <summary>
            Returns the .NET date format that will be used to Format the date.
            Note that parsing the date uses <see cref="M:System.DateTime.ParseExact(System.String,System.String,System.IFormatProvider)"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig">
            <summary>
            This class holds the configuration used to parse numeric queries and create
            <see cref="T:Lucene.Net.Search.NumericRangeQuery"/>s.
            </summary>
            <seealso cref="T:Lucene.Net.Search.NumericRangeQuery"/>
            <seealso cref="T:Lucene.Net.Util.NumberFormat"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.#ctor(System.Int32,Lucene.Net.Util.NumberFormat,Lucene.Net.Documents.NumericType)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> object.
            </summary>
            <param name="precisionStep">the precision used to index the numeric values</param>
            <param name="format">the <see cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.NumberFormat"/> used to parse a <see cref="T:System.String"/> to an <see cref="T:System.Object"/> representing a .NET numeric type.</param>
            <param name="type">the numeric type used to index the numeric values</param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.PrecisionStep"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.NumberFormat"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.Type"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.PrecisionStep">
            <summary>
            Gets or sets the precision used to index the numeric values
            </summary>
            <seealso cref="P:Lucene.Net.Search.NumericRangeQuery`1.PrecisionStep"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.NumberFormat">
            <summary>
            Gets or Sets the <seealso cref="T:Lucene.Net.Util.NumberFormat"/> used to parse a <see cref="T:System.String"/> to
            <see cref="T:System.Object"/> representing a .NET numeric type, cannot be <c>null</c>
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.Type">
            <summary>
            Gets or Sets the numeric type used to index the numeric values
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.GetHashCode">
            <summary>
            LUCENENET specific - Visual Studio provides a compiler warning if
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.Equals(System.Object)"/> is overridden without <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.GetHashCode"/>,
            so we provide an implementation that mirrors <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig.Equals(System.Object)"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericFieldConfigListener">
            <summary>
            This listener is used to listen to <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> requests in
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> and add <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/>
            based on the <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG_MAP"/> set in the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG_MAP"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericFieldConfigListener.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericFieldConfigListener"/> object using the given <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>.
            </summary>
            <param name="config">the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> it will listen too</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler">
            <summary>
            This query configuration handler is used for almost every processor defined
            in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/> processor pipeline. It holds
            configuration methods that reproduce the configuration methods that could be set on the old
            lucene 2.4 QueryParser class.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler.Operator">
            <summary>
            Boolean Operator: AND or OR
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys">
            <summary>
            Class holding keys for <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/> options.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ENABLE_POSITION_INCREMENTS">
            <summary>
            Key used to set whether position increments is enabled
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.EnablePositionIncrements"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOWERCASE_EXPANDED_TERMS">
            <summary>
            Key used to set whether expanded terms should be lower-cased
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.LowercaseExpandedTerms"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ALLOW_LEADING_WILDCARD">
            <summary>
            Key used to set whether leading wildcards are supported
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.AllowLeadingWildcard"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ANALYZER">
            <summary>
            Key used to set the <see cref="T:Lucene.Net.Analysis.Analyzer"/> used for terms found in the query
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.Analyzer"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DEFAULT_OPERATOR">
            <summary>
            Key used to set the default boolean operator
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DefaultOperator"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.PHRASE_SLOP">
            <summary>
            Key used to set the default phrase slop
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.PhraseSlop"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOCALE">
            <summary>
             Key used to set the <see cref="T:System.Globalization.CultureInfo">locale</see> used when parsing the query
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.Locale"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.MULTI_TERM_REWRITE_METHOD">
            <summary>
            Key used to set the <see cref="T:Lucene.Net.Search.MultiTermQuery.RewriteMethod"/> used when creating queries
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.MultiTermRewriteMethod"/> 
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.MULTI_FIELDS">
            <summary>
            Key used to set the fields a query should be expanded to when the field
            is <c>null</c>
            </summary>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetMultiFields(System.String[])"/>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.GetMultiFields"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_BOOST_MAP">
            <summary>
            Key used to set a field to boost map that is used to set the boost for each field
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FieldsBoost"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FIELD_DATE_RESOLUTION_MAP">
            <summary>
            Key used to set a field to <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> map that is used
            to normalize each date field value.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DateResolutionMap"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FUZZY_CONFIG">
            <summary>
            Key used to set the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.FuzzyConfig"/> used to create fuzzy queries.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FuzzyMinSim"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FuzzyPrefixLength"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DATE_RESOLUTION">
            <summary>
            Key used to set default <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/>.
            </summary>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetDateResolution(Lucene.Net.Documents.DateTools.Resolution)"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DateResolution"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.BOOST">
            <summary>
            Key used to set the boost value in <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> objects.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FieldsBoost"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG">
            <summary>
            Key used to set a field to its <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.NumericConfigMap"/>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG_MAP">
            <summary>
            Key used to set the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> in <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> for numeric fields.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.NumericConfigMap"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1">
            <summary>
            This class should be extended by nodes intending to represent range queries.
            </summary>
            <typeparam name="T">the type of the range query bounds (lower and upper)</typeparam>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.#ctor">
            <summary>
            Constructs an <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1"/>, it should be invoked only by
            its extenders.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.Field">
            <summary>
            Gets or Sets the field associated with this node.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.LowerBound">
            <summary>
            Gets the lower bound node.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.UpperBound">
            <summary>
            Gets the upper bound node.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.IsLowerInclusive">
            <summary>
            Gets whether the lower bound is inclusive or exclusive. 
            </summary>
            <remarks>
            <c>true</c> if the lower bound is inclusive, otherwise, <c>false</c>
            </remarks>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.IsUpperInclusive">
            <summary>
            Gets whether the upper bound is inclusive or exclusive.
            </summary>
            <remarks>
            <c>true</c> if the upper bound is inclusive, otherwise, <c>false</c>
            </remarks>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.SetBounds(`0,`0,System.Boolean,System.Boolean)">
            <summary>
            Sets the lower and upper bounds.
            </summary>
            <param name="lower">the lower bound, <c>null</c> if lower bound is open</param>
            <param name="upper">the upper bound, <c>null</c> if upper bound is open</param>
            <param name="lowerInclusive"><c>true</c> if the lower bound is inclusive, otherwise, <c>false</c></param>
            <param name="upperInclusive"><c>true</c> if the upper bound is inclusive, otherwise, <c>false</c></param>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.LowerBound"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.UpperBound"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.IsLowerInclusive"/>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1.IsUpperInclusive"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.IAbstractRangeQueryNode">
            <summary>
            LUCENENET specific interface used to identify
            an AbstractRangeQueryNode without referring to 
            its generic closing type
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.BooleanModifierNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.BooleanModifierNode"/> has the same behaviour as
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/>, it only indicates that this modifier was added by
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.GroupQueryNodeProcessor"/> and not by the user. 
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode"/> indicates that its children should be used to
            build a <see cref="T:Lucene.Net.Search.MultiPhraseQuery"/> instead of <see cref="T:Lucene.Net.Search.PhraseQuery"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode">
            <summary>
            This query node represents a field query that holds a numeric value. It is
            similar to <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>, however the <see cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.Value"/> returns an
            <see cref="T:System.Object"/> representing a .NET numeric type.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.#ctor(System.String,System.Object,Lucene.Net.Util.NumberFormat)">
            <summary>
            Creates a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode"/> object using the given field,
            <see cref="T:System.Object"/> (representing a .NET numeric type) value and <see cref="T:Lucene.Net.Util.NumberFormat"/> used to convert the value to
            <see cref="T:System.String"/>.
            </summary>
            <param name="field">the field associated with this query node</param>
            <param name="value">the value hold by this node</param>
            <param name="numberFormat">the <see cref="T:Lucene.Net.Util.NumberFormat"/> used to convert the value to <see cref="T:System.String"/></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.Field">
            <summary>
            Gets or Sets the field associated with this node.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.GetTermEscaped(Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax)">
            <summary>
            This method is used to get the value converted to <see cref="T:System.String"/> and
            escaped using the given <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax"/>.
            </summary>
            <param name="escaper">The <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax"/> used to escape the value <see cref="T:System.String"/></param>
            <returns>The value converted to <see cref="T:System.String"/> and escaped</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.NumberFormat">
            <summary>
            Gets or Sets the <see cref="T:Lucene.Net.Util.NumberFormat"/> used to convert the value to <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode.Value">
            <summary>
            Gets or Sets the numeric value as <see cref="T:System.Object"/> representing a .NET numeric type.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode">
            <summary>
            This query node represents a range query composed by <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode"/>
            bounds, which means the bound values are <see cref="T:System.Object"/>s representing a .NET numeric type.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,System.Boolean,System.Boolean,Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/> object using the given
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode"/> as its bounds and <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>.
            </summary>
            <param name="lower">the lower bound</param>
            <param name="upper">the upper bound</param>
            <param name="lowerInclusive"><c>true</c> if the lower bound is inclusive, otherwise, <c>false</c></param>
            <param name="upperInclusive"><c>true</c> if the upper bound is inclusive, otherwise, <c>false</c></param>
            <param name="numericConfig">the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> that represents associated with the upper and lower bounds</param>
            <seealso cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode.SetBounds(Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,System.Boolean,System.Boolean,Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig)"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode.SetBounds(Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode,System.Boolean,System.Boolean,Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig)">
            <summary>
            Sets the upper and lower bounds of this range query node and the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> associated with these bounds.
            </summary>
            <param name="lower">the lower bound</param>
            <param name="upper">the upper bound</param>
            <param name="lowerInclusive"><c>true</c> if the lower bound is inclusive, otherwise, <c>false</c></param>
            <param name="upperInclusive"><c>true</c> if the upper bound is inclusive, otherwise, <c>false</c></param>
            <param name="numericConfig">the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> that represents associated with the upper and lower bounds</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode.NumericConfig">
            <summary>
            Gets the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/> associated with the lower and upper bounds.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode"/> represents wildcardquery that matches abc*
            or *. This does not apply to phrases, this is a special case on the original
            lucene parser. TODO: refactor the code to remove this special case from the
            parser. and probably do it on a Processor
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value including the wildcard</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value including the wildcard</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value including the wildcard</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode"/> represents <see cref="T:Lucene.Net.Search.RegexpQuery"/> query Examples: /[a-z]|[0-9]/
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains a regular expression</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains a regular expression</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.RegexpQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains a regular expression</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.StandardBooleanQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.StandardBooleanQueryNode"/> has the same behavior as
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>. It only indicates if the coord should be enabled or
            not for this boolean query. 
            </summary>
            <seealso cref="M:Lucene.Net.Search.Similarities.Similarity.Coord(System.Int32,System.Int32)"/>
            <seealso cref="T:Lucene.Net.Search.BooleanQuery"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode">
            <summary>
            This query node represents a range query composed by <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>
            bounds, which means the bound values are strings.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.AbstractRangeQueryNode`1"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode.#ctor(Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode,Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode,System.Boolean,System.Boolean)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/> object using the given
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> as its bounds.
            </summary>
            <param name="lower">the lower bound</param>
            <param name="upper">the upper bound</param>
            <param name="lowerInclusive"><c>true</c> if the lower bound is inclusive, otherwise, <c>false</c></param>
            <param name="upperInclusive"><c>true</c> if the upper bound is inclusive, otherwise, <c>false</c></param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode">
            <summary>
            A <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/> represents wildcard query This does not apply to
            phrases. Examples: a*b*c Fl?w? m?ke*g
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains one or more wild card characters (? or *)</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode.#ctor(System.String,System.Text.StringBuilder,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains one or more wild card characters (? or *)</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode.#ctor(System.String,J2N.Text.ICharSequence,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="field">field name</param>
            <param name="text">value that contains one or more wild card characters (? or *)</param>
            <param name="begin">position in the query string</param>
            <param name="end">position in the query string</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream">
            <summary>
            This interface describes a character stream that maintains line and
            column number positions of the characters.  It also has the capability
            to backup the stream to some extent.  An implementation of this
            interface is used in the TokenManager implementation generated by
            JavaCCParser.
            
            All the methods except <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BackUp(System.Int32)"/> can be implemented in any fashion. <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BackUp(System.Int32)"/>
            needs to be implemented correctly for the correct operation of the lexer.
            Rest of the methods are all used to get information like line number,
            column number and the string that constitutes a token and are not used
            by the lexer. Hence their implementation won't affect the generated lexer's
            operation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.ReadChar">
            <summary>
            Returns the next character from the selected input.  The method
            of selecting the input is the responsibility of the class
            implementing this interface. Can throw any <see cref="T:System.IO.IOException"/>.
            </summary>
            <returns>the next character from the selected input.</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.Column">
            <summary>
            Gets the column position of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.EndColumn"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.Line">
            <summary>
            Gets the line number of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.EndLine"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.EndColumn">
            <summary>
            Returns the column number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.EndLine">
            <summary>
            Returns the line number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BeginColumn">
            <summary>
            Returns the column number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BeginLine">
            <summary>
            Returns the line number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BackUp(System.Int32)">
            <summary>
            Backs up the input stream by amount steps. Lexer calls this method if it
            had already read some characters, but could not use them to match a
            (longer) token. So, they will be used again as the prefix of the next
            token and it is the implemetation's responsibility to do this right.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BeginToken">
            <summary>
            Returns the next character that marks the beginning of the next token.
            All characters must remain in the buffer between two successive calls
            to this method to implement <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.BackUp(System.Int32)"/> correctly.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.GetImage">
            <summary>
            Returns a string made up of characters from the marked token beginning
            to the current buffer position. Implementations have the choice of returning
            anything that they want to. For example, for efficiency, one might decide
            to just return null, which is a valid implementation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.GetSuffix(System.Int32)">
            <summary>
            Returns an array of characters that make up the suffix of length 'len' for
            the currently matched token. This is used to build up the matched string
            for use in actions in the case of MORE. A simple and inefficient
            implementation of this is as follows :
            <code>
            {
                string t = GetImage();
                return t.Substring(t.Length - len, len).ToCharArray();
            }
            </code>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream.Done">
            <summary>
            The lexer calls this function to indicate that it is done with the stream
            and hence implementations can free any resources held by this class.
            Again, the body of this function can be just empty and it will not
            affect the lexer's operation.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.EscapeQuerySyntax">
            <summary>
            Implementation of <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.IEscapeQuerySyntax"/> for the standard lucene
            syntax.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.EscapeQuerySyntax.ReplaceIgnoreCase(J2N.Text.ICharSequence,System.String,System.String,System.Globalization.CultureInfo)">
            <summary>
            replace with ignore case
            </summary>
            <param name="string">string to get replaced</param>
            <param name="sequence1">the old character sequence in lowercase</param>
            <param name="escapeChar">the new character to prefix sequence1 in return string.</param>
            <param name="locale"></param>
            <returns>the new <see cref="T:J2N.Text.ICharSequence"/></returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="string"/>, <paramref name="sequence1"/>, or <paramref name="escapeChar"/> is <c>null</c>.</exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.EscapeQuerySyntax.EscapeWhiteChar(J2N.Text.ICharSequence,System.Globalization.CultureInfo)">
            <summary>
            escape all tokens that are part of the parser syntax on a given string
            </summary>
            <param name="str">string to get replaced</param>
            <param name="locale">locale to be used when performing string compares</param>
            <returns>the new <see cref="T:J2N.Text.ICharSequence"/></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.EscapeQuerySyntax.DiscardEscapeChar(System.String)">
            <summary>
            Returns a string where the escape char has been removed, or kept only once
            if there was a double escape.
            <para/>
            Supports escaped unicode characters, e. g. translates <c>A</c> to
            <c>A</c>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.EscapeQuerySyntax.HexToInt32(System.Char)">
            <summary>
            Returns the numeric value of the hexadecimal character
            <para/>
            NOTE: This was hexToInt() in Lucene
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.FastCharStream">
            <summary>
            An efficient implementation of JavaCC's <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream"/> interface.  
            <para/>
            Note that
            this does not do line-number counting, but instead keeps track of the
            character position of the token in the input, as required by Lucene's
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token"/> API. 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.FastCharStream.#ctor(System.IO.TextReader)">
            <summary>
            Constructs from a <see cref="T:System.IO.TextReader"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException">
            <summary>
            This exception is thrown when parse errors are encountered.
            You can explicitly create objects of this exception type by
            calling the method generateParseException in the generated
            parser.
            
            You can modify this class to customize your error reporting
            mechanisms so long as you retain the public fields.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token,System.Int32[][],System.String[])">
            <summary>
            This constructor is used by the method "GenerateParseException"
            in the generated parser.  Calling this constructor generates
            a new object of this type with the fields <see cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.CurrentToken"/>,
            <see cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.ExpectedTokenSequences"/>, and <see cref="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.TokenImage"/> set.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.#ctor">
            <summary>
            The following constructors are for use by you for whatever
            purpose you can think of.  Constructing the exception in this
            manner makes the exception behave in the normal way - i.e., as
            documented in the class "Throwable".  The fields "errorToken",
            "expectedTokenSequences", and "tokenImage" do not contain
            relevant information.  The JavaCC generated code does not use
            these constructors.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.#ctor(System.String)">
            <summary>
            Constructor with message.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.#ctor(System.String,System.Exception)">
            <summary>
            Constructor with message and inner exception.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.CurrentToken">
            <summary>
            This is the last token that has been consumed successfully.  If
            this object has been created due to a parse error, the token
            followng this token will (therefore) be the first error token.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.ExpectedTokenSequences">
            <summary>
            Each entry in this array is an array of integers.  Each array
            of integers represents a sequence of tokens (by their ordinal
            values) that is expected at this point of the parse.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.TokenImage">
            <summary>
            This is a reference to the "tokenImage" array of the generated
            parser within which the parse error occurred.  This array is
            defined in the generated ...Constants interface.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.Initialize(Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token,System.Collections.Generic.IList{System.Int32[]},System.String[])">
            <summary>
            It uses <paramref name="currentToken"/> and <paramref name="expectedTokenSequences"/> to generate a parse
            error message and returns it.  If this object has been created
            due to a parse error, and you do not catch it (it gets thrown
            from the parser) the correct error message
            gets displayed.
            </summary>
            <param name="currentToken"></param>
            <param name="expectedTokenSequences"></param>
            <param name="tokenImage"></param>
            <returns></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException.AddEscapes(System.String)">
            <summary>
            Used to convert raw characters to their escaped version
            when these raw version cannot be used as part of an ASCII
            string literal.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser">
            <summary>
            Parser for the standard Lucene syntax
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.Parse(System.String,System.String)">
            <summary>
            Parses a query string, returning a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>.
            </summary>
            <param name="query">the query string to be parsed.</param>
            <param name="field"></param>
            <returns></returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ParseException">if the parsing fails</exception>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.TokenSource">
            <summary>Generated Token Manager.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.Token">
            <summary>Current token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.Jj_nt">
            <summary>Next token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream)">
            <summary>
            Constructor with user supplied <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream"/>.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.ReInit(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream)">
            <summary>Reinitialize.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager)">
            <summary>Constructor with generated Token Manager.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.ReInit(Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager)">
            <summary>Reinitialize.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.LookaheadSuccess.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.GetNextToken">
            <summary>Get the next Token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.GetToken(System.Int32)">
            <summary>Get the specific Token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.GenerateParseException">
            <summary>Generate ParseException.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.Enable_tracing">
            <summary>Enable tracing.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser.Disable_tracing">
            <summary>Disable tracing.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.EOF">
            <summary>End of File. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.NUM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.ESCAPED_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.TERM_START_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.TERM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.WHITESPACE">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.QUOTED_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.AND">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.NOT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.PLUS">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.MINUS">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.LPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_COLON">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_EQUAL">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_LESSTHAN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_LESSTHANEQ">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_MORETHAN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.OP_MORETHANEQ">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.CARAT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.QUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.TERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.FUZZY_SLOP">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.REGEXPTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGEIN_START">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGEEX_START">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.NUMBER">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGE_TO">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGEIN_END">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGEEX_END">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGE_QUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.RegexpToken.RANGE_GOOP">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.LexicalToken.Boost">
            <summary>Lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.LexicalToken.Range">
            <summary>Lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.LexicalToken.DEFAULT">
            <summary>Lexical state.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserConstants">
            <summary>
            Token literal values and constants.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserConstants.TokenImage">
            <summary>Literal token values.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager">
            <summary>
            Token Manager.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.debugStream">
            <summary>Debug output.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.SetDebugStream(System.IO.TextWriter)">
            <summary>Set debug output.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.jjstrLiteralImages">
            <summary>Token literal values.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.lexStateNames">
            <summary>Lexer state names.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.jjnewLexState">
            <summary>Lex State array.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream)">
            <summary>Constructor.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.#ctor(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream,System.Int32)">
            <summary>Constructor.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.ReInit(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream)">
            <summary>Reinitialize parser.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.ReInit(Lucene.Net.QueryParsers.Flexible.Standard.Parser.ICharStream,System.Int32)">
            <summary>Reinitialize parser.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.SwitchTo(System.Int32)">
            <summary>Switch to specified lex state.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParserTokenManager.GetNextToken">
            <summary>Get the next Token.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token">
            <summary>
            Describes the input token stream.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.Kind">
            <summary>
            An integer that describes the kind of this token.  This numbering
            system is determined by JavaCCParser, and a table of these numbers is
            stored in the file ...Constants.java.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.BeginLine">
            <summary>The line number of the first character of this Token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.BeginColumn">
            <summary>The column number of the first character of this Token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.EndLine">
            <summary>The line number of the last character of this Token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.EndColumn">
            <summary>The column number of the last character of this Token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.Image">
            <summary>
            The string image of the token.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.Next">
            <summary>
            A reference to the next regular (non-special) token from the input
            stream.  If this is the last token from the input stream, or if the
            token manager has not read tokens beyond this one, this field is
            set to null.  This is true only if this token is also a regular
            token.  Otherwise, see below for a description of the contents of
            this field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.SpecialToken">
            <summary>
            This field is used to access special tokens that occur prior to this
            token, but after the immediately preceding regular (non-special) token.
            If there are no such special tokens, this field is set to null.
            When there are more than one such special token, this field refers
            to the last of these special tokens, which in turn refers to the next
            previous special token through its specialToken field, and so on
            until the first special token (whose specialToken field is null).
            The next fields of special tokens refer to other special tokens that
            immediately follow it (without an intervening regular token).  If there
            is no such token, this field is null.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.Value">
            <summary>
            An optional attribute value of the Token.
            Tokens which are not used as syntactic sugar will often contain
            meaningful values that will be used later on by the compiler or
            interpreter. This attribute value is often different from the image.
            Any subclass of Token that actually wants to return a non-null value can
            override this method as appropriate.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.#ctor">
            <summary>
            No-argument constructor
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.#ctor(System.Int32)">
            <summary>
            Constructs a new token for the specified Image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.#ctor(System.Int32,System.String)">
            <summary>
            Constructs a new token for the specified Image and Kind.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.ToString">
            <summary>
            Returns the image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token.NewToken(System.Int32,System.String)">
            <summary>
            Returns a new <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token"/> object, by default. However, if you want, you
            can create and return subclass objects based on the value of <paramref name="ofKind"/>.
            Simply add the cases to the switch for all those special cases.
            For example, if you have a subclass of <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.Token"/> called IDToken that
            you want to create if <paramref name="ofKind"/> is ID, simply add something like :
            <code>
                case MyParserConstants.ID : return new IDToken(ofKind, image);
            </code>
            to the following switch statement. Then you can cast matchedToken
            variable to the appropriate type and use sit in your lexical actions.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError">
            <summary>
            Token Manager Error.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.LEXICAL_ERROR">
            <summary>
            Lexical error occurred.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.STATIC_LEXER_ERROR">
            <summary>
            An attempt was made to create a second instance of a static token manager.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.INVALID_LEXICAL_STATE">
            <summary>
            Tried to change to an invalid lexical state.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.LOOP_DETECTED">
            <summary>
            Detected (and bailed out of) an infinite loop in the token manager.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.errorCode">
            <summary>
            Indicates the reason why the exception is thrown. It will have
            one of the above 4 values.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.AddEscapes(System.String)">
            <summary>
            Replaces unprintable characters by their escaped (or unicode escaped)
            equivalents in the given string
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.LexicalError(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char)">
            <summary>
            Returns a detailed message for the Error when it is thrown by the
            token manager to indicate a lexical error.
            </summary>
            <param name="eofSeen">indicates if EOF caused the lexical error</param>
            <param name="lexState">lexical state in which this error occurred</param>
            <param name="errorLine">line number when the error occurred</param>
            <param name="errorColumn">column number when the error occurred</param>
            <param name="errorAfter">prefix that was seen before this error occurred</param>
            <param name="curChar">the offending character</param>
            <remarks>Note: You can customize the lexical error message by modifying this method.</remarks>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.Message">
            <summary>
            You can also modify the body of this method to customize your error messages.
            For example, cases like LOOP_DETECTED and INVALID_LEXICAL_STATE are not
            of end-users concern, so you can return something like :
            
                "Internal Error : Please file a bug report .... "
                
            from this method for such cases in the release version of your parser.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.#ctor">
            <summary>No arg constructor.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.#ctor(System.String,System.Int32)">
            <summary>Constructor with message and reason.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.#ctor(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char,System.Int32)">
            <summary>Full Constructor.</summary>
            <param name="eofSeen">indicates if EOF caused the lexical error</param>
            <param name="lexState">lexical state in which this error occurred</param>
            <param name="errorLine">line number when the error occurred</param>
            <param name="errorColumn">column number when the error occurred</param>
            <param name="errorAfter">prefix that was seen before this error occurred</param>
            <param name="curChar">the offending character</param>
            <param name="reason"></param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Parser.TokenMgrError.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.AllowLeadingWildcardProcessor">
            <summary>
            This processor verifies if
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ALLOW_LEADING_WILDCARD"/> is defined in the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>. If it is and leading wildcard is not allowed, it
            looks for every <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/> contained in the query node tree
            and throws an exception if any of them has a leading wildcard ('*' or '?').
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ALLOW_LEADING_WILDCARD"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.AnalyzerQueryNodeProcessor">
            <summary>
            This processor verifies if <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ANALYZER"/>
            is defined in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>. If it is and the analyzer is
            not <c>null</c>, it looks for every <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> that is not
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/>, <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/> or
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IRangeQueryNode"/> contained in the query node tree, then it applies
            the analyzer to that <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> object.
            <para/>
            If the analyzer return only one term, the returned term is set to the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> and it's returned.
            <para/>
            If the analyzer return more than one term, a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode"/>
            or <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode"/> is created, whether there is one or more
            terms at the same position, and it's returned.
            <para/>
            If no term is returned by the analyzer a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.NoTokenFoundQueryNode"/> object
            is returned.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.ANALYZER"/>
            <seealso cref="T:Lucene.Net.Analysis.Analyzer"/>
            <seealso cref="T:Lucene.Net.Analysis.TokenStream"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.BooleanQuery2ModifierNodeProcessor">
            <summary>
            This processor is used to apply the correct <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>s children. This is a variant of
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.BooleanModifiersQueryNodeProcessor"/> which ignores precedence.
            <para/>
            The <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/> knows the rules of precedence, but lucene
            does not. e.g. <code>(A AND B OR C AND D)</code> ist treated like
            <code>(+A +B +C +D)</code>.
            <para/>
            This processor walks through the query node tree looking for
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>s. If an <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode"/> is found, every child,
            which is not a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> or the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> is
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier.MOD_NONE"/>, becomes a <see cref="F:Lucene.Net.QueryParsers.Flexible.Core.Nodes.Modifier.MOD_REQ"/>. For default
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>, it checks the default operator is
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler.Operator.AND"/>, if it is, the same operation when an
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.AndQueryNode"/> is found is applied to it. Each <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/>
            which direct parent is also a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> is removed (to ignore
            the rules of precedence).
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DEFAULT_OPERATOR"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Precedence.Processors.BooleanModifiersQueryNodeProcessor"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.BooleanSingleChildOptimizationQueryNodeProcessor">
            <summary>
            This processor removes every <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> that contains only one
            child and returns this child. If this child is <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/> that
            was defined by the user. A modifier is not defined by the user when it's a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.BooleanModifierNode"/>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.BoostQueryNodeProcessor">
            <summary>
            This processor iterates the query node tree looking for every
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> that has <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.BOOST"/> in its
            config. If there is, the boost is applied to that <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/>.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.BOOST"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.DefaultPhraseSlopQueryNodeProcessor">
            <summary>
            This processor verifies if <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.PHRASE_SLOP"/>
            is defined in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>. If it is, it looks for every
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode"/> and <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode"/> that does
            not have any <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> applied to it and creates an
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> and apply to it. The new <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> has the
            same slop value defined in the configuration.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.PHRASE_SLOP"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.FuzzyQueryNodeProcessor">
            <summary>
            This processor iterates the query node tree looking for every
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/>, when this kind of node is found, it checks on the
            query configuration for
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FUZZY_CONFIG"/>, gets the
            fuzzy prefix length and default similarity from it and set to the fuzzy node.
            For more information about fuzzy prefix length check: <see cref="T:Lucene.Net.Search.FuzzyQuery"/>.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.FUZZY_CONFIG"/>
            <seealso cref="T:Lucene.Net.Search.FuzzyQuery"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.GroupQueryNodeProcessor">
            <summary>
            The <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Parser.ISyntaxParser"/>
            generates query node trees that consider the boolean operator precedence, but
            Lucene current syntax does not support boolean precedence, so this processor
            remove all the precedence and apply the equivalent modifier according to the
            boolean operation defined on an specific query node.
            <para/>
            If there is a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode"/> in the query node tree, the query node
            tree is not merged with the one above it.
            <para/>
            Example: TODO: describe a good example to show how this processor works
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.LowercaseExpandedTermsQueryNodeProcessor">
            <summary>
            This processor verifies if 
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOWERCASE_EXPANDED_TERMS"/> is defined in the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>. If it is and the expanded terms should be
            lower-cased, it looks for every <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/>,
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FuzzyQueryNode"/> and children of a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IRangeQueryNode"/> and lower-case its
            term.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOWERCASE_EXPANDED_TERMS"/>.
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.MatchAllDocsQueryNodeProcessor">
            <summary>
            This processor converts every <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.WildcardQueryNode"/> that is "*:*" to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchAllDocsQueryNode"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchAllDocsQueryNode"/>
            <seealso cref="T:Lucene.Net.Search.MatchAllDocsQuery"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.MultiFieldQueryNodeProcessor">
            <summary>
            This processor is used to expand terms so the query looks for the same term
            in different fields. It also boosts a query based on its field.
            <para/>
            This processor looks for every <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> contained in the query
            node tree. If a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> is found, it checks if there is a
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.MULTI_FIELDS"/> defined in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/>. If
            there is, the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IFieldableNode"/> is cloned N times and the clones are
            added to a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> together with the original node. N is
            defined by the number of fields that it will be expanded to. The
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.BooleanQueryNode"/> is returned.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.MULTI_FIELDS"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.MultiTermRewriteMethodProcessor">
            <summary>
            This processor instates the default
            <see cref="T:Lucene.Net.Search.MultiTermQuery.RewriteMethod"/>,
            <see cref="F:Lucene.Net.Search.MultiTermQuery.CONSTANT_SCORE_AUTO_REWRITE_DEFAULT"/>, for multi-term
            query nodes.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.NumericQueryNodeProcessor">
            <summary>
            This processor is used to convert <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>s to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>s. It looks for
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/> set in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> of
            every <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> found. If
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/> is found, it considers that
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> to be a numeric query and convert it to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/> with upper and lower inclusive and lower and
            upper equals to the value represented by the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/> converted
            to <see cref="T:System.Object"/> representing a .NET numeric type. It means that <b>field:1</b> is converted to <b>field:[1
            TO 1]</b>.
            <para/>
            Note that <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>s children of a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IRangeQueryNode"/> are ignored.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.FieldQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Processors.NumericQueryNodeProcessor.#ctor">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.NumericQueryNodeProcessor"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.NumericRangeQueryNodeProcessor">
            <summary>
            This processor is used to convert <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>s to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>s. It looks for
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/> set in the <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.FieldConfig"/> of
            every <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/> found. If
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/> is found, it considers that
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/> to be a numeric range query and convert it to
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.NUMERIC_CONFIG"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.NumericConfig"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.Processors.NumericRangeQueryNodeProcessor.#ctor">
            <summary>
            Constructs an empty <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.NumericRangeQueryNode"/> object.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.OpenRangeQueryNodeProcessor">
            <summary>
            Processes <see cref="T:Lucene.Net.Search.TermRangeQuery"/>s with open ranges.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.PhraseSlopQueryNodeProcessor">
            <summary>
            This processor removes invalid <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> objects in the query
            node tree. A <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/> is invalid if its child is neither a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.TokenizedPhraseQueryNode"/> nor a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.MultiPhraseQueryNode"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.SlopQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.RemoveEmptyNonLeafQueryNodeProcessor">
            <summary>
            This processor removes every <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/> that is not a leaf and has not
            children. If after processing the entire tree the root node is not a leaf and
            has no children, a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode"/> object is returned.
            <para/>
            This processor is used at the end of a pipeline to avoid invalid query node
            tree structures like a <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.GroupQueryNode"/> or <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.ModifierQueryNode"/>
            with no children.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.IQueryNode"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Core.Nodes.MatchNoDocsQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline">
            <summary>
            This pipeline has all the processors needed to process a query node tree,
            generated by <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/>, already assembled.
            <para/>
            The order they are assembled affects the results.
            <para/>
            This processor pipeline was designed to work with
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler"/>.
            <para/>
            The result query node tree can be used to build a <see cref="T:Lucene.Net.Search.Query"/> object using
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardQueryTreeBuilder"/>.
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardQueryTreeBuilder"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.TermRangeQueryNodeProcessor">
            <summary>
            This processors process <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>s. It reads the lower and
            upper bounds value from the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/> object and try
            to parse their values using a <c>dateFormat</c>. If the values cannot be
            parsed to a date value, it will only create the <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>
            using the non-parsed values.
            <para/>
            If a <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOCALE"/> is defined in the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Core.Config.QueryConfigHandler"/> it will be used to parse the date, otherwise
            <see cref="P:System.Globalization.CultureInfo.CurrentCulture"/> will be used.
            <para/>
            If a <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DATE_RESOLUTION"/> is defined and the
            <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> is not <c>null</c> it will also be used to parse the
            date value.
            </summary>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.DATE_RESOLUTION"/>
            <seealso cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.ConfigurationKeys.LOCALE"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.TermRangeQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.WildcardQueryNodeProcessor">
            <summary>
            The <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/> creates <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode"/> nodes which
            have values containing the prefixed wildcard. However, Lucene
            <see cref="T:Lucene.Net.Search.PrefixQuery"/> cannot contain the prefixed wildcard. So, this processor
            basically removed the prefixed wildcard from the
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode"/> value.
            </summary>
            <seealso cref="T:Lucene.Net.Search.PrefixQuery"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Nodes.PrefixWildcardQueryNode"/>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.QueryParserUtil">
            <summary>
            This class defines utility methods to (help) parse query strings into
            <see cref="T:Lucene.Net.Search.Query"/> objects.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.QueryParserUtil.Parse(System.String[],System.String[],Lucene.Net.Analysis.Analyzer)">
            <summary>
            Parses a query which searches on the fields specified.
            <para/>
            If x fields are specified, this effectively constructs:
            <code>
            (field1:query1) (field2:query2) (field3:query3)...(fieldx:queryx)
            </code>
            </summary>
            <param name="queries">Queries strings to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:System.ArgumentException">
            if the length of the queries array differs from the length of the
            fields array
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="queries"/> or <paramref name="fields"/> is <c>null</c>
            </exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.QueryParserUtil.Parse(System.String,System.String[],Lucene.Net.Search.Occur[],Lucene.Net.Analysis.Analyzer)">
            <summary>
            Parses a query, searching on the fields specified. Use this if you need to
            specify certain fields as required, and others as prohibited.
            <para/>
            Usage:
            <code>
            string[] fields = {&quot;filename&quot;, &quot;contents&quot;, &quot;description&quot;};
            Occur[] flags = {Occur.SHOULD,
                Occur.MUST,
                Occur.MUST_NOT};
            MultiFieldQueryParser.Parse(&quot;query&quot;, fields, flags, analyzer);
            </code>
            <para/>
            The code above would construct a query:
            <code>
            (filename:query) +(contents:query) -(description:query)
            </code>
            </summary>
            <param name="query">Query string to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="flags">Flags describing the fields</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:System.ArgumentException">
            if the length of the fields array differs from the length of the
            flags array
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="fields"/> or <paramref name="flags"/> is <c>null</c>
            </exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.QueryParserUtil.Parse(System.String[],System.String[],Lucene.Net.Search.Occur[],Lucene.Net.Analysis.Analyzer)">
            <summary>
            Parses a query, searching on the fields specified. Use this if you need to
            specify certain fields as required, and others as prohibited.
            <para/>
            Usage:
            <code>
            string[] query = {&quot;query1&quot;, &quot;query2&quot;, &quot;query3&quot;};
            string[] fields = {&quot;filename&quot;, &quot;contents&quot;, &quot;description&quot;};
            Occur[] flags = {Occur.SHOULD,
                Occur.MUST,
                Occur.MUST_NOT};
            MultiFieldQueryParser.Parse(query, fields, flags, analyzer);
            </code>
            <para/>
            The code above would construct a query:
            <code>
            (filename:query1) +(contents:query2) -(description:query3)
            </code>
            </summary>
            <param name="queries">Queries string to parse</param>
            <param name="fields">Fields to search on</param>
            <param name="flags">Flags describing the fields</param>
            <param name="analyzer">Analyzer to use</param>
            <exception cref="T:System.ArgumentException">
            if the length of the queries, fields, and flags array differ
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="queries"/>, <paramref name="fields"/> or <paramref name="flags"/> is <c>null</c>
            </exception>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.QueryParserUtil.Escape(System.String)">
            <summary>
            Returns a string where those characters that TextParser expects to be
            escaped are escaped by a preceding <c>\</c>.
            </summary>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="s"/> is <c>null</c>
            </exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser">
            <summary>
            This class is a helper that enables users to easily use the Lucene query
            parser.
            <para/>
            To construct a Query object from a query string, use the
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.Parse(System.String,System.String)"/> method:
            <code>
            StandardQueryParser queryParserHelper = new StandardQueryParser();
            Query query = queryParserHelper.Parse("a AND b", "defaultField");
            </code>
            <para/>
            To change any configuration before parsing the query string do, for example:
            <code>
            // the query config handler returned by StandardQueryParser is a
            // StandardQueryConfigHandler
            queryParserHelper.QueryConfigHandler.Analyzer = new WhitespaceAnalyzer();
            </code>
            <para/>
            The syntax for query strings is as follows (copied from the old QueryParser
            javadoc):
            <para/>
            A Query is a series of clauses. A clause may be prefixed by:
            <list type="bullet">
                <item><description>
                a plus (<c>+</c>) or a minus (<c>-</c>) sign, indicating that
                the clause is required or prohibited respectively; or
                </description></item>
                <item><description>
                a term followed by a colon, indicating the field to be searched. This
                enables one to construct queries which search multiple fields.
                </description></item>
            </list>
            
            A clause may be either:
            <list type="bullet">
                <item><description>
                a term, indicating all the documents that contain this term; or
                </description></item>
                <item><description>
                a nested query, enclosed in parentheses. Note that this may be used with
                a <c>+</c>/<c>-</c> prefix to require any of a set of terms.
                </description></item>
            </list>
            
            Thus, in BNF, the query grammar is:
            <code>
                Query  ::= ( Clause )*
                Clause ::= [&quot;+&quot;, &quot;-&quot;] [&lt;TERM&gt; &quot;:&quot;] ( &lt;TERM&gt; | &quot;(&quot; Query &quot;)&quot; )
            </code>
            
            <para>
            Examples of appropriately formatted queries can be found in the query syntax documentation.
            </para>
            <para>
            The text parser used by this helper is a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/>.
            </para>
            <para>
            The query node processor used by this helper is a
            <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>.
            </para>
            <para>
            The builder used by this helper is a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardQueryTreeBuilder"/>.
            </para>
            </summary>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Parser.StandardSyntaxParser"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Processors.StandardQueryNodeProcessorPipeline"/>
            <seealso cref="T:Lucene.Net.QueryParsers.Flexible.Standard.Builders.StandardQueryTreeBuilder"/>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.#ctor">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/> object.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.#ctor(Lucene.Net.Analysis.Analyzer)">
            <summary>
            Constructs a <see cref="T:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser"/> object and sets an
            <see cref="T:Lucene.Net.Analysis.Analyzer"/> to it. The same as:
            <code>
            StandardQueryParser qp = new StandardQueryParser();
            qp.QueryConfigHandler.Analyzer = analyzer;
            </code>
            </summary>
            <param name="analyzer">the analyzer to be used by this query parser helper</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.Parse(System.String,System.String)">
            <summary>
            Overrides <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.Parse(System.String,System.String)"/> so it casts the
            return object to <see cref="T:Lucene.Net.Search.Query"/>. For more reference about this method, check
            <see cref="M:Lucene.Net.QueryParsers.Flexible.Core.QueryParserHelper`1.Parse(System.String,System.String)"/>.
            </summary>
            <param name="query">the query string</param>
            <param name="defaultField">the default field used by the text parser</param>
            <returns>the object built from the query</returns>
            <exception cref="T:Lucene.Net.QueryParsers.Flexible.Core.QueryNodeException">if something wrong happens along the three phases</exception>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DefaultOperator">
            <summary>
            Gets or Sets the boolean operator of the QueryParser. In default mode (
            <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler.Operator.OR"/>) terms without any modifiers are considered optional:
            for example <c>capital of Hungary</c> is equal to
            <c>capital OR of OR Hungary</c>.
            <para/>
            In <see cref="F:Lucene.Net.QueryParsers.Flexible.Standard.Config.StandardQueryConfigHandler.Operator.AND"/> mode terms are considered to be in conjunction: the
            above mentioned query is parsed as <c>capital AND of AND Hungary</c>
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.LowercaseExpandedTerms">
            <summary>
            Set to <c>true</c> to allow leading wildcard characters.
            <para/>
            When set, <c>*</c> or <c>?</c> are allowed as the first
            character of a <see cref="T:Lucene.Net.Search.PrefixQuery"/> and <see cref="T:Lucene.Net.Search.WildcardQuery"/>. Note that this can produce
            very slow queries on big indexes.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.AllowLeadingWildcard">
            <summary>
            Set to <c>true</c> to allow leading wildcard characters.
            <para/>
            When set, <c>*</c> or <c>?</c> are allowed as the first
            character of a <see cref="T:Lucene.Net.Search.PrefixQuery"/> and <see cref="T:Lucene.Net.Search.WildcardQuery"/>. Note that this can produce
            very slow queries on big indexes.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.EnablePositionIncrements">
            <summary>
            Set to <c>true</c> to enable position increments in result query.
            <para/>
            When set, result phrase and multi-phrase queries will be aware of position
            increments. Useful when e.g. a <see cref="T:Lucene.Net.Analysis.Core.StopFilter"/> increases the position increment
            of the token that follows an omitted token.
            <para/>
            Default: false.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.MultiTermRewriteMethod">
            <summary>
            By default, it uses 
            <see cref="F:Lucene.Net.Search.MultiTermQuery.CONSTANT_SCORE_AUTO_REWRITE_DEFAULT"/> when creating a
            prefix, wildcard and range queries. This implementation is generally
            preferable because it a) Runs faster b) Does not have the scarcity of terms
            unduly influence score c) avoids any Exception due to too many listeners.
            However, if your application really needs to use the
            old-fashioned boolean queries expansion rewriting and the above points are
            not relevant then use this change the rewrite method.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetMultiFields(System.String[])">
            <summary>
            Set the fields a query should be expanded to when the field is
            <c>null</c>
            </summary>
            <param name="fields">the fields used to expand the query</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.GetMultiFields">
            <summary>
            Returns the fields used to expand the query when the field for a
            certain query is <c>null</c>
            </summary>
            <returns>the fields used to expand the query</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FuzzyPrefixLength">
            <summary>
            Gets or Sets the prefix length for fuzzy queries. Default is 0.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.Locale">
            <summary>
            Gets or Sets current locale, allowing access by subclasses. Used by date range parsing
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetDefaultPhraseSlop(System.Int32)">
            <summary>
            Sets the default slop for phrases. If zero, then exact phrase matches are
            required. Default value is zero.
            </summary>
            <param name="defaultPhraseSlop"></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.PhraseSlop">
            <summary>
            Gets or Sets the default slop for phrases. If zero, then exact phrase matches are
            required. Default value is zero. NOTE: Setter is deprecated.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FuzzyMinSim">
            <summary>
            Gets or Sets the minimum similarity for fuzzy queries. Default is defined on
            <see cref="F:Lucene.Net.Search.FuzzyQuery.DefaultMinSimilarity"/>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.FieldsBoost">
            <summary>
             Gets or Sets the field to boost map used to set boost for each field.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetDateResolution(Lucene.Net.Documents.DateTools.Resolution)">
            <summary>
            Sets the default <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> used for certain field when
            no <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> is defined for this field.
            </summary>
            <param name="dateResolution">the default <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DateResolution">
            <summary>
            Gets the default <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> used for certain field when
            no <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> is defined for this field.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.SetDateResolution(System.Collections.Generic.IDictionary{System.String,System.Nullable{Lucene.Net.Documents.DateTools.Resolution}})">
            <summary>
            Sets the <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/> used for each field
            </summary>
            <param name="dateRes">a collection that maps a field to its <see cref="T:Lucene.Net.Documents.DateTools.Resolution"/></param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Flexible.Standard.StandardQueryParser.DateResolutionMap">
            <summary>
            Gets or Sets the field to <see cref="T:DateTools.Resolution?"/> map used to normalize each date field.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.AND_OPERATOR">
            <summary>Enables <c>AND</c> operator (+)</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.NOT_OPERATOR">
            <summary>Enables <c>NOT</c> operator (-)</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.OR_OPERATOR">
            <summary>Enables <c>OR</c> operator (|)</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.PREFIX_OPERATOR">
            <summary>Enables <c>PREFIX</c> operator (*)</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.PHRASE_OPERATOR">
            <summary>Enables <c>PHRASE</c> operator (")</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.PRECEDENCE_OPERATORS">
            <summary>Enables <c>PRECEDENCE</c> operators: <c>(</c> and <c>)</c></summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.ESCAPE_OPERATOR">
            <summary>Enables <c>ESCAPE</c> operator (\)</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.WHITESPACE_OPERATOR">
            <summary>Enables <c>WHITESPACE</c> operators: ' ' '\n' '\r' '\t'</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.FUZZY_OPERATOR">
            <summary>Enables <c>FUZZY</c> operators: (~) on single terms</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.Operator.NEAR_OPERATOR">
            <summary>Enables <c>NEAR</c> operators: (~) on phrases</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Simple.SimpleQueryParser">
            <summary>
            <see cref="T:Lucene.Net.QueryParsers.Simple.SimpleQueryParser"/> is used to parse human readable query syntax.
            <para/>
            The main idea behind this parser is that a person should be able to type
            whatever they want to represent a query, and this parser will do its best
            to interpret what to search for no matter how poorly composed the request
            may be. Tokens are considered to be any of a term, phrase, or subquery for the
            operations described below.  Whitespace including ' ' '\n' '\r' and '\t'
            and certain operators may be used to delimit tokens ( ) + | " .
            <para/>
            Any errors in query syntax will be ignored and the parser will attempt
            to decipher what it can; however, this may mean odd or unexpected results.
            <h4>Query Operators</h4>
            <list type="bullet">
             <item><description>'<c>+</c>' specifies <c>AND</c> operation: <c>token1+token2</c></description></item>
             <item><description>'<c>|</c>' specifies <c>OR</c> operation: <c>token1|token2</c></description></item>
             <item><description>'<c>-</c>' negates a single token: <c>-token0</c></description></item>
             <item><description>'<c>"</c>' creates phrases of terms: <c>"term1 term2 ..."</c></description></item>
             <item><description>'<c>*</c>' at the end of terms specifies prefix query: <c>term*</c></description></item>
             <item><description>'<c>~</c>N' at the end of terms specifies fuzzy query: <c>term~1</c></description></item>
             <item><description>'<c>~</c>N' at the end of phrases specifies near query: <c>"term1 term2"~5</c></description></item>
             <item><description>'<c>(</c>' and '<c>)</c>' specifies precedence: <c>token1 + (token2 | token3)</c></description></item>
            </list>
            <para/>
            The default operator is <c>OR</c> if no other operator is specified.
            For example, the following will <c>OR</c> <c>token1</c> and <c>token2</c> together:
            <c>token1 token2</c>
            <para/>
            Normal operator precedence will be simple order from right to left.
            For example, the following will evaluate <c>token1 OR token2</c> first,
            then <c>AND</c> with <c>token3</c>:
            <code>token1 | token2 + token3</code>
            <h4>Escaping</h4>
            <para/>
            An individual term may contain any possible character with certain characters
            requiring escaping using a '<c>\</c>'.  The following characters will need to be escaped in
            terms and phrases:
            <c>+ | " ( ) ' \</c>
            <para/>
            The '<c>-</c>' operator is a special case.  On individual terms (not phrases) the first
            character of a term that is <c>-</c> must be escaped; however, any '<c>-</c>' characters
            beyond the first character do not need to be escaped.
            For example:
            <list type="bullet">
              <item><description><c>-term1</c>   -- Specifies <c>NOT</c> operation against <c>term1</c></description></item>
              <item><description><c>\-term1</c>  -- Searches for the term <c>-term1</c>.</description></item>
              <item><description><c>term-1</c>   -- Searches for the term <c>term-1</c>.</description></item>
              <item><description><c>term\-1</c>  -- Searches for the term <c>term-1</c>.</description></item>
            </list>
            <para/>
            The '<c>*</c>' operator is a special case. On individual terms (not phrases) the last
            character of a term that is '<c>*</c>' must be escaped; however, any '<c>*</c>' characters
            before the last character do not need to be escaped:
            <list type="bullet">
              <item><description><c>term1*</c>  --  Searches for the prefix <c>term1</c></description></item>
              <item><description><c>term1\*</c> --  Searches for the term <c>term1*</c></description></item>
              <item><description><c>term*1</c>  --  Searches for the term <c>term*1</c></description></item>
              <item><description><c>term\*1</c> --  Searches for the term <c>term*1</c></description></item>
            </list>
            <para/>
            Note that above examples consider the terms before text processing.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.m_weights">
            <summary>Map of fields to query against with their weights</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.m_flags">
            <summary>flags to the parser (to turn features on/off)</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.#ctor(Lucene.Net.Analysis.Analyzer,System.String)">
            <summary>Creates a new parser searching over a single field.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.#ctor(Lucene.Net.Analysis.Analyzer,System.Collections.Generic.IDictionary{System.String,System.Single})">
            <summary>Creates a new parser searching over multiple fields with different weights.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.#ctor(Lucene.Net.Analysis.Analyzer,System.Collections.Generic.IDictionary{System.String,System.Single},Lucene.Net.QueryParsers.Simple.Operator)">
            <summary>Creates a new parser with custom flags used to enable/disable certain features.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.Parse(System.String)">
            <summary>Parses the query text and returns parsed query (or null if empty)</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.BuildQueryTree(Lucene.Net.QueryParsers.Simple.SimpleQueryParser.State,Lucene.Net.Search.Query)">
            <summary>
            buildQueryTree should be called after a term, phrase, or subquery
            is consumed to be added to our existing query tree
            this method will only add to the existing tree if the branch contained in state is not null
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.ParseFuzziness(Lucene.Net.QueryParsers.Simple.SimpleQueryParser.State)">
            <summary>
            Helper parsing fuzziness from parsing state
            </summary>
            <returns>slop/edit distance, 0 in the case of non-parsing slop/edit string</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.TokenFinished(Lucene.Net.QueryParsers.Simple.SimpleQueryParser.State)">
            <summary>
            Helper returning true if the state has reached the end of token.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.NewDefaultQuery(System.String)">
            <summary>
            Factory method to generate a standard query (no phrase or prefix operators).
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.NewFuzzyQuery(System.String,System.Int32)">
            <summary>
            Factory method to generate a fuzzy query.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.NewPhraseQuery(System.String,System.Int32)">
            <summary>
            Factory method to generate a phrase query with slop.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.NewPrefixQuery(System.String)">
            <summary>
            Factory method to generate a prefix query.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.Simplify(Lucene.Net.Search.BooleanQuery)">
            <summary>
            Helper to simplify boolean queries with 0 or 1 clause
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Simple.SimpleQueryParser.DefaultOperator">
            <summary>
            Gets or Sets the implicit operator setting, which will be
            either <see cref="F:Lucene.Net.Search.Occur.SHOULD"/> or <see cref="F:Lucene.Net.Search.Occur.MUST"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.ICharStream">
            <summary> 
            This interface describes a character stream that maintains line and
            column number positions of the characters.  It also has the capability
            to backup the stream to some extent.  An implementation of this
            interface is used in the TokenManager implementation generated by
            JavaCCParser.
            
            All the methods except <see cref="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BackUp(System.Int32)"/> can be implemented in any fashion. <see cref="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BackUp(System.Int32)"/>
            needs to be implemented correctly for the correct operation of the lexer.
            Rest of the methods are all used to get information like line number,
            column number and the string that constitutes a token and are not used
            by the lexer. Hence their implementation won't affect the generated lexer's
            operation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.ReadChar">
            <summary> 
            Returns the next character from the selected input.  The method
            of selecting the input is the responsibility of the class
            implementing this interface.  Can throw any <see cref="T:System.IO.IOException"/>.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.Column">
            <summary>
            Returns the column position of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.EndColumn"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.Line">
            <summary>
            Returns the line number of the character last read.
            </summary>
            <seealso cref="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.EndLine"/>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.EndColumn">
            <summary>
            Returns the column number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.EndLine">
            <summary> 
            Returns the line number of the last character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BeginColumn">
            <summary> 
            Returns the column number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BeginLine">
            <summary> 
            Returns the line number of the first character for current token (being
            matched after the last call to BeginTOken).
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BackUp(System.Int32)">
            <summary> 
            Backs up the input stream by amount steps. Lexer calls this method if it
            had already read some characters, but could not use them to match a
            (longer) token. So, they will be used again as the prefix of the next
            token and it is the implemetation's responsibility to do this right.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BeginToken">
            <summary> 
            Returns the next character that marks the beginning of the next token.
            All characters must remain in the buffer between two successive calls
            to this method to implement <see cref="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.BackUp(System.Int32)"/> correctly.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.Image">
            <summary> 
            Returns a string made up of characters from the marked token beginning
            to the current buffer position. Implementations have the choice of returning
            anything that they want to. For example, for efficiency, one might decide
            to just return null, which is a valid implementation.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.GetSuffix(System.Int32)">
            <summary> 
            Returns an array of characters that make up the suffix of length 'len' for
            the currently matched token. This is used to build up the matched string
            for use in actions in the case of MORE. A simple and inefficient
            implementation of this is as follows :
            
            <code>
            {
                string t = Image;
                return t.Substring(t.Length - len, len).ToCharArray();
            }
            </code>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ICharStream.Done">
            <summary> 
            The lexer calls this function to indicate that it is done with the stream
            and hence implementations can free any resources held by this class.
            Again, the body of this function can be just empty and it will not
            affect the lexer's operation.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.FastCharStream">
            <summary>
            An efficient implementation of JavaCC's <see cref="T:Lucene.Net.QueryParsers.Surround.Parser.ICharStream"/> interface.  
            <para/>
            Note that
            this does not do line-number counting, but instead keeps track of the
            character position of the token in the input, as required by Lucene's <see cref="T:Lucene.Net.Analysis.Token" />
            API.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.FastCharStream.#ctor(System.IO.TextReader)">
            <summary>
            Constructs from a <see cref="T:System.IO.TextReader"/>. 
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.ParseException">
            <summary> 
            This exception is thrown when parse errors are encountered.
            You can explicitly create objects of this exception type by
            calling the method GenerateParseException in the generated
            parser.
            
            You can modify this class to customize your error reporting
            mechanisms so long as you retain the public fields.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ParseException.#ctor(Lucene.Net.QueryParsers.Surround.Parser.Token,System.Int32[][],System.String[])">
            <summary>
            This constructor is used by the method "GenerateParseException"
            in the generated parser.  Calling this constructor generates
            a new object of this type with the fields <paramref name="currentToken"/>,
            <paramref name="expectedTokenSequences"/>, and <paramref name="tokenImage"/> set.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ParseException.#ctor">
            The following constructors are for use by you for whatever
            purpose you can think of.  Constructing the exception in this
            manner makes the exception behave in the normal way - i.e., as
            documented in the class "Throwable".  The fields "errorToken",
            "expectedTokenSequences", and "tokenImage" do not contain
            relevant information.  The JavaCC generated code does not use
            these constructors.
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ParseException.CurrentToken">
            <summary> 
            This is the last token that has been consumed successfully.  If
            this object has been created due to a parse error, the token
            following this token will (therefore) be the first error token.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ParseException.ExpectedTokenSequences">
            <summary> 
            Each entry in this array is an array of integers.  Each array
            of integers represents a sequence of tokens (by their ordinal
            values) that is expected at this point of the parse.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.ParseException.TokenImage">
            <summary> 
            This is a reference to the "tokenImage" array of the generated
            parser within which the parse error occurred.  This array is
            defined in the generated ...Constants interface.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ParseException.Initialize(Lucene.Net.QueryParsers.Surround.Parser.Token,System.Int32[][],System.String[])">
            <summary>
            It uses <paramref name="currentToken"/> and <paramref name="expectedTokenSequences"/> to generate a parse
            error message and returns it.  If this object has been created
            due to a parse error, and you do not catch it (it gets thrown
            from the parser) the correct error message
            gets displayed.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.ParseException.eol">
            <summary> 
            The end of line string for this machine.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.ParseException.AddEscapes(System.String)">
            <summary> 
            Used to convert raw characters to their escaped version
            when these raw version cannot be used as part of an ASCII
            string literal.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.QueryParser">
             <summary>
             This class is generated by JavaCC.  The only method that clients should need
             to call is <see cref="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.Parse(System.String)"/>.
            
             <para>
             This parser generates queries that make use of position information
             (Span queries). It provides positional operators (<c>w</c> and
             <c>n</c>) that accept a numeric distance, as well as boolean
             operators (<c>and</c>, <c>or</c>, and <c>not</c>,
             wildcards (<c>///</c> and <c>?</c>), quoting (with
             <c>"</c>), and boosting (via <c>^</c>).
             </para>
            
             <para>
             The operators (W, N, AND, OR, NOT) can be expressed lower-cased or
             upper-cased, and the non-unary operators (everything but NOT) support
             both infix <c>(a AND b AND c)</c> and prefix <c>AND(a, b,
             c)</c> notation.
             </para>
            
             <para>
             The W and N operators express a positional relationship among their
             operands.  N is ordered, and W is unordered.  The distance is 1 by
             default, meaning the operands are adjacent, or may be provided as a
             prefix from 2-99.  So, for example, 3W(a, b) means that terms a and b
             must appear within three positions of each other, or in other words, up
             to two terms may appear between a and b. 
             </para>
             </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.TokenSource">
            <summary>Generated Token Manager.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.Token">
            <summary>Current token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.Jj_nt">
            <summary>Next token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.#ctor(Lucene.Net.QueryParsers.Surround.Parser.ICharStream)">
            <summary>Constructor with user supplied <see cref="T:Lucene.Net.QueryParsers.Surround.Parser.ICharStream"/>.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.ReInit(Lucene.Net.QueryParsers.Surround.Parser.ICharStream)">
            <summary>Reinitialize.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.#ctor(Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager)">
            <summary>Constructor with generated Token Manager.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.ReInit(Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager)">
            <summary>Reinitialize.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.LookaheadSuccess.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.GetNextToken">
            <summary>Get the next Token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.GetToken(System.Int32)">
            <summary>Get the specific Token.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.GenerateParseException">
            <summary>Generate ParseException.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.Enable_tracing">
            <summary>Enable tracing. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParser.Disable_tracing">
            <summary>Disable tracing. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.EOF">
            <summary>End of File. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.NUM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.TERM_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.WHITESPACE">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.STAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.ONE_CHAR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.DISTOP_NUM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.OR">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.AND">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.NOT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.W">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.N">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.LPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.RPAREN">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.COMMA">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.COLON">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.CARAT">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.TRUNCQUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.QUOTED">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.SUFFIXTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.TRUNCTERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.TERM">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.RegexpToken.NUMBER">
            <summary>RegularExpression Id. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.LexicalToken.Boost">
            <summary>Lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.LexicalToken.DEFAULT">
            <summary>Lexical state.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.QueryParserConstants">
            <summary> Token literal values and constants.
            Generated by org.javacc.parser.OtherFilesGen#start()
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.QueryParserConstants.TokenImage">
            <summary>Literal token values. </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager">
            <summary>
            Token Manager.
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.debugStream">
            <summary>Debug output. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.SetDebugStream(System.IO.TextWriter)">
            <summary>Set debug output. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.jjstrLiteralImages">
            <summary>Token literal values.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.lexStateNames">
            <summary>Lexer state names.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.jjnewLexState">
            <summary>Lex State array.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.#ctor(Lucene.Net.QueryParsers.Surround.Parser.ICharStream)">
            <summary>Constructor.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.#ctor(Lucene.Net.QueryParsers.Surround.Parser.ICharStream,System.Int32)">
            <summary>Constructor.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.ReInit(Lucene.Net.QueryParsers.Surround.Parser.ICharStream)">
            <summary>Reinitialize parser.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.ReInit(Lucene.Net.QueryParsers.Surround.Parser.ICharStream,System.Int32)">
            <summary>Reinitialize parser.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.SwitchTo(System.Int32)">
            <summary>Switch to specified lex state.</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.QueryParserTokenManager.GetNextToken">
            <summary>Get the next Token.</summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.Token">
            <summary> 
            Describes the input token stream.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.Kind">
            <summary> 
            An integer that describes the kind of this token.  This numbering
            system is determined by JavaCCParser, and a table of these numbers is
            stored in the file ...Constants.java.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.BeginLine">
            <summary>The line number of the first character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.BeginColumn">
            <summary>The column number of the first character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.EndLine">
            <summary>The line number of the last character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.EndColumn">
            <summary>The column number of the last character of this Token. </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.Image">
            <summary>The string image of the token.</summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.Next">
            <summary> 
            A reference to the next regular (non-special) token from the input
            stream.  If this is the last token from the input stream, or if the
            token manager has not read tokens beyond this one, this field is
            set to null.  This is true only if this token is also a regular
            token.  Otherwise, see below for a description of the contents of
            this field.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.SpecialToken">
            <summary> 
            This field is used to access special tokens that occur prior to this
            token, but after the immediately preceding regular (non-special) token.
            If there are no such special tokens, this field is set to null.
            When there are more than one such special token, this field refers
            to the last of these special tokens, which in turn refers to the next
            previous special token through its specialToken field, and so on
            until the first special token (whose specialToken field is null).
            The next fields of special tokens refer to other special tokens that
            immediately follow it (without an intervening regular token).  If there
            is no such token, this field is null.
            </summary>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.Token.Value">
            <summary> 
            An optional attribute value of the Token.
            Tokens which are not used as syntactic sugar will often contain
            meaningful values that will be used later on by the compiler or
            interpreter. This attribute value is often different from the image.
            Any subclass of Token that actually wants to return a non-null value can
            override this method as appropriate.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.Token.#ctor">
            <summary> 
            No-argument constructor
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.Token.#ctor(System.Int32)">
            <summary> 
            Constructs a new token for the specified Image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.Token.#ctor(System.Int32,System.String)">
            <summary> 
            Constructs a new token for the specified Image and Kind.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.Token.ToString">
            <summary> 
            Returns the image.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.Token.NewToken(System.Int32,System.String)">
            <summary> 
            Returns a new Token object, by default. However, if you want, you
            can create and return subclass objects based on the value of ofKind.
            Simply add the cases to the switch for all those special cases.
            For example, if you have a subclass of Token called IDToken that
            you want to create if ofKind is ID, simply add something like :
            
            case MyParserConstants.ID : return new IDToken(ofKind, image);
            
            to the following switch statement. Then you can cast matchedToken
            variable to the appropriate type and use sit in your lexical actions.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError">
            <summary>Token Manager Error. </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.LEXICAL_ERROR">
            <summary> Lexical error occurred.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.STATIC_LEXER_ERROR">
            <summary> An attempt was made to create a second instance of a static token manager.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.INVALID_LEXICAL_STATE">
            <summary> Tried to change to an invalid lexical state.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.LOOP_DETECTED">
            <summary> Detected (and bailed out of) an infinite loop in the token manager.</summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.errorCode">
            <summary> Indicates the reason why the exception is thrown. It will have
            one of the above 4 values.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.AddEscapes(System.String)">
            <summary> 
            Replaces unprintable characters by their escaped (or unicode escaped)
            equivalents in the given string
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.LexicalError(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char)">
            <summary>
            Returns a detailed message for the Error when it is thrown by the
            token manager to indicate a lexical error.
            </summary>
            <remarks>You can customize the lexical error message by modifying this method.</remarks>
            <param name="eofSeen">indicates if EOF caused the lexical error</param>
            <param name="lexState">lexical state in which this error occurred</param>
            <param name="errorLine">line number when the error occurred</param>
            <param name="errorColumn">column number when the error occurred</param>
            <param name="errorAfter">prefix that was seen before this error occurred</param>
            <param name="curChar">the offending character</param>
            <returns>Detailed error message</returns>
        </member>
        <member name="P:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.Message">
            <summary> 
            You can also modify the body of this method to customize your error messages.
            For example, cases like LOOP_DETECTED and INVALID_LEXICAL_STATE are not
            of end-users concern, so you can return something like :
            
            "Internal Error : Please file a bug report .... "
            
            from this method for such cases in the release version of your parser.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.#ctor">
            <summary>No arg constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.#ctor(System.String,System.Int32)">
            <summary>Constructor with message and reason. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.#ctor(System.Boolean,System.Int32,System.Int32,System.Int32,System.String,System.Char,System.Int32)">
            <summary>Full Constructor. </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Parser.TokenMgrError.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.AndQuery">
            <summary>
            Factory for conjunctions
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.BasicQueryFactory">
            <summary>
            Factory for creating basic term queries
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.BasicQueryFactory.Equals(System.Object)">
            <summary>
            Two BasicQueryFactory's are equal when they generate
            the same types of basic queries, or both cannot generate queries anymore.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.ComposedQuery">
            <summary>
            Base class for composite queries (such as AND/OR/NOT)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.DistanceQuery">
            <summary>
            Factory for NEAR queries 
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.IDistanceSubQuery">
            <summary>
            Interface for queries that can be nested as subqueries
            into a span near.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.IDistanceSubQuery.DistanceSubQueryNotAllowed">
            <summary>
            When <see cref="M:Lucene.Net.QueryParsers.Surround.Query.IDistanceSubQuery.DistanceSubQueryNotAllowed"/> returns non null, the reason why the subquery
            is not allowed as a distance subquery is returned.
            <para/>When <see cref="M:Lucene.Net.QueryParsers.Surround.Query.IDistanceSubQuery.DistanceSubQueryNotAllowed"/> returns null <see cref="M:Lucene.Net.QueryParsers.Surround.Query.IDistanceSubQuery.AddSpanQueries(Lucene.Net.QueryParsers.Surround.Query.SpanNearClauseFactory)"/> can be used
            in the creation of the span near clause for the subquery.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.FieldsQuery">
            <summary>
            Forms an OR query of the provided query across multiple fields.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.NotQuery">
            <summary>
            Factory for prohibited clauses
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.OrQuery">
            <summary>
            Factory for disjunctions
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.RewriteQuery`1.Clone">
            <summary>
            Not supported by this query.
            </summary>
            <exception cref="T:System.NotSupportedException">throws <see cref="T:System.NotSupportedException"/> always: clone is not supported.</exception>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SimpleTerm">
            <summary>
            Base class for queries that expand to sets of simple terms.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SimpleTerm.IMatchingTermVisitor">
            <summary>
            Callback to visit each matching term during "rewrite"
            in <see cref="M:Lucene.Net.QueryParsers.Surround.Query.SimpleTerm.IMatchingTermVisitor.VisitMatchingTerm(Lucene.Net.Index.Term)"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SpanNearClauseFactory">
            <summary>
            SpanNearClauseFactory:
            <para/>
            Operations:
            
            <list type="bullet">
                <item><description>create for a field name and an indexreader.</description></item>
            
                <item><description>add a weighted Term - this should add a corresponding SpanTermQuery, or increase the weight of an existing one.</description></item>
            
                <item><description>add a weighted subquery SpanNearQuery</description></item>
            
                <item><description>create a clause for SpanNearQuery from the things added above.</description></item>
            </list>
            <para/>
            For this, create an array of SpanQuery's from the added ones.
            The clause normally is a SpanOrQuery over the added subquery SpanNearQuery
            the SpanTermQuery's for the added Term's
            <para/>
            When  it is necessary to suppress double subqueries as much as possible:
            GetHashCode() and Equals() on unweighted SpanQuery are needed (possibly via GetTerms(),
            the terms are individually hashable).
            Idem SpanNearQuery: hash on the subqueries and the slop.
            Evt. merge SpanNearQuery's by adding the weights of the corresponding subqueries.
            <para/>
            To be determined:
            Are SpanQuery weights handled correctly during search by Lucene?
            Should the resulting SpanOrQuery be sorted?
            Could other SpanQueries be added for use in this factory:
            - SpanOrQuery: in principle yes, but it only has access to it's terms
                           via getTerms(); are the corresponding weights available?
            - SpanFirstQuery: treat similar to subquery SpanNearQuery. (ok?)
            - SpanNotQuery: treat similar to subquery SpanNearQuery. (ok?)
            
            Factory for <see cref="T:Lucene.Net.Search.Spans.SpanOrQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SrndPrefixQuery">
            <summary>
            Query that matches string prefixes
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SrndQuery">
            <summary>
            Lowest level base class for surround queries 
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.ToString">
            <summary>
            This method is used by <see cref="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.GetHashCode"/> and <see cref="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.Equals(System.Object)"/>,
            see LUCENE-2945.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.Clone">
            <summary> Shallow clone. Subclasses must override this if they
            need to clone any members deeply,
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.GetHashCode">
            <summary>
            For subclasses of <see cref="T:Lucene.Net.QueryParsers.Surround.Query.SrndQuery"/> within the namespace
            <see cref="N:Lucene.Net.QueryParsers.Surround.Query"/>
            it is not necessary to override this method, <see cref="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.ToString"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.Equals(System.Object)">
            <summary>
            For subclasses of <see cref="T:Lucene.Net.QueryParsers.Surround.Query.SrndQuery"/> within the namespace
            <see cref="N:Lucene.Net.QueryParsers.Surround.Query"/>
            it is not necessary to override this method, <see cref="M:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.ToString"/>
            </summary>
        </member>
        <member name="F:Lucene.Net.QueryParsers.Surround.Query.SrndQuery.TheEmptyLcnQuery">
            <summary> An empty Lucene query  </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SrndTermQuery">
            <summary>
            Simple single-term clause
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.SrndTruncQuery">
            <summary>
            Query that matches wildcards
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Surround.Query.TooManyBasicQueries">
            <summary>
            Exception thrown when <see cref="T:Lucene.Net.QueryParsers.Surround.Query.BasicQueryFactory"/> would exceed the limit
            of query clauses.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Surround.Query.TooManyBasicQueries.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.BooleanFilterBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Queries.BooleanFilter"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.BooleanQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.BooleanQuery"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.BooleanQueryBuilder.GetQuery(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.QueryObjectBuilder#process(org.w3c.dom.Element)
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.BoostingQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Queries.BoostingQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.BoostingTermBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Payloads.PayloadTermQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.CachedFilterBuilder">
            <summary>
            Filters are cached in an LRU Cache keyed on the contained query or filter object. Using this will
            speed up overall performance for repeated uses of the same expensive query/filter. The sorts of
            queries/filters likely to benefit from caching need not necessarily be complex - e.g. simple
            TermQuerys with a large DF (document frequency) can be expensive  on large indexes.
            A good example of this might be a term query on a field with only 2 possible  values -
            "true" or "false". In a large index, querying or filtering on this field requires reading
            millions  of document ids from disk which can more usefully be cached as a filter bitset.
            <para/>
            For Queries/Filters to be cached and reused the object must implement hashcode and
            equals methods correctly so that duplicate queries/filters can be detected in the cache.
            <para/>
            The CoreParser.maxNumCachedFilters property can be used to control the size of the LRU
            Cache established during the construction of CoreParser instances.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.ConstantScoreQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.ConstantScoreQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.DisjunctionMaxQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.DisjunctionMaxQuery"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.DisjunctionMaxQueryBuilder.GetQuery(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.QueryObjectBuilder#process(org.w3c.dom.Element)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.DuplicateFilterBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Sandbox.Queries.DuplicateFilter"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.FilteredQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.FilteredQuery"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.FilteredQueryBuilder.GetQuery(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.QueryObjectBuilder#process(org.w3c.dom.Element)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.FuzzyLikeThisQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Sandbox.Queries.FuzzyLikeThisQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.LikeThisQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Queries.Mlt.MoreLikeThisQuery"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.LikeThisQueryBuilder.GetQuery(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.QueryObjectBuilder#process(org.w3c.dom.Element)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.MatchAllDocsQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.MatchAllDocsQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.NumericRangeFilterBuilder">
            <summary>
            Creates a <see cref="T:Lucene.Net.Search.NumericRangeFilter"/>. The table below specifies the required
            attributes and the defaults if optional attributes are omitted. For more
            detail on what each of the attributes actually do, consult the documentation
            for <see cref="T:Lucene.Net.Search.NumericRangeFilter"/>:
            <list type="table">
                <listheader>
                    <term>Attribute name</term>
                    <term>Values</term>
                    <term>Required</term>
                    <term>Default</term>
                </listheader>
                <item>
                    <term>fieldName</term>
                    <term>String</term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>lowerTerm</term>
                    <term>Specified by <c>type</c></term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>upperTerm</term>
                    <term>Specified by <c>type</c></term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>type</term>
                    <term>int, long, float, double</term>
                    <term>No</term>
                    <term>int</term>
                </item>
                <item>
                    <term>includeLower</term>
                    <term>true, false</term>
                    <term>No</term>
                    <term>true</term>
                </item>
                <item>
                    <term>includeUpper</term>
                    <term>true, false</term>
                    <term>No</term>
                    <term>true</term>
                </item>
                <item>
                    <term>precisionStep</term>
                    <term>int</term>
                    <term>No</term>
                    <term>4</term>
                </item>
            </list>
            <para/>
            If an error occurs parsing the supplied <c>lowerTerm</c> or
            <c>upperTerm</c> into the numeric type specified by <c>type</c>, then the
            error will be silently ignored and the resulting filter will not match any
            documents.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.NumericRangeFilterBuilder.SetStrictMode(System.Boolean)">
            <summary>
            Specifies how this <see cref="T:Lucene.Net.QueryParsers.Xml.Builders.NumericRangeFilterBuilder"/> will handle errors.
            <para/>
            If this is set to true, <see cref="M:Lucene.Net.QueryParsers.Xml.Builders.NumericRangeFilterBuilder.GetFilter(System.Xml.XmlElement)"/> will throw a
            <see cref="T:Lucene.Net.QueryParsers.Xml.ParserException"/> if it is unable to parse the lowerTerm or upperTerm
            into the appropriate numeric type. If this is set to false, then this
            exception will be silently ignored and the resulting filter will not match
            any documents.
            <para/>
            Defaults to false.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.NumericRangeQueryBuilder">
            <summary>
            Creates a <see cref="T:Lucene.Net.Search.NumericRangeQuery"/>. The table below specifies the required
            attributes and the defaults if optional attributes are omitted. For more
            detail on what each of the attributes actually do, consult the documentation
            for <see cref="T:Lucene.Net.Search.NumericRangeQuery"/>:
            <list type="table">
                <listheader>
                    <term>Attribute name</term>
                    <term>Values</term>
                    <term>Required</term>
                    <term>Default</term>
                </listheader>
                <item>
                    <term>fieldName</term>
                    <term>String</term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>lowerTerm</term>
                    <term>Specified by <c>type</c></term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>upperTerm</term>
                    <term>Specified by <c>type</c></term>
                    <term>Yes</term>
                    <term>N/A</term>
                </item>
                <item>
                    <term>type</term>
                    <term>int, long, float, double</term>
                    <term>No</term>
                    <term>int</term>
                </item>
                <item>
                    <term>includeLower</term>
                    <term>true, false</term>
                    <term>No</term>
                    <term>true</term>
                </item>
                <item>
                    <term>includeUpper</term>
                    <term>true, false</term>
                    <term>No</term>
                    <term>true</term>
                </item>
                <item>
                    <term>precisionStep</term>
                    <term>int</term>
                    <term>No</term>
                    <term>4</term>
                </item>
            </list>
            <para/>
            A <see cref="T:Lucene.Net.QueryParsers.Xml.ParserException"/> will be thrown if an error occurs parsing the
            supplied <c>lowerTerm</c> or <c>upperTerm</c> into the numeric type
            specified by <c>type</c>.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.RangeFilterBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.TermRangeFilter"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanBuilderBase">
            <summary>
            Base class for building <see cref="T:Lucene.Net.Search.Spans.SpanQuery"/>s
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.SpanBuilderBase.GetSpanQuery(System.Xml.XmlElement)">
            <summary>
            LUCENENET specific - must provide declaration of GetSpanQuery to satisfy
            contract of <see cref="T:Lucene.Net.QueryParsers.Xml.Builders.ISpanQueryBuilder"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanFirstBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Spans.SpanFirstQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanNearBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Spans.SpanNearQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanNotBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Spans.SpanNotQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanOrBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Spans.SpanOrQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanOrTermsBuilder">
            <summary>
            Builder that analyzes the text into a <see cref="T:Lucene.Net.Search.Spans.SpanOrQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.ISpanQueryBuilder">
            <summary>
            Interface for retrieving a <see cref="T:Lucene.Net.Search.Spans.SpanQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanQueryBuilderFactory">
            <summary>
            Factory for <see cref="T:Lucene.Net.QueryParsers.Xml.Builders.ISpanQueryBuilder"/>s
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.SpanTermBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.Spans.SpanTermQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.TermQueryBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Search.TermQuery"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.TermsFilterBuilder">
            <summary>
            Builder for <see cref="T:Lucene.Net.Queries.TermsFilter"/>
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.TermsFilterBuilder.GetFilter(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.FilterBuilder#process(org.w3c.dom.Element)
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.TermsQueryBuilder">
            <summary>
            Builds a <see cref="T:Lucene.Net.Search.BooleanQuery"/> from all of the terms found in the XML element using the choice of analyzer
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.Builders.UserInputQueryBuilder">
            <summary>
            <see cref="T:Lucene.Net.QueryParsers.Xml.Builders.UserInputQueryBuilder"/> uses 1 of 2 strategies for thread-safe parsing:
            1) Synchronizing access to "Parse" calls on a previously supplied <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>
            or..
            2) creating a new <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> object for each parse request
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.UserInputQueryBuilder.#ctor(Lucene.Net.QueryParsers.Classic.QueryParser)">
            <summary>
            This constructor has the disadvantage of not being able to change choice of default field name
            </summary>
            <param name="parser">thread un-safe query parser</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.UserInputQueryBuilder.GetQuery(System.Xml.XmlElement)">
            <summary>
            (non-Javadoc)
            @see org.apache.lucene.xmlparser.QueryObjectBuilder#process(org.w3c.dom.Element)
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.Builders.UserInputQueryBuilder.CreateQueryParser(System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Method to create a <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> - designed to be overridden
            </summary>
            <returns><see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/></returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.CoreParser">
            <summary>
            Assembles a <see cref="T:Lucene.Net.QueryParsers.Xml.IQueryBuilder"/> which uses only core Lucene Query objects
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.CoreParser.#ctor(Lucene.Net.Analysis.Analyzer,Lucene.Net.QueryParsers.Classic.QueryParser)">
            <summary>
            Construct an XML parser that uses a single instance <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> for handling
            UserQuery tags - all parse operations are synchronised on this parser
            </summary>
            <param name="analyzer"></param>
            <param name="parser">A <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> which will be synchronized on during parse calls.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.CoreParser.#ctor(System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Constructs an XML parser that creates a <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> for each UserQuery request.
            </summary>
            <param name="defaultField">The default field name used by <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>s constructed for UserQuery tags</param>
            <param name="analyzer"></param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.CorePlusExtensionsParser">
            <summary>
            Assembles a <see cref="T:Lucene.Net.Util.QueryBuilder"/> which uses <see cref="T:Lucene.Net.Search.Query"/> objects from
            Lucene's <c>sandbox</c> and <c>queries</c>
            modules in addition to core queries.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.CorePlusExtensionsParser.#ctor(Lucene.Net.Analysis.Analyzer,Lucene.Net.QueryParsers.Classic.QueryParser)">
            <summary>
            Construct an XML parser that uses a single instance <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> for handling
            UserQuery tags - all parse operations are synchronized on this parser
            </summary>
            <param name="analyzer"></param>
            <param name="parser">A <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> which will be synchronized on during parse calls.</param>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.CorePlusExtensionsParser.#ctor(System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Constructs an XML parser that creates a <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/> for each UserQuery request.
            </summary>
            <param name="defaultField">The default field name used by <see cref="T:Lucene.Net.QueryParsers.Classic.QueryParser"/>s constructed for UserQuery tags</param>
            <param name="analyzer"></param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.DOMUtils">
            <summary>
            Helper methods for parsing XML
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.GetChildByTagName(System.Xml.XmlElement,System.String)">
            <summary>Convenience method where there is only one child <see cref="T:System.Xml.XmlElement"/> of a given name</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.GetAttributeWithInheritance(System.Xml.XmlElement,System.String)">
            <summary>
            Returns an attribute value from this node, or first parent node with this attribute defined
            </summary>
            <param name="element"></param>
            <param name="attributeName"></param>
            <returns>A non-zero-length value if defined, otherwise null</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.GetChildTextByTagName(System.Xml.XmlElement,System.String)">
            <summary>Convenience method where there is only one child <see cref="T:System.Xml.XmlElement"/> of a given name</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.InsertChild(System.Xml.XmlElement,System.String,System.String)">
            <summary>Convenience method to append a new child with text</summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.LoadXML(System.IO.TextReader)">
            <summary>
            Helper method to parse an XML file into a DOM tree, given a <see cref="T:System.IO.TextReader"/>.
            </summary>
            <param name="input">reader of the XML file to be parsed</param>
            <returns>an <see cref="T:System.Xml.XmlDocument"/> object</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.LoadXML(System.IO.Stream)">
            <summary>
            Helper method to parse an XML file into a DOM tree, given a <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="input">reader of the XML file to be parsed</param>
            <returns>an <see cref="T:System.Xml.XmlDocument"/> object</returns>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.DOMUtils.LoadXML(System.Xml.XmlReader)">
            <summary>
            Helper method to parse an XML file into a DOM tree, given an <see cref="T:System.Xml.XmlReader"/>.
            </summary>
            <param name="input">reader of the XML file to be parsed</param>
            <returns>an <see cref="T:System.Xml.XmlDocument"/> object</returns>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.IFilterBuilder">
            <summary>
            Interface for building <see cref="T:Lucene.Net.Search.Filter"/>s
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.FilterBuilderFactory">
            <summary>
            Factory for <see cref="T:Lucene.Net.QueryParsers.Xml.IFilterBuilder"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.ParserException">
            <summary>
            Thrown when the xml queryparser encounters 
            invalid syntax/configuration.
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.ParserException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of this class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.IQueryBuilder">
            <summary>
            Implemented by objects that produce Lucene Query objects from XML streams. Implementations are
            expected to be thread-safe so that they can be used to simultaneously parse multiple XML documents.
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.QueryBuilderFactory">
            <summary>
            Factory for <see cref="T:Lucene.Net.QueryParsers.Xml.IQueryBuilder"/>
            </summary>
        </member>
        <member name="T:Lucene.Net.QueryParsers.Xml.QueryTemplateManager">
            <summary>
            Provides utilities for turning query form input (such as from a web page or Swing gui) into
            Lucene XML queries by using XSL templates.  This approach offers a convenient way of externalizing
            and changing how user input is turned into Lucene queries.
            Database applications often adopt similar practices by externalizing SQL in template files that can
            be easily changed/optimized by a DBA.
            The static methods can be used on their own or by creating an instance of this class you can store and
            re-use compiled stylesheets for fast use (e.g. in a server environment)
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.GetQueryAsXmlString(System.Collections.Generic.IDictionary{System.String,System.String},System.Xml.Xsl.XslCompiledTransform)">
            <summary>
            Fast means of constructing query using a precompiled stylesheet
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.GetQueryAsXmlString(System.Collections.Generic.IDictionary{System.String,System.String},System.IO.Stream)">
            <summary>
            Slow means of constructing query parsing a stylesheet from an input stream
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.GetQueryAsDOM(System.Collections.Generic.IDictionary{System.String,System.String},System.Xml.Xsl.XslCompiledTransform)">
            <summary>
            Fast means of constructing query using a cached,precompiled stylesheet
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.GetQueryAsDOM(System.Collections.Generic.IDictionary{System.String,System.String},System.IO.Stream)">
            <summary>
            Slow means of constructing query - parses stylesheet from input stream
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.TransformCriteria(System.Collections.Generic.IDictionary{System.String,System.String},System.IO.Stream,System.IO.Stream)">
            <summary>
            Slower transformation using an uncompiled stylesheet (suitable for development environment)
            </summary>
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.TransformCriteria(System.Collections.Generic.IDictionary{System.String,System.String},System.Xml.Xsl.XslCompiledTransform,System.IO.Stream)">
            <summary>
            Fast transformation using a pre-compiled stylesheet (suitable for production environments)
            </summary>   
        </member>
        <member name="M:Lucene.Net.QueryParsers.Xml.QueryTemplateManager.GetTemplates(System.IO.Stream)">
            <summary>
            Parses a query stylesheet for repeated use
            </summary>
        </member>
    </members>
</doc>
