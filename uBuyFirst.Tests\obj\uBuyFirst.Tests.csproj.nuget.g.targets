﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.resources.extensions\9.0.5\buildTransitive\net462\System.Resources.Extensions.targets" Condition="Exists('$(NuGetPackageRoot)system.resources.extensions\9.0.5\buildTransitive\net462\System.Resources.Extensions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.4.3\buildTransitive\netstandard2.0\Microsoft.Testing.Platform.MSBuild.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.4.3\buildTransitive\netstandard2.0\Microsoft.Testing.Platform.MSBuild.targets')" />
    <Import Project="$(NuGetPackageRoot)mstest.testadapter\3.6.4\build\net462\MSTest.TestAdapter.targets" Condition="Exists('$(NuGetPackageRoot)mstest.testadapter\3.6.4\build\net462\MSTest.TestAdapter.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage\17.11.1\build\netstandard2.0\Microsoft.CodeCoverage.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage\17.11.1\build\netstandard2.0\Microsoft.CodeCoverage.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk\17.11.1\build\net462\Microsoft.NET.Test.Sdk.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk\17.11.1\build\net462\Microsoft.NET.Test.Sdk.targets')" />
  </ImportGroup>
</Project>