﻿using System.Diagnostics.CodeAnalysis;

namespace uBuyFirst.Tools
{
    [SuppressMessage("ReSharper", "InconsistentNaming")]
    public static class Stat
    {
        public static int FindActiveThreads;

        public static int Call_SpeedCounter;
        public static int FindApiCounter;
        public static int FindRssCounter;

        public static int FindReqCount;

        public static int ItemsFoundCounter;

        public static int GetItemReqCount;
        public static int GetItemShippingCounter;
        public static int GetItemErrors;
        public static int BrowseApiCounter;
        public static int GetItemsCounter;

        public static int StatusUpdateCounter;
        public static int OutOfStockCounter;

        public static int AvatarReqCount;
        public static int OtherImagesReqCount;

        public static int ApiItem_Source;
        public static int S4Item_Source;
        public static int BrowseApiItem_Source;
        public static int Rss3Item_Source;

        //public static int RssItem_Source;

        public static int TotalItemsProcessed;

        public static int PushBulletCounter;
        public static int TelegramCounter;

        public static int WebCheckoutWonCounter;
        public static int WebCheckoutLostCounter;

        public static int PlaceOfferWonCounter;
        public static int PlaceOfferWonAmount;
        public static int WatchlistApiCounter;
        public static int PlaceOfferEmptyCounter;
        public static int MakeOfferOKCounter;
        public static int MakeOfferEmptyCounter;

        public static int _errorsCount;
        public static int TitleMatchCounter { get; set; }
        public static int TitleNotMatchCounter { get; set; }
        public static int OpenToBrowserCounter { get; set; }

        public static void ResetStatsAfterInitialSearch()
        {
            FindReqCount = 0;
            AvatarReqCount = 0;
            OtherImagesReqCount = 0;
            StatusUpdateCounter = 0;
            OutOfStockCounter = 0;
        }
    }
}
