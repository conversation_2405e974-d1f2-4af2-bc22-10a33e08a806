﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Filters;
using System.Collections.Generic; // Added for ActionData
using DevExpress.Data.Filtering;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class XFilterClassTests
    {
        [TestMethod]
        public void SetActionIdentifier_ValidIdentifier_InitializesHandlerAndUpdatesActionString()
        {
            // Arrange
            var instance = new XFilterClass();
            var identifier = FormatCellsAction.IDENTIFIER;

            // Act
            instance.ActionIdentifier = identifier;

            // Assert
            Assert.IsNotNull(instance.ActionHandler, "ActionHandler should not be null.");
            Assert.IsInstanceOfType(instance.ActionHandler, typeof(FormatCellsAction), "ActionHandler should be of type FormatCellsAction.");
            Assert.AreEqual("Format cells", instance.Action, "Action string should be updated to DisplayName.");
            Assert.AreEqual(identifier, instance.ActionIdentifier, "ActionIdentifier should be set correctly.");
        }

        [TestMethod]
        public void SetActionString_ValidLegacyString_InitializesHandlerAndUpdatesIdentifier()
        {
            // Arrange
            var instance = new XFilterClass();
            var legacyActionString = "Remove rows";

            // Act
            instance.Action = legacyActionString;

            // Assert
            Assert.IsNotNull(instance.ActionHandler, "ActionHandler should not be null.");
            Assert.IsInstanceOfType(instance.ActionHandler, typeof(RemoveRowsAction), "ActionHandler should be of type RemoveRowsAction.");
            Assert.AreEqual(RemoveRowsAction.IDENTIFIER, instance.ActionIdentifier, "ActionIdentifier should be updated correctly.");
            Assert.AreEqual(legacyActionString, instance.Action, "Action string should remain the same.");
        }

        [TestMethod]
        public void SetActionIdentifier_BuyWithAccount_InitializesHandlerAndDeserializesData()
        {
            // Arrange
            var instance = new XFilterClass();
            instance.ActionData = new Dictionary<string, object> { { "AccountUsername", "testUser123" } };
            var identifier = BuyWithAccountAction.IDENTIFIER;

            // Act
            instance.ActionIdentifier = identifier;

            // Assert
            Assert.IsNotNull(instance.ActionHandler, "ActionHandler should not be null.");
            Assert.IsInstanceOfType(instance.ActionHandler, typeof(BuyWithAccountAction), "ActionHandler should be of type BuyWithAccountAction.");
            
            var buyAction = instance.ActionHandler as BuyWithAccountAction;
            Assert.IsNotNull(buyAction, "ActionHandler should be castable to BuyWithAccountAction.");
            Assert.AreEqual("testUser123", buyAction.AccountUsername, "AccountUsername should be deserialized correctly.");
            Assert.AreEqual("Buy with testUser123", instance.Action, "Action string should be updated with deserialized data.");
        }

        [TestMethod]
        public void ExportImport_RoundTrip_RestoresPropertiesCorrectly()
        {
            // Arrange
            var originalFilter = new XFilterClass();
            originalFilter.Alias = "Test Round Trip";
            originalFilter.Enabled = true;

            var buyAction = new BuyWithAccountAction { AccountUsername = "roundTripUser" };
            originalFilter.ActionHandler = buyAction;
            // Manually ensure ActionData is populated for BuyWithAccountAction
            // In a real scenario, the ActionHandler setter or the handler itself should manage this.
            // For this test, explicit setting ensures the data intended for serialization is present.
            // A more robust way would be to call a method on the action to populate the filter's ActionData,
            // e.g., buyAction.SaveToFilter(originalFilter);
            if (originalFilter.ActionData == null) originalFilter.ActionData = new Dictionary<string, object>();
            originalFilter.ActionData["AccountUsername"] = "roundTripUser";


            originalFilter.FormatColumn = "SomeColumn";
            originalFilter.FilterCriteria = CriteriaOperator.Parse("[Quantity] > 10");
            originalFilter.SerializedAppearance = new[] { "appearance1", "appearance2", "appearance3" };
            originalFilter.KeywordScope = "TestScope";
            originalFilter.Expression = originalFilter.FilterCriteria.ToString();

            // Act
            List<string> exportedCells = originalFilter.Export(); // Export() now directly returns List<string>
            // Line 94 (splitting exportedString) is no longer needed and was causing the CS1061 error.
            var newFilter = new XFilterClass();
            string importResult = newFilter.Import(exportedCells); // Pass the List<string> directly

            // Assert
            Assert.IsTrue(string.IsNullOrEmpty(importResult), $"Import failed: {importResult}");
            Assert.AreEqual(originalFilter.Alias, newFilter.Alias);
            Assert.AreEqual(originalFilter.Enabled, newFilter.Enabled);
            Assert.IsNotNull(newFilter.ActionHandler);
            Assert.IsInstanceOfType(newFilter.ActionHandler, typeof(BuyWithAccountAction));
            Assert.AreEqual(originalFilter.ActionIdentifier, newFilter.ActionIdentifier);
            Assert.AreEqual(originalFilter.Action, newFilter.Action); // Should be "Buy with roundTripUser"

            BuyWithAccountAction originalBuyAction = (BuyWithAccountAction)originalFilter.ActionHandler;
            BuyWithAccountAction newBuyAction = (BuyWithAccountAction)newFilter.ActionHandler;
            Assert.AreEqual(originalBuyAction.AccountUsername, newBuyAction.AccountUsername);

            Assert.AreEqual(originalFilter.FormatColumn, newFilter.FormatColumn);
            Assert.AreEqual(originalFilter.FilterCriteria.ToString(), newFilter.FilterCriteria.ToString());
            CollectionAssert.AreEqual(originalFilter.SerializedAppearance, newFilter.SerializedAppearance);
            Assert.AreEqual(originalFilter.KeywordScope, newFilter.KeywordScope);
            Assert.AreEqual(originalFilter.Expression, newFilter.Expression);
        }
    }
}
