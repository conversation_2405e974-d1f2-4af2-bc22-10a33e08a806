<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Lucene.Net.Memory</name>
    </assembly>
    <members>
        <member name="T:Lucene.Net.Index.Memory.MemoryIndex">
            <summary>
            High-performance single-document main memory Apache Lucene fulltext search index. 
            
            <h4>Overview</h4>
            
            This class is a replacement/substitute for a large subset of
            <see cref="T:Lucene.Net.Store.RAMDirectory"/> functionality. It is designed to
            enable maximum efficiency for on-the-fly matchmaking combining structured and 
            fuzzy fulltext search in realtime streaming applications such as Nux XQuery based XML 
            message queues, publish-subscribe systems for Blogs/newsfeeds, text chat, data acquisition and 
            distribution systems, application level routers, firewalls, classifiers, etc. 
            Rather than targeting fulltext search of infrequent queries over huge persistent 
            data archives (historic search), this class targets fulltext search of huge 
            numbers of queries over comparatively small transient realtime data (prospective 
            search). 
            For example as in 
            <code>
            float score = Search(string text, Query query)
            </code>
            <para>
            Each instance can hold at most one Lucene "document", with a document containing
            zero or more "fields", each field having a name and a fulltext value. The
            fulltext value is tokenized (split and transformed) into zero or more index terms 
            (aka words) on <code>AddField()</code>, according to the policy implemented by an
            Analyzer. For example, Lucene analyzers can split on whitespace, normalize to lower case
            for case insensitivity, ignore common terms with little discriminatory value such as "he", "in", "and" (stop
            words), reduce the terms to their natural linguistic root form such as "fishing"
            being reduced to "fish" (stemming), resolve synonyms/inflexions/thesauri 
            (upon indexing and/or querying), etc. For details, see
            <a target="_blank" href="http://today.java.net/pub/a/today/2003/07/30/LuceneIntro.html">Lucene Analyzer Intro</a>.
            </para>
            <para>
            Arbitrary Lucene queries can be run against this class - see <a target="_blank" 
            href="{@docRoot}/../queryparser/org/apache/lucene/queryparser/classic/package-summary.html#package_description">
            Lucene Query Syntax</a>
            as well as <a target="_blank" 
            href="http://today.java.net/pub/a/today/2003/11/07/QueryParserRules.html">Query Parser Rules</a>.
            Note that a Lucene query selects on the field names and associated (indexed) 
            tokenized terms, not on the original fulltext(s) - the latter are not stored 
            but rather thrown away immediately after tokenization.
            </para>
            <para>
            For some interesting background information on search technology, see Bob Wyman's
            <a target="_blank" 
            href="http://bobwyman.pubsub.com/main/2005/05/mary_hodder_poi.html">Prospective Search</a>, 
            Jim Gray's
            <a target="_blank" href="http://www.acmqueue.org/modules.php?name=Content&amp;pa=showpage&amp;pid=293&amp;page=4">
            A Call to Arms - Custom subscriptions</a>, and Tim Bray's
            <a target="_blank" 
            href="http://www.tbray.org/ongoing/When/200x/2003/07/30/OnSearchTOC">On Search, the Series</a>.
            
            
            <h4>Example Usage</h4> 
            
            <code>
            Analyzer analyzer = new SimpleAnalyzer(version);
            MemoryIndex index = new MemoryIndex();
            index.AddField("content", "Readings about Salmons and other select Alaska fishing Manuals", analyzer);
            index.AddField("author", "Tales of James", analyzer);
            QueryParser parser = new QueryParser(version, "content", analyzer);
            float score = index.Search(parser.Parse("+author:james +salmon~ +fish* manual~"));
            if (score &gt; 0.0f) {
                Console.WriteLine("it's a match");
            } else {
                Console.WriteLine("no match found");
            }
            Console.WriteLine("indexData=" + index.toString());
            </code>
            
            
            <h4>Example XQuery Usage</h4> 
            
            <code>
            (: An XQuery that finds all books authored by James that have something to do with "salmon fishing manuals", sorted by relevance :)
            declare namespace lucene = "java:nux.xom.pool.FullTextUtil";
            declare variable $query := "+salmon~ +fish* manual~"; (: any arbitrary Lucene query can go here :)
            
            for $book in /books/book[author="James" and lucene:match(abstract, $query) > 0.0]
            let $score := lucene:match($book/abstract, $query)
            order by $score descending
            return $book
            </code>
            
            
            <h4>No thread safety guarantees</h4>
            
            An instance can be queried multiple times with the same or different queries,
            but an instance is not thread-safe. If desired use idioms such as:
            <code>
            MemoryIndex index = ...
            lock (index) {
               // read and/or write index (i.e. add fields and/or query)
            } 
            </code>
            
            
            <h4>Performance Notes</h4>
            
            Internally there's a new data structure geared towards efficient indexing 
            and searching, plus the necessary support code to seamlessly plug into the Lucene 
            framework.
            </para>
            <para>
            This class performs very well for very small texts (e.g. 10 chars) 
            as well as for large texts (e.g. 10 MB) and everything in between. 
            Typically, it is about 10-100 times faster than <see cref="T:Lucene.Net.Store.RAMDirectory"/>.
            Note that <see cref="T:Lucene.Net.Store.RAMDirectory"/> has particularly 
            large efficiency overheads for small to medium sized texts, both in time and space.
            Indexing a field with N tokens takes O(N) in the best case, and O(N logN) in the worst 
            case. Memory consumption is probably larger than for <see cref="T:Lucene.Net.Store.RAMDirectory"/>.
            </para>
            <para>
            Example throughput of many simple term queries over a single MemoryIndex: 
            ~500000 queries/sec on a MacBook Pro, jdk 1.5.0_06, server VM. 
            As always, your mileage may vary.
            </para>
            <para>
            If you're curious about
            the whereabouts of bottlenecks, run java 1.5 with the non-perturbing '-server
            -agentlib:hprof=cpu=samples,depth=10' flags, then study the trace log and
            correlate its hotspot trailer with its call stack headers (see <a
            target="_blank"
            href="http://java.sun.com/developer/technicalArticles/Programming/HPROF.html">
            hprof tracing </a>).
            
            </para>
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.fields">
            <summary>
            info for each field: <see cref="T:System.Collections.Generic.IDictionary`2"/>
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.sortedFields">
            <summary>
            fields sorted ascending by fieldName; lazily computed on demand </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.#ctor">
            <summary>
            Constructs an empty instance.
            </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.#ctor(System.Boolean)">
            <summary>
            Constructs an empty instance that can optionally store the start and end
            character offset of each token term in the text. This can be useful for
            highlighting of hit locations with the Lucene highlighter package.
            Protected until the highlighter package matures, so that this can actually
            be meaningfully integrated.
            </summary>
            <param name="storeOffsets">
                       whether or not to store the start and end character offset of
                       each token term in the text </param>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.#ctor(System.Boolean,System.Int64)">
            <summary>
            Expert: This constructor accepts an upper limit for the number of bytes that should be reused if this instance is <see cref="M:Lucene.Net.Index.Memory.MemoryIndex.Reset"/>.
            </summary>
            <param name="storeOffsets"> <c>true</c> if offsets should be stored </param>
            <param name="maxReusedBytes"> the number of bytes that should remain in the internal memory pools after <see cref="M:Lucene.Net.Index.Memory.MemoryIndex.Reset"/> is called </param>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,System.String,Lucene.Net.Analysis.Analyzer)">
            <summary>
            Convenience method; Tokenizes the given field text and adds the resulting
            terms to the index; Equivalent to adding an indexed non-keyword Lucene
            <see cref="T:Lucene.Net.Documents.Field"/> that is tokenized, not stored,
            termVectorStored with positions (or termVectorStored with positions and offsets),
            </summary>
            <param name="fieldName"> a name to be associated with the text </param>
            <param name="text"> the text to tokenize and index. </param>
            <param name="analyzer"> the analyzer to use for tokenization </param>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.KeywordTokenStream``1(System.Collections.Generic.ICollection{``0})">
            <summary>
            Convenience method; Creates and returns a token stream that generates a
            token for each keyword in the given collection, "as is", without any
            transforming text analysis. The resulting token stream can be fed into
            <see cref="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,Lucene.Net.Analysis.TokenStream)"/>, perhaps wrapped into another
            <see cref="T:Lucene.Net.Analysis.TokenFilter"/>, as desired.
            </summary>
            <param name="keywords"> the keywords to generate tokens for </param>
            <returns> the corresponding token stream </returns>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.TokenStreamAnonymousClass`1.Dispose(System.Boolean)">
            <summary>
            Releases resources used by the <see cref="T:Lucene.Net.Index.Memory.MemoryIndex.TokenStreamAnonymousClass`1"/> and
            if overridden in a derived class, optionally releases unmanaged resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources;
            <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,Lucene.Net.Analysis.TokenStream)">
            <summary>
            Equivalent to <c>AddField(fieldName, stream, 1.0f)</c>.
            </summary>
            <param name="fieldName"> a name to be associated with the text </param>
            <param name="stream"> the token stream to retrieve tokens from </param>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,Lucene.Net.Analysis.TokenStream,System.Single)">
            <summary>
            Iterates over the given token stream and adds the resulting terms to the index;
            Equivalent to adding a tokenized, indexed, termVectorStored, unstored,
            Lucene <see cref="T:Lucene.Net.Documents.Field"/>.
            Finally closes the token stream. Note that untokenized keywords can be added with this method via 
            <see cref="T:KeywordTokenStream{T}(ICollection{T}"/>)"/>, the Lucene <c>KeywordTokenizer</c> or similar utilities.
            </summary>
            <param name="fieldName"> a name to be associated with the text </param>
            <param name="stream"> the token stream to retrieve tokens from. </param>
            <param name="boost"> the boost factor for hits for this field </param>
            <seealso cref="P:Lucene.Net.Documents.Field.Boost"/>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,Lucene.Net.Analysis.TokenStream,System.Single,System.Int32)">
            <summary>
            Iterates over the given token stream and adds the resulting terms to the index;
            Equivalent to adding a tokenized, indexed, termVectorStored, unstored,
            Lucene <see cref="T:Lucene.Net.Documents.Field"/>.
            Finally closes the token stream. Note that untokenized keywords can be added with this method via
            <see cref="T:KeywordTokenStream{T}(ICollection{T}"/>)"/>, the Lucene <c>KeywordTokenizer</c> or similar utilities.
            </summary>
            <param name="fieldName"> a name to be associated with the text </param>
            <param name="stream"> the token stream to retrieve tokens from. </param>
            <param name="boost"> the boost factor for hits for this field </param>
            <param name="positionIncrementGap"> 
            the position increment gap if fields with the same name are added more than once
            </param>
            <seealso cref="P:Lucene.Net.Documents.Field.Boost"/>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.AddField(System.String,Lucene.Net.Analysis.TokenStream,System.Single,System.Int32,System.Int32)">
            <summary>
            Iterates over the given token stream and adds the resulting terms to the index;
            Equivalent to adding a tokenized, indexed, termVectorStored, unstored,
            Lucene <see cref="T:Lucene.Net.Documents.Field"/>.
            Finally closes the token stream. Note that untokenized keywords can be added with this method via 
            <see cref="T:KeywordTokenStream{T}(ICollection{T}"/>)"/>, the Lucene <c>KeywordTokenizer</c> or similar utilities.
            
            </summary>
            <param name="fieldName"> a name to be associated with the text </param>
            <param name="stream"> the token stream to retrieve tokens from. </param>
            <param name="boost"> the boost factor for hits for this field </param>
            <param name="positionIncrementGap"> the position increment gap if fields with the same name are added more than once </param>
            <param name="offsetGap"> the offset gap if fields with the same name are added more than once </param>
            <seealso cref="P:Lucene.Net.Documents.Field.Boost"/>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.CreateSearcher">
            <summary>
            Creates and returns a searcher that can be used to execute arbitrary
            Lucene queries and to collect the resulting query results as hits.
            </summary>
            <returns> a searcher </returns>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.Search(Lucene.Net.Search.Query)">
            <summary>
            Convenience method that efficiently returns the relevance score by
            matching this index against the given Lucene query expression.
            </summary>
            <param name="query"> an arbitrary Lucene query to run against this index </param>
            <returns> the relevance score of the matchmaking; A number in the range
                    [0.0 .. 1.0], with 0.0 indicating no match. The higher the number
                    the better the match.
             </returns>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.GetMemorySize">
            <summary>
            Returns a reasonable approximation of the main memory [bytes] consumed by
            this instance. Useful for smart memory sensititive caches/pools. </summary>
            <returns> the main memory consumption </returns>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.SortFields">
            <summary>
            sorts into ascending order (on demand), reusing memory along the way
            </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.Sort``2(System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            returns a view of the given map's entries, sorted ascending by key
            </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.ToString">
            <summary>
            Returns a String representation of the index data for debugging purposes.
            </summary>
            <returns> the string representation </returns>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.Reset">
            <summary>
            Resets the <seealso cref="T:Lucene.Net.Index.Memory.MemoryIndex"/> to its initial state and recycles all internal buffers.
            </summary>
        </member>
        <member name="T:Lucene.Net.Index.Memory.MemoryIndex.Info">
            <summary>
            Index data structure for a field; Contains the tokenized term texts and
            their positions.
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.terms">
            <summary>
            Term strings and their positions for this field: <see cref="T:System.Collections.Generic.IDictionary`2"/>
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.sortedTerms">
            <summary>
            Terms sorted ascending by term text; computed on demand 
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.numTokens">
            <summary>
            Number of added tokens for this field 
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.numOverlapTokens">
            <summary>
            Number of overlapping tokens for this field 
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.boost">
            <summary>
            Boost factor for hits for this field 
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.lastPosition">
            <summary>
            the last position encountered in this field for multi field support 
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.Info.lastOffset">
            <summary>
            the last offset encountered in this field for multi field support 
            </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.Info.SortTerms">
            <summary>
            Sorts hashed terms into ascending order, reusing memory along the
            way. Note that sorting is lazily delayed until required (often it's
            not required at all). If a sorted view is required then hashing +
            sort + binary search is still faster and smaller than TreeMap usage
            (which would be an alternative and somewhat more elegant approach,
            apart from more sophisticated Tries / prefix trees).
            </summary>
        </member>
        <member name="T:Lucene.Net.Index.Memory.MemoryIndex.MemoryIndexReader">
            <summary>
            Search support for Lucene framework integration; implements all methods
            required by the Lucene IndexReader contracts.
            </summary>
        </member>
        <member name="F:Lucene.Net.Index.Memory.MemoryIndex.MemoryIndexReader.cachedNormValues">
            <summary>
            performance hack: cache norms to avoid repeated expensive calculations </summary>
        </member>
        <member name="M:Lucene.Net.Index.Memory.MemoryIndex.TermComparer.KeyComparer``2(System.Collections.Generic.KeyValuePair{``0,``1},System.Collections.Generic.KeyValuePair{``0,``1})">
            <summary>
            Sorts term entries into ascending order; also works for
            <see cref="M:System.Array.BinarySearch``1(``0[],``0,System.Collections.Generic.IComparer{``0})"/> and 
            <see cref="M:System.Array.Sort``1(``0[],System.Collections.Generic.IComparer{``0})"/>.
            </summary>
        </member>
        <member name="T:Lucene.Net.Index.Memory.MemoryIndexNormDocValues">
            
            <summary>
            @lucene.internal
            </summary>
        </member>
    </members>
</doc>
